{"version": 3, "file": "currentScopes.js", "sources": ["../../src/currentScopes.ts"], "sourcesContent": ["import { getAsyncContextStrategy } from './asyncContext';\nimport { getGlobal<PERSON>ingleton, getMainCarrier } from './carrier';\nimport type { Client } from './client';\nimport { Scope } from './scope';\nimport type { TraceContext } from './types-hoist/context';\nimport { generateSpanId } from './utils/propagationContext';\n\n/**\n * Get the currently active scope.\n */\nexport function getCurrentScope(): Scope {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getCurrentScope();\n}\n\n/**\n * Get the currently active isolation scope.\n * The isolation scope is active for the current execution context.\n */\nexport function getIsolationScope(): Scope {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getIsolationScope();\n}\n\n/**\n * Get the global scope.\n * This scope is applied to _all_ events.\n */\nexport function getGlobalScope(): Scope {\n  return getGlobalSingleton('globalScope', () => new Scope());\n}\n\n/**\n * Creates a new scope with and executes the given operation within.\n * The scope is automatically removed once the operation\n * finishes or throws.\n */\nexport function withScope<T>(callback: (scope: Scope) => T): T;\n/**\n * Set the given scope as the active scope in the callback.\n */\nexport function withScope<T>(scope: Scope | undefined, callback: (scope: Scope) => T): T;\n/**\n * Either creates a new active scope, or sets the given scope as active scope in the given callback.\n */\nexport function withScope<T>(\n  ...rest: [callback: (scope: Scope) => T] | [scope: Scope | undefined, callback: (scope: Scope) => T]\n): T {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [scope, callback] = rest;\n\n    if (!scope) {\n      return acs.withScope(callback);\n    }\n\n    return acs.withSetScope(scope, callback);\n  }\n\n  return acs.withScope(rest[0]);\n}\n\n/**\n * Attempts to fork the current isolation scope and the current scope based on the current async context strategy. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n */\nexport function withIsolationScope<T>(callback: (isolationScope: Scope) => T): T;\n/**\n * Set the provided isolation scope as active in the given callback. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n *\n * If you pass in `undefined` as a scope, it will fork a new isolation scope, the same as if no scope is passed.\n */\nexport function withIsolationScope<T>(isolationScope: Scope | undefined, callback: (isolationScope: Scope) => T): T;\n/**\n * Either creates a new active isolation scope, or sets the given isolation scope as active scope in the given callback.\n */\nexport function withIsolationScope<T>(\n  ...rest:\n    | [callback: (isolationScope: Scope) => T]\n    | [isolationScope: Scope | undefined, callback: (isolationScope: Scope) => T]\n): T {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [isolationScope, callback] = rest;\n\n    if (!isolationScope) {\n      return acs.withIsolationScope(callback);\n    }\n\n    return acs.withSetIsolationScope(isolationScope, callback);\n  }\n\n  return acs.withIsolationScope(rest[0]);\n}\n\n/**\n * Get the currently active client.\n */\nexport function getClient<C extends Client>(): C | undefined {\n  return getCurrentScope().getClient<C>();\n}\n\n/**\n * Get a trace context for the given scope.\n */\nexport function getTraceContextFromScope(scope: Scope): TraceContext {\n  const propagationContext = scope.getPropagationContext();\n\n  const { traceId, parentSpanId, propagationSpanId } = propagationContext;\n\n  const traceContext: TraceContext = {\n    trace_id: traceId,\n    span_id: propagationSpanId || generateSpanId(),\n  };\n\n  if (parentSpanId) {\n    traceContext.parent_span_id = parentSpanId;\n  }\n\n  return traceContext;\n}\n"], "names": ["carrier", "getMainCarrier", "getAsyncContextStrategy", "getGlobalSingleton", "<PERSON><PERSON>", "propagationContext", "generateSpanId"], "mappings": ";;;;;;;AAOA;AACA;AACA;AACO,SAAS,eAAe,GAAU;AACzC,EAAE,MAAMA,SAAA,GAAUC,sBAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAEC,6BAAuB,CAACF,SAAO,CAAC;AAC9C,EAAE,OAAO,GAAG,CAAC,eAAe,EAAE;AAC9B;;AAEA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,GAAU;AAC3C,EAAE,MAAMA,SAAA,GAAUC,sBAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAEC,6BAAuB,CAACF,SAAO,CAAC;AAC9C,EAAE,OAAO,GAAG,CAAC,iBAAiB,EAAE;AAChC;;AAEA;AACA;AACA;AACA;AACO,SAAS,cAAc,GAAU;AACxC,EAAE,OAAOG,0BAAkB,CAAC,aAAa,EAAE,MAAM,IAAIC,WAAK,EAAE,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACO,SAAS,SAAS;AACzB,EAAE,GAAG;AACL,EAAK;AACL,EAAE,MAAMJ,SAAA,GAAUC,sBAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAEC,6BAAuB,CAACF,SAAO,CAAC;;AAE9C;AACA,EAAE,IAAI,IAAI,CAAC,MAAO,KAAI,CAAC,EAAE;AACzB,IAAI,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAA,GAAI,IAAI;;AAElC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;AACpC;;AAEA,IAAI,OAAO,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC5C;;AAEA,EAAE,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAeA;AACA;AACA;AACO,SAAS,kBAAkB;AAClC,EAAE,GAAG;;AAGL,EAAK;AACL,EAAE,MAAMA,SAAA,GAAUC,sBAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAEC,6BAAuB,CAACF,SAAO,CAAC;;AAE9C;AACA,EAAE,IAAI,IAAI,CAAC,MAAO,KAAI,CAAC,EAAE;AACzB,IAAI,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAA,GAAI,IAAI;;AAE3C,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,GAAG,CAAC,kBAAkB,CAAC,QAAQ,CAAC;AAC7C;;AAEA,IAAI,OAAO,GAAG,CAAC,qBAAqB,CAAC,cAAc,EAAE,QAAQ,CAAC;AAC9D;;AAEA,EAAE,OAAO,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxC;;AAEA;AACA;AACA;AACO,SAAS,SAAS,GAAoC;AAC7D,EAAE,OAAO,eAAe,EAAE,CAAC,SAAS,EAAK;AACzC;;AAEA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC,KAAK,EAAuB;AACrE,EAAE,MAAMK,oBAAmB,GAAE,KAAK,CAAC,qBAAqB,EAAE;;AAE1D,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,iBAAA,EAAoB,GAAEA,oBAAkB;;AAEzE,EAAE,MAAM,YAAY,GAAiB;AACrC,IAAI,QAAQ,EAAE,OAAO;AACrB,IAAI,OAAO,EAAE,iBAAA,IAAqBC,iCAAc,EAAE;AAClD,GAAG;;AAEH,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,YAAY,CAAC,cAAe,GAAE,YAAY;AAC9C;;AAEA,EAAE,OAAO,YAAY;AACrB;;;;;;;;;;"}