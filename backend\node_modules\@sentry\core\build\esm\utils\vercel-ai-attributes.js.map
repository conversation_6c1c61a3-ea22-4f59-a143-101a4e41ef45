{"version": 3, "file": "vercel-ai-attributes.js", "sources": ["../../../src/utils/vercel-ai-attributes.ts"], "sourcesContent": ["/**\n * AI SDK Telemetry Attributes\n * Based on https://ai-sdk.dev/docs/ai-sdk-core/telemetry#collected-data\n */\n\n// =============================================================================\n// COMMON ATTRIBUTES\n// =============================================================================\n\n/**\n * Common attribute for operation name across all functions and spans\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#collected-data\n */\nexport const OPERATION_NAME_ATTRIBUTE = 'operation.name';\n\n/**\n * Common attribute for AI operation ID across all functions and spans\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#collected-data\n */\nexport const AI_OPERATION_ID_ATTRIBUTE = 'ai.operationId';\n\n// =============================================================================\n// SHARED ATTRIBUTES\n// =============================================================================\n\n/**\n * `generateText` function - `ai.generateText` span\n * `streamText` function - `ai.streamText` span\n *\n * The prompt that was used when calling the function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function\n */\nexport const AI_PROMPT_ATTRIBUTE = 'ai.prompt';\n\n/**\n * `generateObject` function - `ai.generateObject` span\n * `streamObject` function - `ai.streamObject` span\n *\n * The JSON schema version of the schema that was passed into the function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function\n */\nexport const AI_SCHEMA_ATTRIBUTE = 'ai.schema';\n\n/**\n * `generateObject` function - `ai.generateObject` span\n * `streamObject` function - `ai.streamObject` span\n *\n * The name of the schema that was passed into the function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function\n */\nexport const AI_SCHEMA_NAME_ATTRIBUTE = 'ai.schema.name';\n\n/**\n * `generateObject` function - `ai.generateObject` span\n * `streamObject` function - `ai.streamObject` span\n *\n * The description of the schema that was passed into the function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function\n */\nexport const AI_SCHEMA_DESCRIPTION_ATTRIBUTE = 'ai.schema.description';\n\n/**\n * `generateObject` function - `ai.generateObject` span\n * `streamObject` function - `ai.streamObject` span\n *\n * The object that was generated (stringified JSON)\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function\n */\nexport const AI_RESPONSE_OBJECT_ATTRIBUTE = 'ai.response.object';\n\n/**\n * `generateObject` function - `ai.generateObject` span\n * `streamObject` function - `ai.streamObject` span\n *\n * The object generation mode, e.g. `json`\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function\n */\nexport const AI_SETTINGS_MODE_ATTRIBUTE = 'ai.settings.mode';\n\n/**\n * `generateObject` function - `ai.generateObject` span\n * `streamObject` function - `ai.streamObject` span\n *\n * The output type that was used, e.g. `object` or `no-schema`\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function\n */\nexport const AI_SETTINGS_OUTPUT_ATTRIBUTE = 'ai.settings.output';\n\n/**\n * `embed` function - `ai.embed.doEmbed` span\n * `embedMany` function - `ai.embedMany` span\n *\n * The values that were passed into the function (array)\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embedmany-function\n */\nexport const AI_VALUES_ATTRIBUTE = 'ai.values';\n\n/**\n * `embed` function - `ai.embed.doEmbed` span\n * `embedMany` function - `ai.embedMany` span\n *\n * An array of JSON-stringified embeddings\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embedmany-function\n */\nexport const AI_EMBEDDINGS_ATTRIBUTE = 'ai.embeddings';\n\n// =============================================================================\n// GENERATETEXT FUNCTION - UNIQUE ATTRIBUTES\n// =============================================================================\n\n/**\n * `generateText` function - `ai.generateText` span\n *\n * The text that was generated\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_RESPONSE_TEXT_ATTRIBUTE = 'ai.response.text';\n\n/**\n * `generateText` function - `ai.generateText` span\n *\n * The tool calls that were made as part of the generation (stringified JSON)\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_RESPONSE_TOOL_CALLS_ATTRIBUTE = 'ai.response.toolCalls';\n\n/**\n * `generateText` function - `ai.generateText` span\n *\n * The reason why the generation finished\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_RESPONSE_FINISH_REASON_ATTRIBUTE = 'ai.response.finishReason';\n\n/**\n * `generateText` function - `ai.generateText` span\n *\n * The maximum number of steps that were set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_SETTINGS_MAX_STEPS_ATTRIBUTE = 'ai.settings.maxSteps';\n\n/**\n * `generateText` function - `ai.generateText.doGenerate` span\n *\n * The format of the prompt\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_PROMPT_FORMAT_ATTRIBUTE = 'ai.prompt.format';\n\n/**\n * `generateText` function - `ai.generateText.doGenerate` span\n *\n * The messages that were passed into the provider\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_PROMPT_MESSAGES_ATTRIBUTE = 'ai.prompt.messages';\n\n/**\n * `generateText` function - `ai.generateText.doGenerate` span\n *\n * Array of stringified tool definitions\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_PROMPT_TOOLS_ATTRIBUTE = 'ai.prompt.tools';\n\n/**\n * `generateText` function - `ai.generateText.doGenerate` span\n *\n * The stringified tool choice setting (JSON)\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_PROMPT_TOOL_CHOICE_ATTRIBUTE = 'ai.prompt.toolChoice';\n\n// =============================================================================\n// STREAMTEXT FUNCTION - UNIQUE ATTRIBUTES\n// =============================================================================\n\n/**\n * `streamText` function - `ai.streamText.doStream` span\n *\n * The time it took to receive the first chunk in milliseconds\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function\n */\nexport const AI_RESPONSE_MS_TO_FIRST_CHUNK_ATTRIBUTE = 'ai.response.msToFirstChunk';\n\n/**\n * `streamText` function - `ai.streamText.doStream` span\n *\n * The time it took to receive the finish part of the LLM stream in milliseconds\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function\n */\nexport const AI_RESPONSE_MS_TO_FINISH_ATTRIBUTE = 'ai.response.msToFinish';\n\n/**\n * `streamText` function - `ai.streamText.doStream` span\n *\n * The average completion tokens per second\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function\n */\nexport const AI_RESPONSE_AVG_COMPLETION_TOKENS_PER_SECOND_ATTRIBUTE = 'ai.response.avgCompletionTokensPerSecond';\n\n// =============================================================================\n// EMBED FUNCTION - UNIQUE ATTRIBUTES\n// =============================================================================\n\n/**\n * `embed` function - `ai.embed` span\n *\n * The value that was passed into the `embed` function\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function\n */\nexport const AI_VALUE_ATTRIBUTE = 'ai.value';\n\n/**\n * `embed` function - `ai.embed` span\n *\n * A JSON-stringified embedding\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function\n */\nexport const AI_EMBEDDING_ATTRIBUTE = 'ai.embedding';\n\n// =============================================================================\n// BASIC LLM SPAN INFORMATION\n// =============================================================================\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The functionId that was set through `telemetry.functionId`\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const RESOURCE_NAME_ATTRIBUTE = 'resource.name';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The id of the model\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_MODEL_ID_ATTRIBUTE = 'ai.model.id';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The provider of the model\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_MODEL_PROVIDER_ATTRIBUTE = 'ai.model.provider';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The request headers that were passed in through `headers`\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_REQUEST_HEADERS_ATTRIBUTE = 'ai.request.headers';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The maximum number of retries that were set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_SETTINGS_MAX_RETRIES_ATTRIBUTE = 'ai.settings.maxRetries';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The functionId that was set through `telemetry.functionId`\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE = 'ai.telemetry.functionId';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The metadata that was passed in through `telemetry.metadata`\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_TELEMETRY_METADATA_ATTRIBUTE = 'ai.telemetry.metadata';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The number of completion tokens that were used\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE = 'ai.usage.completionTokens';\n\n/**\n * Basic LLM span information\n * Multiple spans\n *\n * The number of prompt tokens that were used\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information\n */\nexport const AI_USAGE_PROMPT_TOKENS_ATTRIBUTE = 'ai.usage.promptTokens';\n\n// =============================================================================\n// CALL LLM SPAN INFORMATION\n// =============================================================================\n\n/**\n * Call LLM span information\n * Individual LLM call spans\n *\n * The model that was used to generate the response\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const AI_RESPONSE_MODEL_ATTRIBUTE = 'ai.response.model';\n\n/**\n * Call LLM span information\n * Individual LLM call spans\n *\n * The id of the response\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const AI_RESPONSE_ID_ATTRIBUTE = 'ai.response.id';\n\n/**\n * Call LLM span information\n * Individual LLM call spans\n *\n * The timestamp of the response\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const AI_RESPONSE_TIMESTAMP_ATTRIBUTE = 'ai.response.timestamp';\n\n// =============================================================================\n// SEMANTIC CONVENTIONS FOR GENAI OPERATIONS\n// =============================================================================\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The provider that was used\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_SYSTEM_ATTRIBUTE = 'gen_ai.system';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The model that was requested\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_MODEL_ATTRIBUTE = 'gen_ai.request.model';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The temperature that was set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_TEMPERATURE_ATTRIBUTE = 'gen_ai.request.temperature';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The maximum number of tokens that were set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_MAX_TOKENS_ATTRIBUTE = 'gen_ai.request.max_tokens';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The frequency penalty that was set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_FREQUENCY_PENALTY_ATTRIBUTE = 'gen_ai.request.frequency_penalty';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The presence penalty that was set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_PRESENCE_PENALTY_ATTRIBUTE = 'gen_ai.request.presence_penalty';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The topK parameter value that was set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_TOP_K_ATTRIBUTE = 'gen_ai.request.top_k';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The topP parameter value that was set\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_TOP_P_ATTRIBUTE = 'gen_ai.request.top_p';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The stop sequences\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_REQUEST_STOP_SEQUENCES_ATTRIBUTE = 'gen_ai.request.stop_sequences';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The finish reasons that were returned by the provider\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_RESPONSE_FINISH_REASONS_ATTRIBUTE = 'gen_ai.response.finish_reasons';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The model that was used to generate the response\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_RESPONSE_MODEL_ATTRIBUTE = 'gen_ai.response.model';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The id of the response\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_RESPONSE_ID_ATTRIBUTE = 'gen_ai.response.id';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The number of prompt tokens that were used\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE = 'gen_ai.usage.input_tokens';\n\n/**\n * Semantic Conventions for GenAI operations\n * Individual LLM call spans\n *\n * The number of completion tokens that were used\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information\n */\nexport const GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE = 'gen_ai.usage.output_tokens';\n\n// =============================================================================\n// BASIC EMBEDDING SPAN INFORMATION\n// =============================================================================\n\n/**\n * Basic embedding span information\n * Embedding spans\n *\n * The number of tokens that were used\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-embedding-span-information\n */\nexport const AI_USAGE_TOKENS_ATTRIBUTE = 'ai.usage.tokens';\n\n// =============================================================================\n// TOOL CALL SPANS\n// =============================================================================\n\n/**\n * Tool call spans\n * `ai.toolCall` span\n *\n * The name of the tool\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans\n */\nexport const AI_TOOL_CALL_NAME_ATTRIBUTE = 'ai.toolCall.name';\n\n/**\n * Tool call spans\n * `ai.toolCall` span\n *\n * The id of the tool call\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans\n */\nexport const AI_TOOL_CALL_ID_ATTRIBUTE = 'ai.toolCall.id';\n\n/**\n * Tool call spans\n * `ai.toolCall` span\n *\n * The parameters of the tool call\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans\n */\nexport const AI_TOOL_CALL_ARGS_ATTRIBUTE = 'ai.toolCall.args';\n\n/**\n * Tool call spans\n * `ai.toolCall` span\n *\n * The result of the tool call\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans\n */\nexport const AI_TOOL_CALL_RESULT_ATTRIBUTE = 'ai.toolCall.result';\n\n// =============================================================================\n// SPAN ATTRIBUTE OBJECTS\n// =============================================================================\n\n/**\n * Attributes collected for `ai.generateText` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_GENERATE_TEXT_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_PROMPT: AI_PROMPT_ATTRIBUTE,\n  AI_RESPONSE_TEXT: AI_RESPONSE_TEXT_ATTRIBUTE,\n  AI_RESPONSE_TOOL_CALLS: AI_RESPONSE_TOOL_CALLS_ATTRIBUTE,\n  AI_RESPONSE_FINISH_REASON: AI_RESPONSE_FINISH_REASON_ATTRIBUTE,\n  AI_SETTINGS_MAX_STEPS: AI_SETTINGS_MAX_STEPS_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  AI_USAGE_COMPLETION_TOKENS: AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE,\n  AI_USAGE_PROMPT_TOKENS: AI_USAGE_PROMPT_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.generateText.doGenerate` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function\n */\nexport const AI_GENERATE_TEXT_DO_GENERATE_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_PROMPT_FORMAT: AI_PROMPT_FORMAT_ATTRIBUTE,\n  AI_PROMPT_MESSAGES: AI_PROMPT_MESSAGES_ATTRIBUTE,\n  AI_PROMPT_TOOLS: AI_PROMPT_TOOLS_ATTRIBUTE,\n  AI_PROMPT_TOOL_CHOICE: AI_PROMPT_TOOL_CHOICE_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  AI_USAGE_COMPLETION_TOKENS: AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE,\n  AI_USAGE_PROMPT_TOKENS: AI_USAGE_PROMPT_TOKENS_ATTRIBUTE,\n  // Call LLM span information\n  AI_RESPONSE_MODEL: AI_RESPONSE_MODEL_ATTRIBUTE,\n  AI_RESPONSE_ID: AI_RESPONSE_ID_ATTRIBUTE,\n  AI_RESPONSE_TIMESTAMP: AI_RESPONSE_TIMESTAMP_ATTRIBUTE,\n  // Semantic Conventions for GenAI operations\n  GEN_AI_SYSTEM: GEN_AI_SYSTEM_ATTRIBUTE,\n  GEN_AI_REQUEST_MODEL: GEN_AI_REQUEST_MODEL_ATTRIBUTE,\n  GEN_AI_REQUEST_TEMPERATURE: GEN_AI_REQUEST_TEMPERATURE_ATTRIBUTE,\n  GEN_AI_REQUEST_MAX_TOKENS: GEN_AI_REQUEST_MAX_TOKENS_ATTRIBUTE,\n  GEN_AI_REQUEST_FREQUENCY_PENALTY: GEN_AI_REQUEST_FREQUENCY_PENALTY_ATTRIBUTE,\n  GEN_AI_REQUEST_PRESENCE_PENALTY: GEN_AI_REQUEST_PRESENCE_PENALTY_ATTRIBUTE,\n  GEN_AI_REQUEST_TOP_K: GEN_AI_REQUEST_TOP_K_ATTRIBUTE,\n  GEN_AI_REQUEST_TOP_P: GEN_AI_REQUEST_TOP_P_ATTRIBUTE,\n  GEN_AI_REQUEST_STOP_SEQUENCES: GEN_AI_REQUEST_STOP_SEQUENCES_ATTRIBUTE,\n  GEN_AI_RESPONSE_FINISH_REASONS: GEN_AI_RESPONSE_FINISH_REASONS_ATTRIBUTE,\n  GEN_AI_RESPONSE_MODEL: GEN_AI_RESPONSE_MODEL_ATTRIBUTE,\n  GEN_AI_RESPONSE_ID: GEN_AI_RESPONSE_ID_ATTRIBUTE,\n  GEN_AI_USAGE_INPUT_TOKENS: GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE,\n  GEN_AI_USAGE_OUTPUT_TOKENS: GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.streamText` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function\n */\nexport const AI_STREAM_TEXT_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_PROMPT: AI_PROMPT_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  AI_USAGE_COMPLETION_TOKENS: AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE,\n  AI_USAGE_PROMPT_TOKENS: AI_USAGE_PROMPT_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.streamText.doStream` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function\n */\nexport const AI_STREAM_TEXT_DO_STREAM_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_RESPONSE_MS_TO_FIRST_CHUNK: AI_RESPONSE_MS_TO_FIRST_CHUNK_ATTRIBUTE,\n  AI_RESPONSE_MS_TO_FINISH: AI_RESPONSE_MS_TO_FINISH_ATTRIBUTE,\n  AI_RESPONSE_AVG_COMPLETION_TOKENS_PER_SECOND: AI_RESPONSE_AVG_COMPLETION_TOKENS_PER_SECOND_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  AI_USAGE_COMPLETION_TOKENS: AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE,\n  AI_USAGE_PROMPT_TOKENS: AI_USAGE_PROMPT_TOKENS_ATTRIBUTE,\n  // Call LLM span information\n  AI_RESPONSE_MODEL: AI_RESPONSE_MODEL_ATTRIBUTE,\n  AI_RESPONSE_ID: AI_RESPONSE_ID_ATTRIBUTE,\n  AI_RESPONSE_TIMESTAMP: AI_RESPONSE_TIMESTAMP_ATTRIBUTE,\n  // Semantic Conventions for GenAI operations\n  GEN_AI_SYSTEM: GEN_AI_SYSTEM_ATTRIBUTE,\n  GEN_AI_REQUEST_MODEL: GEN_AI_REQUEST_MODEL_ATTRIBUTE,\n  GEN_AI_REQUEST_TEMPERATURE: GEN_AI_REQUEST_TEMPERATURE_ATTRIBUTE,\n  GEN_AI_REQUEST_MAX_TOKENS: GEN_AI_REQUEST_MAX_TOKENS_ATTRIBUTE,\n  GEN_AI_REQUEST_FREQUENCY_PENALTY: GEN_AI_REQUEST_FREQUENCY_PENALTY_ATTRIBUTE,\n  GEN_AI_REQUEST_PRESENCE_PENALTY: GEN_AI_REQUEST_PRESENCE_PENALTY_ATTRIBUTE,\n  GEN_AI_REQUEST_TOP_K: GEN_AI_REQUEST_TOP_K_ATTRIBUTE,\n  GEN_AI_REQUEST_TOP_P: GEN_AI_REQUEST_TOP_P_ATTRIBUTE,\n  GEN_AI_REQUEST_STOP_SEQUENCES: GEN_AI_REQUEST_STOP_SEQUENCES_ATTRIBUTE,\n  GEN_AI_RESPONSE_FINISH_REASONS: GEN_AI_RESPONSE_FINISH_REASONS_ATTRIBUTE,\n  GEN_AI_RESPONSE_MODEL: GEN_AI_RESPONSE_MODEL_ATTRIBUTE,\n  GEN_AI_RESPONSE_ID: GEN_AI_RESPONSE_ID_ATTRIBUTE,\n  GEN_AI_USAGE_INPUT_TOKENS: GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE,\n  GEN_AI_USAGE_OUTPUT_TOKENS: GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.generateObject` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function\n */\nexport const AI_GENERATE_OBJECT_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_SCHEMA: AI_SCHEMA_ATTRIBUTE,\n  AI_SCHEMA_NAME: AI_SCHEMA_NAME_ATTRIBUTE,\n  AI_SCHEMA_DESCRIPTION: AI_SCHEMA_DESCRIPTION_ATTRIBUTE,\n  AI_RESPONSE_OBJECT: AI_RESPONSE_OBJECT_ATTRIBUTE,\n  AI_SETTINGS_MODE: AI_SETTINGS_MODE_ATTRIBUTE,\n  AI_SETTINGS_OUTPUT: AI_SETTINGS_OUTPUT_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  AI_USAGE_COMPLETION_TOKENS: AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE,\n  AI_USAGE_PROMPT_TOKENS: AI_USAGE_PROMPT_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.streamObject` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function\n */\nexport const AI_STREAM_OBJECT_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_SCHEMA: AI_SCHEMA_ATTRIBUTE,\n  AI_SCHEMA_NAME: AI_SCHEMA_NAME_ATTRIBUTE,\n  AI_SCHEMA_DESCRIPTION: AI_SCHEMA_DESCRIPTION_ATTRIBUTE,\n  AI_RESPONSE_OBJECT: AI_RESPONSE_OBJECT_ATTRIBUTE,\n  AI_SETTINGS_MODE: AI_SETTINGS_MODE_ATTRIBUTE,\n  AI_SETTINGS_OUTPUT: AI_SETTINGS_OUTPUT_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  AI_USAGE_COMPLETION_TOKENS: AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE,\n  AI_USAGE_PROMPT_TOKENS: AI_USAGE_PROMPT_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.embed` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function\n */\nexport const AI_EMBED_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_VALUE: AI_VALUE_ATTRIBUTE,\n  AI_EMBEDDING: AI_EMBEDDING_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  // Basic embedding span information\n  AI_USAGE_TOKENS: AI_USAGE_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.embed.doEmbed` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function\n */\nexport const AI_EMBED_DO_EMBED_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_VALUES: AI_VALUES_ATTRIBUTE,\n  AI_EMBEDDINGS: AI_EMBEDDINGS_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  // Basic embedding span information\n  AI_USAGE_TOKENS: AI_USAGE_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.embedMany` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embedmany-function\n */\nexport const AI_EMBED_MANY_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_VALUES: AI_VALUES_ATTRIBUTE,\n  AI_EMBEDDINGS: AI_EMBEDDINGS_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n  // Basic embedding span information\n  AI_USAGE_TOKENS: AI_USAGE_TOKENS_ATTRIBUTE,\n} as const;\n\n/**\n * Attributes collected for `ai.toolCall` span\n * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans\n */\nexport const AI_TOOL_CALL_SPAN_ATTRIBUTES = {\n  OPERATION_NAME: OPERATION_NAME_ATTRIBUTE,\n  AI_OPERATION_ID: AI_OPERATION_ID_ATTRIBUTE,\n  AI_TOOL_CALL_NAME: AI_TOOL_CALL_NAME_ATTRIBUTE,\n  AI_TOOL_CALL_ID: AI_TOOL_CALL_ID_ATTRIBUTE,\n  AI_TOOL_CALL_ARGS: AI_TOOL_CALL_ARGS_ATTRIBUTE,\n  AI_TOOL_CALL_RESULT: AI_TOOL_CALL_RESULT_ATTRIBUTE,\n  // Basic LLM span information\n  RESOURCE_NAME: RESOURCE_NAME_ATTRIBUTE,\n  AI_MODEL_ID: AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER: AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_REQUEST_HEADERS: AI_REQUEST_HEADERS_ATTRIBUTE,\n  AI_SETTINGS_MAX_RETRIES: AI_SETTINGS_MAX_RETRIES_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID: AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TELEMETRY_METADATA: AI_TELEMETRY_METADATA_ATTRIBUTE,\n} as const;\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;;;AAkBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,mBAAoB,GAAE;;AAkFnC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,0BAA2B,GAAE;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gCAAiC,GAAE;;AA0BhD;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,4BAA6B,GAAE;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,yBAA0B,GAAE;;AAuEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,qBAAsB,GAAE;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,2BAA4B,GAAE;;AAoB3C;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,kCAAmC,GAAE;;AAWlD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,oCAAqC,GAAE;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gCAAiC,GAAE;;AA+HhD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,+BAAgC,GAAE;;AAW/C;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,mCAAoC,GAAE;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,oCAAqC,GAAE;;AAepD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,2BAA4B,GAAE;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,yBAA0B,GAAE;;;;"}