import 'package:budget_tracker/models/transaction_model.dart';
import 'package:flutter/material.dart';

class MockTransactionProvider with ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  List<Transaction> _transactions = [];
  final String _testUserId = 'test_user_123';
  final String _testCategoryId = 'test_category_456';

  bool get isLoading => _isLoading;
  String? get error => _error;
  List<Transaction> get transactions => _transactions;

  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void setTransactions(List<Transaction> transactions) {
    _transactions = transactions;
    notifyListeners();
  }

  Future<void> fetchTransactions() async {
    setLoading(true);
    setError(null);
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 100));

      // Return mock data with all required fields
      _transactions = [
        Transaction(
          id: '1',
          userId: _testUserId,
          amount: 100,
          type: 'expense',
          categoryId: _testCategoryId,
          categoryName: 'Food',
          categoryColorValue: Colors.red.value,
          date: DateTime.now(),
          description: 'Lunch',
          isRecurring: false,
        ),
        Transaction(
          id: '2',
          userId: _testUserId,
          amount: 2000,
          type: 'income',
          categoryId: _testCategoryId,
          categoryName: 'Salary',
          categoryColorValue: Colors.green.value,
          date: DateTime.now(),
          description: 'Monthly salary',
          isRecurring: true,
          recurringFrequency: 'monthly',
          recurringEndDate: DateTime.now().add(const Duration(days: 365)),
        ),
      ];
    } catch (e) {
      setError('Failed to fetch transactions: $e');
    } finally {
      setLoading(false);
    }
  }
}
