const mongoose = require('mongoose');

const BudgetSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: true,
    },
    amount: {
      type: Number,
      required: [true, 'Please add a budget amount'],
    },
    month: {
      type: Number,
      required: [true, 'Please specify month (1-12)'],
      min: 1,
      max: 12,
    },
    year: {
      type: Number,
      required: [true, 'Please specify year'],
    },
    notes: {
      type: String,
      maxlength: [200, 'Notes cannot be more than 200 characters'],
    },
  },
  {
    timestamps: true,
  }
);

// Ensure unique budgets per category, month, and year for each user
BudgetSchema.index({ user: 1, category: 1, month: 1, year: 1 }, { unique: true });

// Method to calculate budget usage
BudgetSchema.methods.calculateUsage = async function () {
  const Transaction = mongoose.model('Transaction');
  
  // Get the first and last day of the month
  const startDate = new Date(this.year, this.month - 1, 1);
  const endDate = new Date(this.year, this.month, 0);
  
  // Get the sum of expenses for this category in the specified month
  const result = await Transaction.aggregate([
    {
      $match: {
        user: this.user,
        category: this.category,
        type: 'expense',
        date: { $gte: startDate, $lte: endDate },
      },
    },
    {
      $group: {
        _id: null,
        used: { $sum: '$amount' },
      },
    },
  ]);
  
  const used = result.length > 0 ? result[0].used : 0;
  const remaining = this.amount - used;
  const percentage = (used / this.amount) * 100;
  
  return {
    total: this.amount,
    used,
    remaining,
    percentage,
  };
};

module.exports = mongoose.model('Budget', BudgetSchema);
