{"version": 3, "file": "functiontostring.js", "sources": ["../../../src/integrations/functiontostring.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { getClient } from '../currentScopes';\nimport { defineIntegration } from '../integration';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport type { WrappedFunction } from '../types-hoist/wrappedfunction';\nimport { getOriginalFunction } from '../utils/object';\n\nlet originalFunctionToString: () => void;\n\nconst INTEGRATION_NAME = 'FunctionToString';\n\nconst SETUP_CLIENTS = new WeakMap<Client, boolean>();\n\nconst _functionToStringIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      originalFunctionToString = Function.prototype.toString;\n\n      // intrinsics (like Function.prototype) might be immutable in some environments\n      // e.g. Node with --frozen-intrinsics, XS (an embedded JavaScript engine) or SES (a JavaScript proposal)\n      try {\n        Function.prototype.toString = function (this: WrappedFunction, ...args: unknown[]): string {\n          const originalFunction = getOriginalFunction(this);\n          const context =\n            SETUP_CLIENTS.has(getClient() as Client) && originalFunction !== undefined ? originalFunction : this;\n          return originalFunctionToString.apply(context, args);\n        };\n      } catch {\n        // ignore errors here, just don't patch this\n      }\n    },\n    setup(client) {\n      SETUP_CLIENTS.set(client, true);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Patch toString calls to return proper name for wrapped functions.\n *\n * ```js\n * Sentry.init({\n *   integrations: [\n *     functionToStringIntegration(),\n *   ],\n * });\n * ```\n */\nexport const functionToStringIntegration = defineIntegration(_functionToStringIntegration);\n"], "names": ["getOriginalFunction", "getClient", "defineIntegration"], "mappings": ";;;;;;AAOA,IAAI,wBAAwB;;AAE5B,MAAM,gBAAA,GAAmB,kBAAkB;;AAE3C,MAAM,aAAc,GAAE,IAAI,OAAO,EAAmB;;AAEpD,MAAM,4BAA6B,IAAG,MAAM;AAC5C,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,SAAS,GAAG;AAChB;AACA,MAAM,2BAA2B,QAAQ,CAAC,SAAS,CAAC,QAAQ;;AAE5D;AACA;AACA,MAAM,IAAI;AACV,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAS,GAAE,WAAiC,GAAG,IAAI,EAAqB;AACnG,UAAU,MAAM,gBAAiB,GAAEA,0BAAmB,CAAC,IAAI,CAAC;AAC5D,UAAU,MAAM,OAAQ;AACxB,YAAY,aAAa,CAAC,GAAG,CAACC,uBAAS,EAAC,EAAa,IAAG,qBAAqB,SAAA,GAAY,gBAAA,GAAmB,IAAI;AAChH,UAAU,OAAO,wBAAwB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;AAC9D,SAAS;AACT,QAAQ,MAAM;AACd;AACA;AACA,KAAK;AACL,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;AACrC,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,2BAA4B,GAAEC,6BAAiB,CAAC,4BAA4B;;;;"}