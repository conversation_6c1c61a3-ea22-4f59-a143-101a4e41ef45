const express = require('express');
const { 
  createCategory, 
  getCategories, 
  getCategory,
  updateCategory,
  deleteCategory,
  getDefaultCategories
} = require('../controllers/category.controller');
const { protect } = require('../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .get(getCategories)
  .post(createCategory);

router.route('/defaults')
  .get(getDefaultCategories);
  
router.route('/:id')
  .get(getCategory)
  .put(updateCategory)
  .delete(deleteCategory);

module.exports = router;
