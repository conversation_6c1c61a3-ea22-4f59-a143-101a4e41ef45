{"version": 3, "file": "clientreport.js", "sources": ["../../../src/utils/clientreport.ts"], "sourcesContent": ["import type { ClientReport } from '../types-hoist/clientreport';\nimport type { ClientReportEnvelope, ClientReportItem } from '../types-hoist/envelope';\nimport { createEnvelope } from './envelope';\nimport { dateTimestampInSeconds } from './time';\n\n/**\n * Creates client report envelope\n * @param discarded_events An array of discard events\n * @param dsn A DSN that can be set on the header. Optional.\n */\nexport function createClientReportEnvelope(\n  discarded_events: ClientReport['discarded_events'],\n  dsn?: string,\n  timestamp?: number,\n): ClientReportEnvelope {\n  const clientReportItem: ClientReportItem = [\n    { type: 'client_report' },\n    {\n      timestamp: timestamp || dateTimestampInSeconds(),\n      discarded_events,\n    },\n  ];\n  return createEnvelope<ClientReportEnvelope>(dsn ? { dsn } : {}, [clientReportItem]);\n}\n"], "names": ["dateTimestampInSeconds", "createEnvelope"], "mappings": ";;;;;AAKA;AACA;AACA;AACA;AACA;AACO,SAAS,0BAA0B;AAC1C,EAAE,gBAAgB;AAClB,EAAE,GAAG;AACL,EAAE,SAAS;AACX,EAAwB;AACxB,EAAE,MAAM,gBAAgB,GAAqB;AAC7C,IAAI,EAAE,IAAI,EAAE,eAAA,EAAiB;AAC7B,IAAI;AACJ,MAAM,SAAS,EAAE,SAAA,IAAaA,2BAAsB,EAAE;AACtD,MAAM,gBAAgB;AACtB,KAAK;AACL,GAAG;AACH,EAAE,OAAOC,uBAAc,CAAuB,GAAA,GAAM,EAAE,GAAA,EAAM,GAAE,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC;AACrF;;;;"}