{"version": 3, "file": "mcp-server.js", "sources": ["../../src/mcp-server.ts"], "sourcesContent": ["import { DEBUG_BUILD } from './debug-build';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from './semanticAttributes';\nimport { startSpan, withActiveSpan } from './tracing';\nimport type { Span } from './types-hoist/span';\nimport { logger } from './utils/logger';\nimport { getActiveSpan } from './utils/spanUtils';\n\ninterface MCPTransport {\n  // The first argument is a JSON RPC message\n  onmessage?: (...args: unknown[]) => void;\n  onclose?: (...args: unknown[]) => void;\n  sessionId?: string;\n}\n\ninterface MCPServerInstance {\n  // The first arg is always a name, the last arg should always be a callback function (ie a handler).\n  // TODO: We could also make use of the resource uri argument somehow.\n  resource: (name: string, ...args: unknown[]) => void;\n  // The first arg is always a name, the last arg should always be a callback function (ie a handler).\n  tool: (name: string, ...args: unknown[]) => void;\n  // The first arg is always a name, the last arg should always be a callback function (ie a handler).\n  prompt: (name: string, ...args: unknown[]) => void;\n  connect(transport: MCPTransport): Promise<void>;\n}\n\nconst wrappedMcpServerInstances = new WeakSet();\n\n/**\n * Wraps a MCP Server instance from the `@modelcontextprotocol/sdk` package with Sentry instrumentation.\n *\n * Compatible with versions `^1.9.0` of the `@modelcontextprotocol/sdk` package.\n */\n// We are exposing this API for non-node runtimes that cannot rely on auto-instrumentation.\nexport function wrapMcpServerWithSentry<S extends object>(mcpServerInstance: S): S {\n  if (wrappedMcpServerInstances.has(mcpServerInstance)) {\n    return mcpServerInstance;\n  }\n\n  if (!isMcpServerInstance(mcpServerInstance)) {\n    DEBUG_BUILD && logger.warn('Did not patch MCP server. Interface is incompatible.');\n    return mcpServerInstance;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  mcpServerInstance.connect = new Proxy(mcpServerInstance.connect, {\n    apply(target, thisArg, argArray) {\n      const [transport, ...restArgs] = argArray as [MCPTransport, ...unknown[]];\n\n      if (!transport.onclose) {\n        transport.onclose = () => {\n          if (transport.sessionId) {\n            handleTransportOnClose(transport.sessionId);\n          }\n        };\n      }\n\n      if (!transport.onmessage) {\n        transport.onmessage = jsonRpcMessage => {\n          if (transport.sessionId && isJsonRPCMessageWithRequestId(jsonRpcMessage)) {\n            handleTransportOnMessage(transport.sessionId, jsonRpcMessage.id);\n          }\n        };\n      }\n\n      const patchedTransport = new Proxy(transport, {\n        set(target, key, value) {\n          if (key === 'onmessage') {\n            target[key] = new Proxy(value, {\n              apply(onMessageTarget, onMessageThisArg, onMessageArgArray) {\n                const [jsonRpcMessage] = onMessageArgArray;\n                if (transport.sessionId && isJsonRPCMessageWithRequestId(jsonRpcMessage)) {\n                  handleTransportOnMessage(transport.sessionId, jsonRpcMessage.id);\n                }\n                return Reflect.apply(onMessageTarget, onMessageThisArg, onMessageArgArray);\n              },\n            });\n          } else if (key === 'onclose') {\n            target[key] = new Proxy(value, {\n              apply(onCloseTarget, onCloseThisArg, onCloseArgArray) {\n                if (transport.sessionId) {\n                  handleTransportOnClose(transport.sessionId);\n                }\n                return Reflect.apply(onCloseTarget, onCloseThisArg, onCloseArgArray);\n              },\n            });\n          } else {\n            target[key as keyof MCPTransport] = value;\n          }\n          return true;\n        },\n      });\n\n      return Reflect.apply(target, thisArg, [patchedTransport, ...restArgs]);\n    },\n  });\n\n  mcpServerInstance.resource = new Proxy(mcpServerInstance.resource, {\n    apply(target, thisArg, argArray) {\n      const resourceName: unknown = argArray[0];\n      const resourceHandler: unknown = argArray[argArray.length - 1];\n\n      if (typeof resourceName !== 'string' || typeof resourceHandler !== 'function') {\n        return target.apply(thisArg, argArray);\n      }\n\n      const wrappedResourceHandler = new Proxy(resourceHandler, {\n        apply(resourceHandlerTarget, resourceHandlerThisArg, resourceHandlerArgArray) {\n          const extraHandlerDataWithRequestId = resourceHandlerArgArray.find(isExtraHandlerDataWithRequestId);\n          return associateContextWithRequestSpan(extraHandlerDataWithRequestId, () => {\n            return startSpan(\n              {\n                name: `mcp-server/resource:${resourceName}`,\n                forceTransaction: true,\n                attributes: {\n                  [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'auto.function.mcp-server',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.function.mcp-server',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'route',\n                  'mcp_server.resource': resourceName,\n                },\n              },\n              () => resourceHandlerTarget.apply(resourceHandlerThisArg, resourceHandlerArgArray),\n            );\n          });\n        },\n      });\n\n      return Reflect.apply(target, thisArg, [...argArray.slice(0, -1), wrappedResourceHandler]);\n    },\n  });\n\n  mcpServerInstance.tool = new Proxy(mcpServerInstance.tool, {\n    apply(target, thisArg, argArray) {\n      const toolName: unknown = argArray[0];\n      const toolHandler: unknown = argArray[argArray.length - 1];\n\n      if (typeof toolName !== 'string' || typeof toolHandler !== 'function') {\n        return target.apply(thisArg, argArray);\n      }\n\n      const wrappedToolHandler = new Proxy(toolHandler, {\n        apply(toolHandlerTarget, toolHandlerThisArg, toolHandlerArgArray) {\n          const extraHandlerDataWithRequestId = toolHandlerArgArray.find(isExtraHandlerDataWithRequestId);\n          return associateContextWithRequestSpan(extraHandlerDataWithRequestId, () => {\n            return startSpan(\n              {\n                name: `mcp-server/tool:${toolName}`,\n                forceTransaction: true,\n                attributes: {\n                  [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'auto.function.mcp-server',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.function.mcp-server',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'route',\n                  'mcp_server.tool': toolName,\n                },\n              },\n              () => toolHandlerTarget.apply(toolHandlerThisArg, toolHandlerArgArray),\n            );\n          });\n        },\n      });\n\n      return Reflect.apply(target, thisArg, [...argArray.slice(0, -1), wrappedToolHandler]);\n    },\n  });\n\n  mcpServerInstance.prompt = new Proxy(mcpServerInstance.prompt, {\n    apply(target, thisArg, argArray) {\n      const promptName: unknown = argArray[0];\n      const promptHandler: unknown = argArray[argArray.length - 1];\n\n      if (typeof promptName !== 'string' || typeof promptHandler !== 'function') {\n        return target.apply(thisArg, argArray);\n      }\n\n      const wrappedPromptHandler = new Proxy(promptHandler, {\n        apply(promptHandlerTarget, promptHandlerThisArg, promptHandlerArgArray) {\n          const extraHandlerDataWithRequestId = promptHandlerArgArray.find(isExtraHandlerDataWithRequestId);\n          return associateContextWithRequestSpan(extraHandlerDataWithRequestId, () => {\n            return startSpan(\n              {\n                name: `mcp-server/prompt:${promptName}`,\n                forceTransaction: true,\n                attributes: {\n                  [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'auto.function.mcp-server',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.function.mcp-server',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'route',\n                  'mcp_server.prompt': promptName,\n                },\n              },\n              () => promptHandlerTarget.apply(promptHandlerThisArg, promptHandlerArgArray),\n            );\n          });\n        },\n      });\n\n      return Reflect.apply(target, thisArg, [...argArray.slice(0, -1), wrappedPromptHandler]);\n    },\n  });\n\n  wrappedMcpServerInstances.add(mcpServerInstance);\n\n  return mcpServerInstance as S;\n}\n\nfunction isMcpServerInstance(mcpServerInstance: unknown): mcpServerInstance is MCPServerInstance {\n  return (\n    typeof mcpServerInstance === 'object' &&\n    mcpServerInstance !== null &&\n    'resource' in mcpServerInstance &&\n    typeof mcpServerInstance.resource === 'function' &&\n    'tool' in mcpServerInstance &&\n    typeof mcpServerInstance.tool === 'function' &&\n    'prompt' in mcpServerInstance &&\n    typeof mcpServerInstance.prompt === 'function' &&\n    'connect' in mcpServerInstance &&\n    typeof mcpServerInstance.connect === 'function'\n  );\n}\n\nfunction isJsonRPCMessageWithRequestId(target: unknown): target is { id: RequestId } {\n  return (\n    typeof target === 'object' &&\n    target !== null &&\n    'id' in target &&\n    (typeof target.id === 'number' || typeof target.id === 'string')\n  );\n}\n\ninterface ExtraHandlerDataWithRequestId {\n  sessionId: SessionId;\n  requestId: RequestId;\n}\n\n// Note that not all versions of the MCP library have `requestId` as a field on the extra data.\nfunction isExtraHandlerDataWithRequestId(target: unknown): target is ExtraHandlerDataWithRequestId {\n  return (\n    typeof target === 'object' &&\n    target !== null &&\n    'sessionId' in target &&\n    typeof target.sessionId === 'string' &&\n    'requestId' in target &&\n    (typeof target.requestId === 'number' || typeof target.requestId === 'string')\n  );\n}\n\ntype SessionId = string;\ntype RequestId = string | number;\n\nconst sessionAndRequestToRequestParentSpanMap = new Map<SessionId, Map<RequestId, Span>>();\n\nfunction handleTransportOnClose(sessionId: SessionId): void {\n  sessionAndRequestToRequestParentSpanMap.delete(sessionId);\n}\n\nfunction handleTransportOnMessage(sessionId: SessionId, requestId: RequestId): void {\n  const activeSpan = getActiveSpan();\n  if (activeSpan) {\n    const requestIdToSpanMap = sessionAndRequestToRequestParentSpanMap.get(sessionId) ?? new Map();\n    requestIdToSpanMap.set(requestId, activeSpan);\n    sessionAndRequestToRequestParentSpanMap.set(sessionId, requestIdToSpanMap);\n  }\n}\n\nfunction associateContextWithRequestSpan<T>(\n  extraHandlerData: ExtraHandlerDataWithRequestId | undefined,\n  cb: () => T,\n): T {\n  if (extraHandlerData) {\n    const { sessionId, requestId } = extraHandlerData;\n    const requestIdSpanMap = sessionAndRequestToRequestParentSpanMap.get(sessionId);\n\n    if (!requestIdSpanMap) {\n      return cb();\n    }\n\n    const span = requestIdSpanMap.get(requestId);\n    if (!span) {\n      return cb();\n    }\n\n    // remove the span from the map so it can be garbage collected\n    requestIdSpanMap.delete(requestId);\n    return withActiveSpan(span, () => {\n      return cb();\n    });\n  }\n\n  return cb();\n}\n"], "names": [], "mappings": ";;;;;;;;AA6BA,MAAM,yBAA0B,GAAE,IAAI,OAAO,EAAE;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAmB,iBAAiB,EAAQ;AACnF,EAAE,IAAI,yBAAyB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;AACxD,IAAI,OAAO,iBAAiB;AAC5B;;AAEA,EAAE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;AAC/C,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC;AACtF,IAAI,OAAO,iBAAiB;AAC5B;;AAEA;AACA,EAAE,iBAAiB,CAAC,OAAA,GAAU,IAAI,KAAK,CAAC,iBAAiB,CAAC,OAAO,EAAE;AACnE,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;AACrC,MAAM,MAAM,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAA,GAAI,QAAS;;AAEhD,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC9B,QAAQ,SAAS,CAAC,OAAA,GAAU,MAAM;AAClC,UAAU,IAAI,SAAS,CAAC,SAAS,EAAE;AACnC,YAAY,sBAAsB,CAAC,SAAS,CAAC,SAAS,CAAC;AACvD;AACA,SAAS;AACT;;AAEA,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAChC,QAAQ,SAAS,CAAC,SAAU,GAAE,kBAAkB;AAChD,UAAU,IAAI,SAAS,CAAC,SAAA,IAAa,6BAA6B,CAAC,cAAc,CAAC,EAAE;AACpF,YAAY,wBAAwB,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,CAAC,EAAE,CAAC;AAC5E;AACA,SAAS;AACT;;AAEA,MAAM,MAAM,gBAAiB,GAAE,IAAI,KAAK,CAAC,SAAS,EAAE;AACpD,QAAQ,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;AAChC,UAAU,IAAI,GAAI,KAAI,WAAW,EAAE;AACnC,YAAY,MAAM,CAAC,GAAG,CAAA,GAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AAC3C,cAAc,KAAK,CAAC,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE;AAC1E,gBAAgB,MAAM,CAAC,cAAc,CAAA,GAAI,iBAAiB;AAC1D,gBAAgB,IAAI,SAAS,CAAC,SAAA,IAAa,6BAA6B,CAAC,cAAc,CAAC,EAAE;AAC1F,kBAAkB,wBAAwB,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,CAAC,EAAE,CAAC;AAClF;AACA,gBAAgB,OAAO,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,CAAC;AAC1F,eAAe;AACf,aAAa,CAAC;AACd,iBAAiB,IAAI,GAAI,KAAI,SAAS,EAAE;AACxC,YAAY,MAAM,CAAC,GAAG,CAAA,GAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AAC3C,cAAc,KAAK,CAAC,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE;AACpE,gBAAgB,IAAI,SAAS,CAAC,SAAS,EAAE;AACzC,kBAAkB,sBAAsB,CAAC,SAAS,CAAC,SAAS,CAAC;AAC7D;AACA,gBAAgB,OAAO,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,cAAc,EAAE,eAAe,CAAC;AACpF,eAAe;AACf,aAAa,CAAC;AACd,iBAAiB;AACjB,YAAY,MAAM,CAAC,GAAI,EAAA,GAAyB,KAAK;AACrD;AACA,UAAU,OAAO,IAAI;AACrB,SAAS;AACT,OAAO,CAAC;;AAER,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,gBAAgB,EAAE,GAAG,QAAQ,CAAC,CAAC;AAC5E,KAAK;AACL,GAAG,CAAC;;AAEJ,EAAE,iBAAiB,CAAC,QAAA,GAAW,IAAI,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE;AACrE,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;AACrC,MAAM,MAAM,YAAY,GAAY,QAAQ,CAAC,CAAC,CAAC;AAC/C,MAAM,MAAM,eAAe,GAAY,QAAQ,CAAC,QAAQ,CAAC,MAAA,GAAS,CAAC,CAAC;;AAEpE,MAAM,IAAI,OAAO,YAAa,KAAI,QAAS,IAAG,OAAO,eAAA,KAAoB,UAAU,EAAE;AACrF,QAAQ,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC9C;;AAEA,MAAM,MAAM,sBAAuB,GAAE,IAAI,KAAK,CAAC,eAAe,EAAE;AAChE,QAAQ,KAAK,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;AACtF,UAAU,MAAM,gCAAgC,uBAAuB,CAAC,IAAI,CAAC,+BAA+B,CAAC;AAC7G,UAAU,OAAO,+BAA+B,CAAC,6BAA6B,EAAE,MAAM;AACtF,YAAY,OAAO,SAAS;AAC5B,cAAc;AACd,gBAAgB,IAAI,EAAE,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAA;AACA,gBAAA,gBAAA,EAAA,IAAA;AACA,gBAAA,UAAA,EAAA;AACA,kBAAA,CAAA,4BAAA,GAAA,0BAAA;AACA,kBAAA,CAAA,gCAAA,GAAA,0BAAA;AACA,kBAAA,CAAA,gCAAA,GAAA,OAAA;AACA,kBAAA,qBAAA,EAAA,YAAA;AACA,iBAAA;AACA,eAAA;AACA,cAAA,MAAA,qBAAA,CAAA,KAAA,CAAA,sBAAA,EAAA,uBAAA,CAAA;AACA,aAAA;AACA,WAAA,CAAA;AACA,SAAA;AACA,OAAA,CAAA;;AAEA,MAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,CAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,sBAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA;;AAEA,EAAA,iBAAA,CAAA,IAAA,GAAA,IAAA,KAAA,CAAA,iBAAA,CAAA,IAAA,EAAA;AACA,IAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA;AACA,MAAA,MAAA,QAAA,GAAA,QAAA,CAAA,CAAA,CAAA;AACA,MAAA,MAAA,WAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;;AAEA,MAAA,IAAA,OAAA,QAAA,KAAA,QAAA,IAAA,OAAA,WAAA,KAAA,UAAA,EAAA;AACA,QAAA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,QAAA,CAAA;AACA;;AAEA,MAAA,MAAA,kBAAA,GAAA,IAAA,KAAA,CAAA,WAAA,EAAA;AACA,QAAA,KAAA,CAAA,iBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA;AACA,UAAA,MAAA,6BAAA,GAAA,mBAAA,CAAA,IAAA,CAAA,+BAAA,CAAA;AACA,UAAA,OAAA,+BAAA,CAAA,6BAAA,EAAA,MAAA;AACA,YAAA,OAAA,SAAA;AACA,cAAA;AACA,gBAAA,IAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,CAAA,CAAA;AACA,gBAAA,gBAAA,EAAA,IAAA;AACA,gBAAA,UAAA,EAAA;AACA,kBAAA,CAAA,4BAAA,GAAA,0BAAA;AACA,kBAAA,CAAA,gCAAA,GAAA,0BAAA;AACA,kBAAA,CAAA,gCAAA,GAAA,OAAA;AACA,kBAAA,iBAAA,EAAA,QAAA;AACA,iBAAA;AACA,eAAA;AACA,cAAA,MAAA,iBAAA,CAAA,KAAA,CAAA,kBAAA,EAAA,mBAAA,CAAA;AACA,aAAA;AACA,WAAA,CAAA;AACA,SAAA;AACA,OAAA,CAAA;;AAEA,MAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,CAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,kBAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA;;AAEA,EAAA,iBAAA,CAAA,MAAA,GAAA,IAAA,KAAA,CAAA,iBAAA,CAAA,MAAA,EAAA;AACA,IAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA;AACA,MAAA,MAAA,UAAA,GAAA,QAAA,CAAA,CAAA,CAAA;AACA,MAAA,MAAA,aAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;;AAEA,MAAA,IAAA,OAAA,UAAA,KAAA,QAAA,IAAA,OAAA,aAAA,KAAA,UAAA,EAAA;AACA,QAAA,OAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,QAAA,CAAA;AACA;;AAEA,MAAA,MAAA,oBAAA,GAAA,IAAA,KAAA,CAAA,aAAA,EAAA;AACA,QAAA,KAAA,CAAA,mBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA;AACA,UAAA,MAAA,6BAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,+BAAA,CAAA;AACA,UAAA,OAAA,+BAAA,CAAA,6BAAA,EAAA,MAAA;AACA,YAAA,OAAA,SAAA;AACA,cAAA;AACA,gBAAA,IAAA,EAAA,CAAA,kBAAA,EAAA,UAAA,CAAA,CAAA;AACA,gBAAA,gBAAA,EAAA,IAAA;AACA,gBAAA,UAAA,EAAA;AACA,kBAAA,CAAA,4BAAA,GAAA,0BAAA;AACA,kBAAA,CAAA,gCAAA,GAAA,0BAAA;AACA,kBAAA,CAAA,gCAAA,GAAA,OAAA;AACA,kBAAA,mBAAA,EAAA,UAAA;AACA,iBAAA;AACA,eAAA;AACA,cAAA,MAAA,mBAAA,CAAA,KAAA,CAAA,oBAAA,EAAA,qBAAA,CAAA;AACA,aAAA;AACA,WAAA,CAAA;AACA,SAAA;AACA,OAAA,CAAA;;AAEA,MAAA,OAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,OAAA,EAAA,CAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,oBAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA;;AAEA,EAAA,yBAAA,CAAA,GAAA,CAAA,iBAAA,CAAA;;AAEA,EAAA,OAAA,iBAAA;AACA;;AAEA,SAAA,mBAAA,CAAA,iBAAA,EAAA;AACA,EAAA;AACA,IAAA,OAAA,iBAAA,KAAA,QAAA;AACA,IAAA,iBAAA,KAAA,IAAA;AACA,IAAA,UAAA,IAAA,iBAAA;AACA,IAAA,OAAA,iBAAA,CAAA,QAAA,KAAA,UAAA;AACA,IAAA,MAAA,IAAA,iBAAA;AACA,IAAA,OAAA,iBAAA,CAAA,IAAA,KAAA,UAAA;AACA,IAAA,QAAA,IAAA,iBAAA;AACA,IAAA,OAAA,iBAAA,CAAA,MAAA,KAAA,UAAA;AACA,IAAA,SAAA,IAAA,iBAAA;AACA,IAAA,OAAA,iBAAA,CAAA,OAAA,KAAA;AACA;AACA;;AAEA,SAAA,6BAAA,CAAA,MAAA,EAAA;AACA,EAAA;AACA,IAAA,OAAA,MAAA,KAAA,QAAA;AACA,IAAA,MAAA,KAAA,IAAA;AACA,IAAA,IAAA,IAAA,MAAA;AACA,KAAA,OAAA,MAAA,CAAA,EAAA,KAAA,QAAA,IAAA,OAAA,MAAA,CAAA,EAAA,KAAA,QAAA;AACA;AACA;;AAOA;AACA,SAAA,+BAAA,CAAA,MAAA,EAAA;AACA,EAAA;AACA,IAAA,OAAA,MAAA,KAAA,QAAA;AACA,IAAA,MAAA,KAAA,IAAA;AACA,IAAA,WAAA,IAAA,MAAA;AACA,IAAA,OAAA,MAAA,CAAA,SAAA,KAAA,QAAA;AACA,IAAA,WAAA,IAAA,MAAA;AACA,KAAA,OAAA,MAAA,CAAA,SAAA,KAAA,QAAA,IAAA,OAAA,MAAA,CAAA,SAAA,KAAA,QAAA;AACA;AACA;;AAKA,MAAA,uCAAA,GAAA,IAAA,GAAA,EAAA;;AAEA,SAAA,sBAAA,CAAA,SAAA,EAAA;AACA,EAAA,uCAAA,CAAA,MAAA,CAAA,SAAA,CAAA;AACA;;AAEA,SAAA,wBAAA,CAAA,SAAA,EAAA,SAAA,EAAA;AACA,EAAA,MAAA,UAAA,GAAA,aAAA,EAAA;AACA,EAAA,IAAA,UAAA,EAAA;AACA,IAAA,MAAA,kBAAA,GAAA,uCAAA,CAAA,GAAA,CAAA,SAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AACA,IAAA,kBAAA,CAAA,GAAA,CAAA,SAAA,EAAA,UAAA,CAAA;AACA,IAAA,uCAAA,CAAA,GAAA,CAAA,SAAA,EAAA,kBAAA,CAAA;AACA;AACA;;AAEA,SAAA,+BAAA;AACA,EAAA,gBAAA;AACA,EAAA,EAAA;AACA,EAAA;AACA,EAAA,IAAA,gBAAA,EAAA;AACA,IAAA,MAAA,EAAA,SAAA,EAAA,SAAA,EAAA,GAAA,gBAAA;AACA,IAAA,MAAA,gBAAA,GAAA,uCAAA,CAAA,GAAA,CAAA,SAAA,CAAA;;AAEA,IAAA,IAAA,CAAA,gBAAA,EAAA;AACA,MAAA,OAAA,EAAA,EAAA;AACA;;AAEA,IAAA,MAAA,IAAA,GAAA,gBAAA,CAAA,GAAA,CAAA,SAAA,CAAA;AACA,IAAA,IAAA,CAAA,IAAA,EAAA;AACA,MAAA,OAAA,EAAA,EAAA;AACA;;AAEA;AACA,IAAA,gBAAA,CAAA,MAAA,CAAA,SAAA,CAAA;AACA,IAAA,OAAA,cAAA,CAAA,IAAA,EAAA,MAAA;AACA,MAAA,OAAA,EAAA,EAAA;AACA,KAAA,CAAA;AACA;;AAEA,EAAA,OAAA,EAAA,EAAA;AACA;;;;"}