{"version": 3, "file": "request.js", "sources": ["../../../src/utils/request.ts"], "sourcesContent": ["import type { PolymorphicRequest } from '../types-hoist/polymorphics';\nimport type { RequestEventData } from '../types-hoist/request';\nimport type { WebFetchHeaders, WebFetchRequest } from '../types-hoist/webfetchapi';\n\n/**\n * Transforms a `Headers` object that implements the `Web Fetch API` (https://developer.mozilla.org/en-US/docs/Web/API/Headers) into a simple key-value dict.\n * The header keys will be lower case: e.g. A \"Content-Type\" header will be stored as \"content-type\".\n */\nexport function winterCGHeadersToDict(winterCGHeaders: WebFetchHeaders): Record<string, string> {\n  const headers: Record<string, string> = {};\n  try {\n    winterCGHeaders.forEach((value, key) => {\n      if (typeof value === 'string') {\n        // We check that value is a string even though it might be redundant to make sure prototype pollution is not possible.\n        headers[key] = value;\n      }\n    });\n  } catch {\n    // just return the empty headers\n  }\n\n  return headers;\n}\n\n/**\n * Convert common request headers to a simple dictionary.\n */\nexport function headersToDict(reqHeaders: Record<string, string | string[] | undefined>): Record<string, string> {\n  const headers: Record<string, string> = Object.create(null);\n\n  try {\n    Object.entries(reqHeaders).forEach(([key, value]) => {\n      if (typeof value === 'string') {\n        headers[key] = value;\n      }\n    });\n  } catch {\n    // just return the empty headers\n  }\n\n  return headers;\n}\n\n/**\n * Converts a `Request` object that implements the `Web Fetch API` (https://developer.mozilla.org/en-US/docs/Web/API/Headers) into the format that the `RequestData` integration understands.\n */\nexport function winterCGRequestToRequestData(req: WebFetchRequest): RequestEventData {\n  const headers = winterCGHeadersToDict(req.headers);\n\n  return {\n    method: req.method,\n    url: req.url,\n    query_string: extractQueryParamsFromUrl(req.url),\n    headers,\n    // TODO: Can we extract body data from the request?\n  };\n}\n\n/**\n * Convert a HTTP request object to RequestEventData to be passed as normalizedRequest.\n * Instead of allowing `PolymorphicRequest` to be passed,\n * we want to be more specific and generally require a http.IncomingMessage-like object.\n */\nexport function httpRequestToRequestData(request: {\n  method?: string;\n  url?: string;\n  headers?: {\n    [key: string]: string | string[] | undefined;\n  };\n  protocol?: string;\n  socket?: {\n    encrypted?: boolean;\n    remoteAddress?: string;\n  };\n}): RequestEventData {\n  const headers = request.headers || {};\n\n  // Check for x-forwarded-host first, then fall back to host header\n  const forwardedHost = typeof headers['x-forwarded-host'] === 'string' ? headers['x-forwarded-host'] : undefined;\n  const host = forwardedHost || (typeof headers.host === 'string' ? headers.host : undefined);\n\n  // Check for x-forwarded-proto first, then fall back to existing protocol detection\n  const forwardedProto = typeof headers['x-forwarded-proto'] === 'string' ? headers['x-forwarded-proto'] : undefined;\n  const protocol = forwardedProto || request.protocol || (request.socket?.encrypted ? 'https' : 'http');\n\n  const url = request.url || '';\n\n  const absoluteUrl = getAbsoluteUrl({\n    url,\n    host,\n    protocol,\n  });\n\n  // This is non-standard, but may be sometimes set\n  // It may be overwritten later by our own body handling\n  const data = (request as PolymorphicRequest).body || undefined;\n\n  // This is non-standard, but may be set on e.g. Next.js or Express requests\n  const cookies = (request as PolymorphicRequest).cookies;\n\n  return {\n    url: absoluteUrl,\n    method: request.method,\n    query_string: extractQueryParamsFromUrl(url),\n    headers: headersToDict(headers),\n    cookies,\n    data,\n  };\n}\n\nfunction getAbsoluteUrl({\n  url,\n  protocol,\n  host,\n}: {\n  url?: string;\n  protocol: string;\n  host?: string;\n}): string | undefined {\n  if (url?.startsWith('http')) {\n    return url;\n  }\n\n  if (url && host) {\n    return `${protocol}://${host}${url}`;\n  }\n\n  return undefined;\n}\n\n/** Extract the query params from an URL. */\nexport function extractQueryParamsFromUrl(url: string): string | undefined {\n  // url is path and query string\n  if (!url) {\n    return;\n  }\n\n  try {\n    // The `URL` constructor can't handle internal URLs of the form `/some/path/here`, so stick a dummy protocol and\n    // hostname as the base. Since the point here is just to grab the query string, it doesn't matter what we use.\n    const queryParams = new URL(url, 'http://s.io').search.slice(1);\n    return queryParams.length ? queryParams : undefined;\n  } catch {\n    return undefined;\n  }\n}\n"], "names": [], "mappings": ";;AAIA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,eAAe,EAA2C;AAChG,EAAE,MAAM,OAAO,GAA2B,EAAE;AAC5C,EAAE,IAAI;AACN,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC5C,MAAM,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;AACrC;AACA,QAAQ,OAAO,CAAC,GAAG,CAAA,GAAI,KAAK;AAC5B;AACA,KAAK,CAAC;AACN,IAAI,MAAM;AACV;AACA;;AAEA,EAAE,OAAO,OAAO;AAChB;;AAEA;AACA;AACA;AACO,SAAS,aAAa,CAAC,UAAU,EAAyE;AACjH,EAAE,MAAM,OAAO,GAA2B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;;AAE7D,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;AACzD,MAAM,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;AACrC,QAAQ,OAAO,CAAC,GAAG,CAAA,GAAI,KAAK;AAC5B;AACA,KAAK,CAAC;AACN,IAAI,MAAM;AACV;AACA;;AAEA,EAAE,OAAO,OAAO;AAChB;;AAEA;AACA;AACA;AACO,SAAS,4BAA4B,CAAC,GAAG,EAAqC;AACrF,EAAE,MAAM,UAAU,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC;;AAEpD,EAAE,OAAO;AACT,IAAI,MAAM,EAAE,GAAG,CAAC,MAAM;AACtB,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG;AAChB,IAAI,YAAY,EAAE,yBAAyB,CAAC,GAAG,CAAC,GAAG,CAAC;AACpD,IAAI,OAAO;AACX;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC;;AAWzC,EAAqB;AACrB,EAAE,MAAM,UAAU,OAAO,CAAC,OAAQ,IAAG,EAAE;;AAEvC;AACA,EAAE,MAAM,aAAc,GAAE,OAAO,OAAO,CAAC,kBAAkB,CAAA,KAAM,QAAA,GAAW,OAAO,CAAC,kBAAkB,CAAA,GAAI,SAAS;AACjH,EAAE,MAAM,IAAK,GAAE,kBAAkB,OAAO,OAAO,CAAC,IAAK,KAAI,WAAW,OAAO,CAAC,IAAK,GAAE,SAAS,CAAC;;AAE7F;AACA,EAAE,MAAM,cAAe,GAAE,OAAO,OAAO,CAAC,mBAAmB,CAAA,KAAM,QAAA,GAAW,OAAO,CAAC,mBAAmB,CAAA,GAAI,SAAS;AACpH,EAAE,MAAM,QAAS,GAAE,kBAAkB,OAAO,CAAC,QAAS,KAAI,OAAO,CAAC,MAAM,EAAE,SAAA,GAAY,OAAQ,GAAE,MAAM,CAAC;;AAEvG,EAAE,MAAM,GAAI,GAAE,OAAO,CAAC,GAAA,IAAO,EAAE;;AAE/B,EAAE,MAAM,WAAA,GAAc,cAAc,CAAC;AACrC,IAAI,GAAG;AACP,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,GAAG,CAAC;;AAEJ;AACA;AACA,EAAE,MAAM,OAAO,CAAC,UAA+B,IAAK,IAAG,SAAS;;AAEhE;AACA,EAAE,MAAM,OAAQ,GAAE,CAAC,OAAQ,GAAuB,OAAO;;AAEzD,EAAE,OAAO;AACT,IAAI,GAAG,EAAE,WAAW;AACpB,IAAI,MAAM,EAAE,OAAO,CAAC,MAAM;AAC1B,IAAI,YAAY,EAAE,yBAAyB,CAAC,GAAG,CAAC;AAChD,IAAI,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC;AACnC,IAAI,OAAO;AACX,IAAI,IAAI;AACR,GAAG;AACH;;AAEA,SAAS,cAAc,CAAC;AACxB,EAAE,GAAG;AACL,EAAE,QAAQ;AACV,EAAE,IAAI;AACN;;AAIA,EAAuB;AACvB,EAAE,IAAI,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE;AAC/B,IAAI,OAAO,GAAG;AACd;;AAEA,EAAE,IAAI,GAAI,IAAG,IAAI,EAAE;AACnB,IAAI,OAAO,CAAC,EAAA,QAAA,CAAA,GAAA,EAAA,IAAA,CAAA,EAAA,GAAA,CAAA,CAAA;AACA;;AAEA,EAAA,OAAA,SAAA;AACA;;AAEA;AACA,SAAA,yBAAA,CAAA,GAAA,EAAA;AACA;AACA,EAAA,IAAA,CAAA,GAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA;AACA;AACA;AACA,IAAA,MAAA,WAAA,GAAA,IAAA,GAAA,CAAA,GAAA,EAAA,aAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,WAAA,CAAA,MAAA,GAAA,WAAA,GAAA,SAAA;AACA,GAAA,CAAA,MAAA;AACA,IAAA,OAAA,SAAA;AACA;AACA;;;;;;;;"}