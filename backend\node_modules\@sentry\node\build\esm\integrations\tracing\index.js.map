{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/tracing/index.ts"], "sourcesContent": ["import type { Integration } from '@sentry/core';\nimport { instrumentOtelHttp } from '../http';\nimport { amqplibIntegration, instrumentAmqplib } from './amqplib';\nimport { connectIntegration, instrumentConnect } from './connect';\nimport { expressIntegration, instrumentExpress, instrumentExpressV5 } from './express';\nimport { fastifyIntegration, instrumentFastify, instrumentFastifyV3 } from './fastify';\nimport { genericPoolIntegration, instrumentGenericPool } from './genericPool';\nimport { graphqlIntegration, instrumentGraphql } from './graphql';\nimport { hapiIntegration, instrumentHapi } from './hapi';\nimport { instrumentKafka, kafkaIntegration } from './kafka';\nimport { instrumentKoa, koaIntegration } from './koa';\nimport { instrumentLruMemoizer, lruMemoizerIntegration } from './lrumemoizer';\nimport { instrumentMongo, mongoIntegration } from './mongo';\nimport { instrumentMongoose, mongooseIntegration } from './mongoose';\nimport { instrumentMysql, mysqlIntegration } from './mysql';\nimport { instrumentMysql2, mysql2Integration } from './mysql2';\nimport { instrumentPostgres, postgresIntegration } from './postgres';\nimport { instrumentPostgresJs, postgresJsIntegration } from './postgresjs';\nimport { prismaIntegration } from './prisma';\nimport { instrumentRedis, redisIntegration } from './redis';\nimport { instrumentTedious, tediousIntegration } from './tedious';\nimport { instrumentVercelAi, vercelAIIntegration } from './vercelai';\n\n/**\n * With OTEL, all performance integrations will be added, as OTEL only initializes them when the patched package is actually required.\n */\nexport function getAutoPerformanceIntegrations(): Integration[] {\n  return [\n    expressIntegration(),\n    fastifyIntegration(),\n    graphqlIntegration(),\n    mongoIntegration(),\n    mongooseIntegration(),\n    mysqlIntegration(),\n    mysql2Integration(),\n    redisIntegration(),\n    postgresIntegration(),\n    prismaIntegration(),\n    hapiIntegration(),\n    koaIntegration(),\n    connectIntegration(),\n    tediousIntegration(),\n    genericPoolIntegration(),\n    kafkaIntegration(),\n    amqplibIntegration(),\n    lruMemoizerIntegration(),\n    vercelAIIntegration(),\n    postgresJsIntegration(),\n  ];\n}\n\n/**\n * Get a list of methods to instrument OTEL, when preload instrumentation.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function getOpenTelemetryInstrumentationToPreload(): (((options?: any) => void) & { id: string })[] {\n  return [\n    instrumentOtelHttp,\n    instrumentExpress,\n    instrumentExpressV5,\n    instrumentConnect,\n    instrumentFastify,\n    instrumentFastifyV3,\n    instrumentHapi,\n    instrumentKafka,\n    instrumentKoa,\n    instrumentLruMemoizer,\n    instrumentMongo,\n    instrumentMongoose,\n    instrumentMysql,\n    instrumentMysql2,\n    instrumentPostgres,\n    instrumentHapi,\n    instrumentGraphql,\n    instrumentRedis,\n    instrumentTedious,\n    instrumentGenericPool,\n    instrumentAmqplib,\n    instrumentVercelAi,\n    instrumentPostgresJs,\n  ];\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;AACO,SAAS,8BAA8B,GAAkB;AAChE,EAAE,OAAO;AACT,IAAI,kBAAkB,EAAE;AACxB,IAAI,kBAAkB,EAAE;AACxB,IAAI,kBAAkB,EAAE;AACxB,IAAI,gBAAgB,EAAE;AACtB,IAAI,mBAAmB,EAAE;AACzB,IAAI,gBAAgB,EAAE;AACtB,IAAI,iBAAiB,EAAE;AACvB,IAAI,gBAAgB,EAAE;AACtB,IAAI,mBAAmB,EAAE;AACzB,IAAI,iBAAiB,EAAE;AACvB,IAAI,eAAe,EAAE;AACrB,IAAI,cAAc,EAAE;AACpB,IAAI,kBAAkB,EAAE;AACxB,IAAI,kBAAkB,EAAE;AACxB,IAAI,sBAAsB,EAAE;AAC5B,IAAI,gBAAgB,EAAE;AACtB,IAAI,kBAAkB,EAAE;AACxB,IAAI,sBAAsB,EAAE;AAC5B,IAAI,mBAAmB,EAAE;AACzB,IAAI,qBAAqB,EAAE;AAC3B,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACO,SAAS,wCAAwC,GAAmD;AAC3G,EAAE,OAAO;AACT,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AACvB,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,qBAAqB;AACzB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,oBAAoB;AACxB,GAAG;AACH;;;;"}