{"version": 3, "file": "featureFlagsIntegration.js", "sources": ["../../../../src/integrations/featureFlags/featureFlagsIntegration.ts"], "sourcesContent": ["import { type Client } from '../../client';\nimport { defineIntegration } from '../../integration';\nimport { type Event, type EventHint } from '../../types-hoist/event';\nimport { type Integration, type IntegrationFn } from '../../types-hoist/integration';\nimport {\n  _INTERNAL_addFeatureFlagToActiveSpan,\n  _INTERNAL_copyFlagsFromScopeToEvent,\n  _INTERNAL_insertFlagToScope,\n} from '../../utils/featureFlags';\n\nexport interface FeatureFlagsIntegration extends Integration {\n  addFeatureFlag: (name: string, value: unknown) => void;\n}\n\n/**\n * Sentry integration for buffering feature flag evaluations manually with an API, and\n * capturing them on error events and spans.\n *\n * See the [feature flag documentation](https://develop.sentry.dev/sdk/expected-features/#feature-flags) for more information.\n *\n * @example\n * ```\n * import * as Sentry from '@sentry/browser';\n * import { type FeatureFlagsIntegration } from '@sentry/browser';\n *\n * // Setup\n * Sentry.init(..., integrations: [Sentry.featureFlagsIntegration()])\n *\n * // Verify\n * const flagsIntegration = Sentry.getClient()?.getIntegrationByName<FeatureFlagsIntegration>('FeatureFlags');\n * if (flagsIntegration) {\n *   flagsIntegration.addFeatureFlag('my-flag', true);\n * } else {\n *   // check your setup\n * }\n * Sentry.captureException(Exception('broke')); // 'my-flag' should be captured to this Sentry event.\n * ```\n */\nexport const featureFlagsIntegration = defineIntegration(() => {\n  return {\n    name: 'FeatureFlags',\n\n    processEvent(event: Event, _hint: EventHint, _client: Client): Event {\n      return _INTERNAL_copyFlagsFromScopeToEvent(event);\n    },\n\n    addFeatureFlag(name: string, value: unknown): void {\n      _INTERNAL_insertFlagToScope(name, value);\n      _INTERNAL_addFeatureFlagToActiveSpan(name, value);\n    },\n  };\n}) as IntegrationFn<FeatureFlagsIntegration>;\n"], "names": [], "mappings": ";;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,uBAAwB,GAAE,iBAAiB,CAAC,MAAM;AAC/D,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,cAAc;;AAExB,IAAI,YAAY,CAAC,KAAK,EAAS,KAAK,EAAa,OAAO,EAAiB;AACzE,MAAM,OAAO,mCAAmC,CAAC,KAAK,CAAC;AACvD,KAAK;;AAEL,IAAI,cAAc,CAAC,IAAI,EAAU,KAAK,EAAiB;AACvD,MAAM,2BAA2B,CAAC,IAAI,EAAE,KAAK,CAAC;AAC9C,MAAM,oCAAoC,CAAC,IAAI,EAAE,KAAK,CAAC;AACvD,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;;;"}