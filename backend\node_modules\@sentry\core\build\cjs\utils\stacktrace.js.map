{"version": 3, "file": "stacktrace.js", "sources": ["../../../src/utils/stacktrace.ts"], "sourcesContent": ["import type { Event } from '../types-hoist/event';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport type { <PERSON>ack<PERSON>ineParser, StackParser } from '../types-hoist/stacktrace';\n\nconst STACKTRACE_FRAME_LIMIT = 50;\nexport const UNKNOWN_FUNCTION = '?';\n// Used to sanitize webpack (error: *) wrapped stack errors\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STRIP_FRAME_REGEXP = /captureMessage|captureException/;\n\n/**\n * Creates a stack parser with the supplied line parsers\n *\n * StackFrames are returned in the correct order for Sentry Exception\n * frames and with Sentry SDK internal frames removed from the top and bottom\n *\n */\nexport function createStackParser(...parsers: StackLineParser[]): StackParser {\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n\n  return (stack: string, skipFirstLines: number = 0, framesToPop: number = 0): StackFrame[] => {\n    const frames: StackFrame[] = [];\n    const lines = stack.split('\\n');\n\n    for (let i = skipFirstLines; i < lines.length; i++) {\n      const line = lines[i] as string;\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      // Many of the regular expressions use backtracking which results in run time that increases exponentially with\n      // input size. Huge strings can result in hangs/Denial of Service:\n      // https://github.com/getsentry/sentry-javascript/issues/2286\n      if (line.length > 1024) {\n        continue;\n      }\n\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n\n      if (frames.length >= STACKTRACE_FRAME_LIMIT + framesToPop) {\n        break;\n      }\n    }\n\n    return stripSentryFramesAndReverse(frames.slice(framesToPop));\n  };\n}\n\n/**\n * Gets a stack parser implementation from Options.stackParser\n * @see Options\n *\n * If options contains an array of line parsers, it is converted into a parser\n */\nexport function stackParserFromStackParserOptions(stackParser: StackParser | StackLineParser[]): StackParser {\n  if (Array.isArray(stackParser)) {\n    return createStackParser(...stackParser);\n  }\n  return stackParser;\n}\n\n/**\n * Removes Sentry frames from the top and bottom of the stack if present and enforces a limit of max number of frames.\n * Assumes stack input is ordered from top to bottom and returns the reverse representation so call site of the\n * function that caused the crash is the last frame in the array.\n * @hidden\n */\nexport function stripSentryFramesAndReverse(stack: ReadonlyArray<StackFrame>): StackFrame[] {\n  if (!stack.length) {\n    return [];\n  }\n\n  const localStack = Array.from(stack);\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (/sentryWrapped/.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n  }\n\n  // Reversing in the middle of the procedure allows us to just pop the values off the stack\n  localStack.reverse();\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n\n    // When using synthetic events, we will have a 2 levels deep stack, as `new Error('Sentry syntheticException')`\n    // is produced within the scope itself, making it:\n    //\n    //   Sentry.captureException()\n    //   scope.captureException()\n    //\n    // instead of just the top `Sentry` call itself.\n    // This forces us to possibly strip an additional frame in the exact same was as above.\n    if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n      localStack.pop();\n    }\n  }\n\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || getLastStackFrame(localStack).filename,\n    function: frame.function || UNKNOWN_FUNCTION,\n  }));\n}\n\nfunction getLastStackFrame(arr: StackFrame[]): StackFrame {\n  return arr[arr.length - 1] || {};\n}\n\nconst defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n\n/**\n * Get's stack frames from an event without needing to check for undefined properties.\n */\nexport function getFramesFromEvent(event: Event): StackFrame[] | undefined {\n  const exception = event.exception;\n\n  if (exception) {\n    const frames: StackFrame[] = [];\n    try {\n      // @ts-expect-error Object could be undefined\n      exception.values.forEach(value => {\n        // @ts-expect-error Value could be undefined\n        if (value.stacktrace.frames) {\n          // @ts-expect-error Value could be undefined\n          frames.push(...value.stacktrace.frames);\n        }\n      });\n      return frames;\n    } catch (_oO) {\n      return undefined;\n    }\n  }\n  return undefined;\n}\n"], "names": [], "mappings": ";;AAIA,MAAM,sBAAA,GAAyB,EAAE;AAC1B,MAAM,gBAAiB,GAAE;AAChC;AACA,MAAM,oBAAA,GAAuB,iBAAiB;AAC9C,MAAM,kBAAA,GAAqB,iCAAiC;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,GAAG,OAAO,EAAkC;AAC9E,EAAE,MAAM,aAAA,GAAgB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE1E,EAAE,OAAO,CAAC,KAAK,EAAU,cAAc,GAAW,CAAC,EAAE,WAAW,GAAW,CAAC,KAAmB;AAC/F,IAAI,MAAM,MAAM,GAAiB,EAAE;AACnC,IAAI,MAAM,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;;AAEnC,IAAI,KAAK,IAAI,CAAA,GAAI,cAAc,EAAE,CAAE,GAAE,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxD,MAAM,MAAM,IAAK,GAAE,KAAK,CAAC,CAAC,CAAE;AAC5B;AACA;AACA;AACA;AACA,MAAM,IAAI,IAAI,CAAC,MAAO,GAAE,IAAI,EAAE;AAC9B,QAAQ;AACR;;AAEA;AACA;AACA,MAAM,MAAM,WAAY,GAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAA,GAAI,IAAI;;AAE3G;AACA;AACA,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC3C,QAAQ;AACR;;AAEA,MAAM,KAAK,MAAM,MAAO,IAAG,aAAa,EAAE;AAC1C,QAAQ,MAAM,KAAM,GAAE,MAAM,CAAC,WAAW,CAAC;;AAEzC,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5B,UAAU;AACV;AACA;;AAEA,MAAM,IAAI,MAAM,CAAC,UAAU,sBAAA,GAAyB,WAAW,EAAE;AACjE,QAAQ;AACR;AACA;;AAEA,IAAI,OAAO,2BAA2B,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AACjE,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iCAAiC,CAAC,WAAW,EAAgD;AAC7G,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAClC,IAAI,OAAO,iBAAiB,CAAC,GAAG,WAAW,CAAC;AAC5C;AACA,EAAE,OAAO,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B,CAAC,KAAK,EAA2C;AAC5F,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACrB,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,MAAM,aAAa,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;AAEtC;AACA,EAAE,IAAI,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE;AAC1E,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB;;AAEA;AACA,EAAE,UAAU,CAAC,OAAO,EAAE;;AAEtB;AACA,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE;AAC7E,IAAI,UAAU,CAAC,GAAG,EAAE;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE;AAC/E,MAAM,UAAU,CAAC,GAAG,EAAE;AACtB;AACA;;AAEA,EAAE,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAAC,GAAG,CAAC,KAAA,KAAU;AACnE,IAAI,GAAG,KAAK;AACZ,IAAI,QAAQ,EAAE,KAAK,CAAC,QAAA,IAAY,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAQ;AACtE,IAAI,QAAQ,EAAE,KAAK,CAAC,QAAA,IAAY,gBAAgB;AAChD,GAAG,CAAC,CAAC;AACL;;AAEA,SAAS,iBAAiB,CAAC,GAAG,EAA4B;AAC1D,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,MAAO,GAAE,CAAC,CAAA,IAAK,EAAE;AAClC;;AAEA,MAAM,mBAAA,GAAsB,aAAa;;AAEzC;AACA;AACA;AACO,SAAS,eAAe,CAAC,EAAE,EAAmB;AACrD,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,EAAA,IAAM,OAAO,EAAA,KAAO,UAAU,EAAE;AACzC,MAAM,OAAO,mBAAmB;AAChC;AACA,IAAI,OAAO,EAAE,CAAC,IAAA,IAAQ,mBAAmB;AACzC,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA;AACA,IAAI,OAAO,mBAAmB;AAC9B;AACA;;AAEA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,KAAK,EAAmC;AAC3E,EAAE,MAAM,SAAA,GAAY,KAAK,CAAC,SAAS;;AAEnC,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,MAAM,MAAM,GAAiB,EAAE;AACnC,IAAI,IAAI;AACR;AACA,MAAM,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS;AACxC;AACA,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE;AACrC;AACA,UAAU,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;AACjD;AACA,OAAO,CAAC;AACR,MAAM,OAAO,MAAM;AACnB,KAAM,CAAA,OAAO,GAAG,EAAE;AAClB,MAAM,OAAO,SAAS;AACtB;AACA;AACA,EAAE,OAAO,SAAS;AAClB;;;;;;;;;"}