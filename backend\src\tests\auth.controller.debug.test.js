const mongoose = require('mongoose');
const User = require('../models/user.model');

console.log('Starting debug test file');

describe('Auth Controller Debug', () => {
  beforeAll(async () => {
    console.log('BeforeAll: Connecting to MongoDB');
    try {
      await mongoose.connect(process.env.MONGODB_URI, {
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 5000
      });
      console.log('BeforeAll: MongoDB connected');
    } catch (err) {
      console.error('MongoDB connection error:', err);
      throw err;
    }
  });

  afterAll(async () => {
    console.log('AfterAll: Closing MongoDB connection');
    await mongoose.connection.close();
    console.log('AfterAll: MongoDB connection closed');
  });

  it('should simply pass', () => {
    console.log('Running simple test');
    expect(true).toBe(true);
  });

  it('should connect to database', async () => {
    console.log('Running database test');
    const count = await User.countDocuments();
    console.log(`User count: ${count}`);
    expect(count).toBeDefined();
  });
});
