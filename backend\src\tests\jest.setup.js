// Additional setup for Jest tests
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env.test') });

// Set test environment variables if not already set
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret-key-for-testing-that-is-at-least-32-characters-long';
process.env.REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET || 'test-refresh-secret-key-for-testing-that-is-at-least-32-characters-long';

// Mock any external services if needed
console.log('Jest setup running');

// Clear any existing database connections
const mongoose = require('mongoose');
beforeEach(async () => {
  // Clear test data before each test
  if (mongoose.connection.readyState === 1) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      await collections[key].deleteMany({});
    }
  }
});

console.log('Test environment initialized');
