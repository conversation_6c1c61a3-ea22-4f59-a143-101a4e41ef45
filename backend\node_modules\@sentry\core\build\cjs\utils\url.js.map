{"version": 3, "file": "url.js", "sources": ["../../../src/utils/url.ts"], "sourcesContent": ["import {\n  SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  SEMANTIC_ATTRIBUTE_URL_FULL,\n} from '../semanticAttributes';\nimport type { SpanAttributes } from '../types-hoist/span';\n\ntype PartialURL = {\n  host?: string;\n  path?: string;\n  protocol?: string;\n  relative?: string;\n  search?: string;\n  hash?: string;\n};\n\ninterface URLwithCanParse extends URL {\n  canParse: (url: string, base?: string | URL | undefined) => boolean;\n}\n\n// A subset of the URL object that is valid for relative URLs\n// The URL object cannot handle relative URLs, so we need to handle them separately\ntype RelativeURL = {\n  isRelative: true;\n  pathname: URL['pathname'];\n  search: URL['search'];\n  hash: URL['hash'];\n};\n\ntype URLObject = RelativeURL | URL;\n\n// Curious about `thismessage:/`? See: https://www.rfc-editor.org/rfc/rfc2557.html\n//  > When the methods above do not yield an absolute URI, a base URL\n//  > of \"thismessage:/\" MUST be employed. This base URL has been\n//  > defined for the sole purpose of resolving relative references\n//  > within a multipart/related structure when no other base URI is\n//  > specified.\n//\n// We need to provide a base URL to `parseStringToURLObject` because the fetch API gives us a\n// relative URL sometimes.\n//\n// This is the only case where we need to provide a base URL to `parseStringToURLObject`\n// because the relative URL is not valid on its own.\nconst DEFAULT_BASE_URL = 'thismessage:/';\n\n/**\n * Checks if the URL object is relative\n *\n * @param url - The URL object to check\n * @returns True if the URL object is relative, false otherwise\n */\nexport function isURLObjectRelative(url: URLObject): url is RelativeURL {\n  return 'isRelative' in url;\n}\n\n/**\n * Parses string to a URL object\n *\n * @param url - The URL to parse\n * @returns The parsed URL object or undefined if the URL is invalid\n */\nexport function parseStringToURLObject(url: string, urlBase?: string | URL | undefined): URLObject | undefined {\n  const isRelative = url.indexOf('://') <= 0 && url.indexOf('//') !== 0;\n  const base = urlBase ?? (isRelative ? DEFAULT_BASE_URL : undefined);\n  try {\n    // Use `canParse` to short-circuit the URL constructor if it's not a valid URL\n    // This is faster than trying to construct the URL and catching the error\n    // Node 20+, Chrome 120+, Firefox 115+, Safari 17+\n    if ('canParse' in URL && !(URL as unknown as URLwithCanParse).canParse(url, base)) {\n      return undefined;\n    }\n\n    const fullUrlObject = new URL(url, base);\n    if (isRelative) {\n      // Because we used a fake base URL, we need to return a relative URL object.\n      // We cannot return anything about the origin, host, etc. because it will refer to the fake base URL.\n      return {\n        isRelative,\n        pathname: fullUrlObject.pathname,\n        search: fullUrlObject.search,\n        hash: fullUrlObject.hash,\n      };\n    }\n    return fullUrlObject;\n  } catch {\n    // empty body\n  }\n\n  return undefined;\n}\n\n/**\n * Takes a URL object and returns a sanitized string which is safe to use as span name\n * see: https://develop.sentry.dev/sdk/data-handling/#structuring-data\n */\nexport function getSanitizedUrlStringFromUrlObject(url: URLObject): string {\n  if (isURLObjectRelative(url)) {\n    return url.pathname;\n  }\n\n  const newUrl = new URL(url);\n  newUrl.search = '';\n  newUrl.hash = '';\n  if (['80', '443'].includes(newUrl.port)) {\n    newUrl.port = '';\n  }\n  if (newUrl.password) {\n    newUrl.password = '%filtered%';\n  }\n  if (newUrl.username) {\n    newUrl.username = '%filtered%';\n  }\n\n  return newUrl.toString();\n}\n\ntype PartialRequest = {\n  method?: string;\n};\n\nfunction getHttpSpanNameFromUrlObject(\n  urlObject: URLObject | undefined,\n  kind: 'server' | 'client',\n  request?: PartialRequest,\n  routeName?: string,\n): string {\n  const method = request?.method?.toUpperCase() ?? 'GET';\n  const route = routeName\n    ? routeName\n    : urlObject\n      ? kind === 'client'\n        ? getSanitizedUrlStringFromUrlObject(urlObject)\n        : urlObject.pathname\n      : '/';\n\n  return `${method} ${route}`;\n}\n\n/**\n * Takes a parsed URL object and returns a set of attributes for the span\n * that represents the HTTP request for that url. This is used for both server\n * and client http spans.\n *\n * Follows https://opentelemetry.io/docs/specs/semconv/http/.\n *\n * @param urlObject - see {@link parseStringToURLObject}\n * @param kind - The type of HTTP operation (server or client)\n * @param spanOrigin - The origin of the span\n * @param request - The request object, see {@link PartialRequest}\n * @param routeName - The name of the route, must be low cardinality\n * @returns The span name and attributes for the HTTP operation\n */\nexport function getHttpSpanDetailsFromUrlObject(\n  urlObject: URLObject | undefined,\n  kind: 'server' | 'client',\n  spanOrigin: string,\n  request?: PartialRequest,\n  routeName?: string,\n): [name: string, attributes: SpanAttributes] {\n  const attributes: SpanAttributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: spanOrigin,\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n  };\n\n  if (routeName) {\n    // This is based on https://opentelemetry.io/docs/specs/semconv/http/http-spans/#name\n    attributes[kind === 'server' ? 'http.route' : 'url.template'] = routeName;\n    attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] = 'route';\n  }\n\n  if (request?.method) {\n    attributes[SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD] = request.method.toUpperCase();\n  }\n\n  if (urlObject) {\n    if (urlObject.search) {\n      attributes['url.query'] = urlObject.search;\n    }\n    if (urlObject.hash) {\n      attributes['url.fragment'] = urlObject.hash;\n    }\n    if (urlObject.pathname) {\n      attributes['url.path'] = urlObject.pathname;\n      if (urlObject.pathname === '/') {\n        attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] = 'route';\n      }\n    }\n\n    if (!isURLObjectRelative(urlObject)) {\n      attributes[SEMANTIC_ATTRIBUTE_URL_FULL] = urlObject.href;\n      if (urlObject.port) {\n        attributes['url.port'] = urlObject.port;\n      }\n      if (urlObject.protocol) {\n        attributes['url.scheme'] = urlObject.protocol;\n      }\n      if (urlObject.hostname) {\n        attributes[kind === 'server' ? 'server.address' : 'url.domain'] = urlObject.hostname;\n      }\n    }\n  }\n\n  return [getHttpSpanNameFromUrlObject(urlObject, kind, request, routeName), attributes];\n}\n\n/**\n * Parses string form of URL into an object\n * // borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n * // intentionally using regex and not <a/> href parsing trick because React Native and other\n * // environments where DOM might not be available\n * @returns parsed URL object\n */\nexport function parseUrl(url: string): PartialURL {\n  if (!url) {\n    return {};\n  }\n\n  const match = url.match(/^(([^:/?#]+):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n\n  if (!match) {\n    return {};\n  }\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  const query = match[6] || '';\n  const fragment = match[8] || '';\n  return {\n    host: match[4],\n    path: match[5],\n    protocol: match[2],\n    search: query,\n    hash: fragment,\n    relative: match[5] + query + fragment, // everything minus origin\n  };\n}\n\n/**\n * Strip the query string and fragment off of a given URL or path (if present)\n *\n * @param urlPath Full URL or path, including possible query string and/or fragment\n * @returns URL or path without query string or fragment\n */\nexport function stripUrlQueryAndFragment(urlPath: string): string {\n  return (urlPath.split(/[?#]/, 1) as [string, ...string[]])[0];\n}\n\n/**\n * Takes a URL object and returns a sanitized string which is safe to use as span name\n * see: https://develop.sentry.dev/sdk/data-handling/#structuring-data\n */\nexport function getSanitizedUrlString(url: PartialURL): string {\n  const { protocol, host, path } = url;\n\n  const filteredHost =\n    host\n      // Always filter out authority\n      ?.replace(/^.*@/, '[filtered]:[filtered]@')\n      // Don't show standard :80 (http) and :443 (https) ports to reduce the noise\n      // TODO: Use new URL global if it exists\n      .replace(/(:80)$/, '')\n      .replace(/(:443)$/, '') || '';\n\n  return `${protocol ? `${protocol}://` : ''}${filteredHost}${path}`;\n}\n"], "names": ["SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD", "SEMANTIC_ATTRIBUTE_URL_FULL"], "mappings": ";;;;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gBAAA,GAAmB,eAAe;;AAExC;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,GAAG,EAAiC;AACxE,EAAE,OAAO,YAAa,IAAG,GAAG;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,GAAG,EAAU,OAAO,EAAoD;AAC/G,EAAE,MAAM,UAAW,GAAE,GAAG,CAAC,OAAO,CAAC,KAAK,CAAA,IAAK,CAAE,IAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAA,KAAM,CAAC;AACvE,EAAE,MAAM,IAAK,GAAE,OAAQ,KAAI,UAAA,GAAa,gBAAA,GAAmB,SAAS,CAAC;AACrE,EAAE,IAAI;AACN;AACA;AACA;AACA,IAAI,IAAI,UAAA,IAAc,GAAA,IAAO,CAAC,CAAC,GAAI,GAA+B,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;AACvF,MAAM,OAAO,SAAS;AACtB;;AAEA,IAAI,MAAM,gBAAgB,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;AAC5C,IAAI,IAAI,UAAU,EAAE;AACpB;AACA;AACA,MAAM,OAAO;AACb,QAAQ,UAAU;AAClB,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,QAAQ,MAAM,EAAE,aAAa,CAAC,MAAM;AACpC,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI;AAChC,OAAO;AACP;AACA,IAAI,OAAO,aAAa;AACxB,IAAI,MAAM;AACV;AACA;;AAEA,EAAE,OAAO,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACO,SAAS,kCAAkC,CAAC,GAAG,EAAqB;AAC3E,EAAE,IAAI,mBAAmB,CAAC,GAAG,CAAC,EAAE;AAChC,IAAI,OAAO,GAAG,CAAC,QAAQ;AACvB;;AAEA,EAAE,MAAM,MAAO,GAAE,IAAI,GAAG,CAAC,GAAG,CAAC;AAC7B,EAAE,MAAM,CAAC,MAAO,GAAE,EAAE;AACpB,EAAE,MAAM,CAAC,IAAK,GAAE,EAAE;AAClB,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AAC3C,IAAI,MAAM,CAAC,IAAK,GAAE,EAAE;AACpB;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,MAAM,CAAC,QAAS,GAAE,YAAY;AAClC;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,MAAM,CAAC,QAAS,GAAE,YAAY;AAClC;;AAEA,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE;AAC1B;;AAMA,SAAS,4BAA4B;AACrC,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,SAAS;AACX,EAAU;AACV,EAAE,MAAM,MAAO,GAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAG,IAAG,KAAK;AACxD,EAAE,MAAM,QAAQ;AAChB,MAAM;AACN,MAAM;AACN,QAAQ,SAAS;AACjB,UAAU,kCAAkC,CAAC,SAAS;AACtD,UAAU,SAAS,CAAC;AACpB,QAAQ,GAAG;;AAEX,EAAE,OAAO,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,+BAAA;AACA,EAAA,SAAA;AACA,EAAA,IAAA;AACA,EAAA,UAAA;AACA,EAAA,OAAA;AACA,EAAA,SAAA;AACA,EAAA;AACA,EAAA,MAAA,UAAA,GAAA;AACA,IAAA,CAAAA,mDAAA,GAAA,UAAA;AACA,IAAA,CAAAC,mDAAA,GAAA,KAAA;AACA,GAAA;;AAEA,EAAA,IAAA,SAAA,EAAA;AACA;AACA,IAAA,UAAA,CAAA,IAAA,KAAA,QAAA,GAAA,YAAA,GAAA,cAAA,CAAA,GAAA,SAAA;AACA,IAAA,UAAA,CAAAA,mDAAA,CAAA,GAAA,OAAA;AACA;;AAEA,EAAA,IAAA,OAAA,EAAA,MAAA,EAAA;AACA,IAAA,UAAA,CAAAC,yDAAA,CAAA,GAAA,OAAA,CAAA,MAAA,CAAA,WAAA,EAAA;AACA;;AAEA,EAAA,IAAA,SAAA,EAAA;AACA,IAAA,IAAA,SAAA,CAAA,MAAA,EAAA;AACA,MAAA,UAAA,CAAA,WAAA,CAAA,GAAA,SAAA,CAAA,MAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACA,MAAA,UAAA,CAAA,cAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,QAAA,EAAA;AACA,MAAA,UAAA,CAAA,UAAA,CAAA,GAAA,SAAA,CAAA,QAAA;AACA,MAAA,IAAA,SAAA,CAAA,QAAA,KAAA,GAAA,EAAA;AACA,QAAA,UAAA,CAAAD,mDAAA,CAAA,GAAA,OAAA;AACA;AACA;;AAEA,IAAA,IAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAA,UAAA,CAAAE,8CAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA,MAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACA,QAAA,UAAA,CAAA,UAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA;AACA,MAAA,IAAA,SAAA,CAAA,QAAA,EAAA;AACA,QAAA,UAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,QAAA;AACA;AACA,MAAA,IAAA,SAAA,CAAA,QAAA,EAAA;AACA,QAAA,UAAA,CAAA,IAAA,KAAA,QAAA,GAAA,gBAAA,GAAA,YAAA,CAAA,GAAA,SAAA,CAAA,QAAA;AACA;AACA;AACA;;AAEA,EAAA,OAAA,CAAA,4BAAA,CAAA,SAAA,EAAA,IAAA,EAAA,OAAA,EAAA,SAAA,CAAA,EAAA,UAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,QAAA,CAAA,GAAA,EAAA;AACA,EAAA,IAAA,CAAA,GAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA;;AAEA,EAAA,MAAA,KAAA,GAAA,GAAA,CAAA,KAAA,CAAA,8DAAA,CAAA;;AAEA,EAAA,IAAA,CAAA,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA;;AAEA;AACA,EAAA,MAAA,KAAA,GAAA,KAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,QAAA,GAAA,KAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AACA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACA,IAAA,QAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,EAAA,KAAA;AACA,IAAA,IAAA,EAAA,QAAA;AACA,IAAA,QAAA,EAAA,KAAA,CAAA,CAAA,CAAA,GAAA,KAAA,GAAA,QAAA;AACA,GAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,wBAAA,CAAA,OAAA,EAAA;AACA,EAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,GAAA,EAAA;AACA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAA,EAAA,GAAA,GAAA;;AAEA,EAAA,MAAA,YAAA;AACA,IAAA;AACA;AACA,QAAA,OAAA,CAAA,MAAA,EAAA,wBAAA;AACA;AACA;AACA,OAAA,OAAA,CAAA,QAAA,EAAA,EAAA;AACA,OAAA,OAAA,CAAA,SAAA,EAAA,EAAA,CAAA,IAAA,EAAA;;AAEA,EAAA,OAAA,CAAA,EAAA,QAAA,GAAA,CAAA,EAAA,QAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,EAAA,YAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;;;;;;;;;;"}