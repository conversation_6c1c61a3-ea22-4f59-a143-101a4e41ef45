export { registerSpanErrorInstrumentation } from './tracing/errors.js';
export { getCapturedScopesOnSpan, setCapturedScopesOnSpan } from './tracing/utils.js';
export { TRACING_DEFAULTS, startIdleSpan } from './tracing/idleSpan.js';
export { SentrySpan } from './tracing/sentrySpan.js';
export { SentryNonRecordingSpan } from './tracing/sentryNonRecordingSpan.js';
export { SPAN_STATUS_ERROR, SPAN_STATUS_OK, SPAN_STATUS_UNSET, getSpanStatusFromHttpCode, setHttpStatus } from './tracing/spanstatus.js';
export { continueTrace, startInactiveSpan, startNewTrace, startSpan, startSpanManual, suppressTracing, withActiveSpan } from './tracing/trace.js';
export { getDynamicSamplingContextFromClient, getDynamicSamplingContextFromScope, getDynamicSamplingContextFromSpan, spanToBaggageHeader } from './tracing/dynamicSamplingContext.js';
export { setMeasurement, timedEventsToMeasurements } from './tracing/measurement.js';
export { sampleSpan } from './tracing/sampling.js';
export { logSpanEnd, logSpanStart } from './tracing/logSpans.js';
export { SEMANTIC_ATTRIBUTE_CACHE_HIT, SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE, SEMANTIC_ATTRIBUTE_CACHE_KEY, SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME, SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD, SEMANTIC_ATTRIBUTE_PROFILE_ID, SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME, SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON, SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT, SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE, SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, SEMANTIC_ATTRIBUTE_URL_FULL, SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE } from './semanticAttributes.js';
export { createEventEnvelope, createSessionEnvelope, createSpanEnvelope } from './envelope.js';
export { addEventProcessor, captureCheckIn, captureEvent, captureException, captureMessage, captureSession, close, endSession, flush, isEnabled, isInitialized, lastEventId, setContext, setExtra, setExtras, setTag, setTags, setUser, startSession, withMonitor } from './exports.js';
export { getClient, getCurrentScope, getGlobalScope, getIsolationScope, getTraceContextFromScope, withIsolationScope, withScope } from './currentScopes.js';
export { getDefaultCurrentScope, getDefaultIsolationScope } from './defaultScopes.js';
export { setAsyncContextStrategy } from './asyncContext/index.js';
export { getGlobalSingleton, getMainCarrier } from './carrier.js';
export { closeSession, makeSession, updateSession } from './session.js';
export { Scope } from './scope.js';
export { notifyEventProcessors } from './eventProcessors.js';
export { getEnvelopeEndpointWithUrlEncodedAuth, getReportDialogEndpoint } from './api.js';
export { BaseClient, Client } from './client.js';
export { ServerRuntimeClient } from './server-runtime-client.js';
export { initAndBind, setCurrentClient } from './sdk.js';
export { createTransport } from './transports/base.js';
export { makeOfflineTransport } from './transports/offline.js';
export { makeMultiplexedTransport } from './transports/multiplexed.js';
export { addIntegration, defineIntegration, getIntegrationsToSetup } from './integration.js';
export { applyScopeDataToEvent, mergeScopeData } from './utils/applyScopeDataToEvent.js';
export { prepareEvent } from './utils/prepareEvent.js';
export { createCheckInEnvelope } from './checkin.js';
export { hasSpansEnabled, hasTracingEnabled } from './utils/hasSpansEnabled.js';
export { isSentryRequestUrl } from './utils/isSentryRequestUrl.js';
export { handleCallbackErrors } from './utils/handleCallbackErrors.js';
export { fmt, parameterize } from './utils/parameterize.js';
export { addAutoIpAddressToSession, addAutoIpAddressToUser } from './utils/ipAddress.js';
export { addChildSpanToSpan, convertSpanLinksForEnvelope, getActiveSpan, getRootSpan, getSpanDescendants, getStatusMessage, spanIsSampled, spanTimeInputToSeconds, spanToJSON, spanToTraceContext, spanToTraceHeader, updateSpanName } from './utils/spanUtils.js';
export { parseSampleRate } from './utils/parseSampleRate.js';
export { applySdkMetadata } from './utils/sdkMetadata.js';
export { getTraceData } from './utils/traceData.js';
export { getTraceMetaTags } from './utils/meta.js';
export { debounce } from './utils/debounce.js';
export { extractQueryParamsFromUrl, headersToDict, httpRequestToRequestData, winterCGHeadersToDict, winterCGRequestToRequestData } from './utils/request.js';
export { DEFAULT_ENVIRONMENT } from './constants.js';
export { addBreadcrumb } from './breadcrumbs.js';
export { functionToStringIntegration } from './integrations/functiontostring.js';
export { eventFiltersIntegration, inboundFiltersIntegration } from './integrations/eventFilters.js';
export { linkedErrorsIntegration } from './integrations/linkederrors.js';
export { moduleMetadataIntegration } from './integrations/metadata.js';
export { requestDataIntegration } from './integrations/requestdata.js';
export { captureConsoleIntegration } from './integrations/captureconsole.js';
export { dedupeIntegration } from './integrations/dedupe.js';
export { extraErrorDataIntegration } from './integrations/extraerrordata.js';
export { rewriteFramesIntegration } from './integrations/rewriteframes.js';
export { instrumentSupabaseClient, supabaseIntegration } from './integrations/supabase.js';
export { zodErrorsIntegration } from './integrations/zoderrors.js';
export { thirdPartyErrorFilterIntegration } from './integrations/third-party-errors-filter.js';
export { consoleIntegration } from './integrations/console.js';
export { featureFlagsIntegration } from './integrations/featureFlags/featureFlagsIntegration.js';
export { profiler } from './profiling.js';
export { instrumentFetchRequest } from './fetch.js';
export { trpcMiddleware } from './trpc.js';
export { wrapMcpServerWithSentry } from './mcp-server.js';
export { captureFeedback } from './feedback.js';
export { _INTERNAL_captureLog, _INTERNAL_captureSerializedLog, _INTERNAL_flushLogsBuffer } from './logs/exports.js';
export { consoleLoggingIntegration } from './logs/console-integration.js';
export { addVercelAiProcessors } from './utils/vercel-ai.js';
export { _INTERNAL_FLAG_BUFFER_SIZE, _INTERNAL_MAX_FLAGS_PER_SPAN, _INTERNAL_addFeatureFlagToActiveSpan, _INTERNAL_copyFlagsFromScopeToEvent, _INTERNAL_insertFlagToScope } from './utils/featureFlags.js';
export { applyAggregateErrorsToEvent } from './utils/aggregate-errors.js';
export { getBreadcrumbLogLevelFromHttpStatusCode } from './utils/breadcrumb-log-level.js';
export { getComponentName, getLocationHref, htmlTreeAsString } from './utils/browser.js';
export { dsnFromString, dsnToString, makeDsn } from './utils/dsn.js';
export { SentryError } from './utils/error.js';
export { GLOBAL_OBJ } from './utils/worldwide.js';
export { addConsoleInstrumentationHandler } from './instrument/console.js';
export { addFetchEndInstrumentationHandler, addFetchInstrumentationHandler } from './instrument/fetch.js';
export { addGlobalErrorInstrumentationHandler } from './instrument/globalError.js';
export { addGlobalUnhandledRejectionInstrumentationHandler } from './instrument/globalUnhandledRejection.js';
export { addHandler, maybeInstrument, resetInstrumentationHandlers, triggerHandlers } from './instrument/handlers.js';
export { isDOMError, isDOMException, isElement, isError, isErrorEvent, isEvent, isInstanceOf, isParameterizedString, isPlainObject, isPrimitive, isRegExp, isString, isSyntheticEvent, isThenable, isVueViewModel } from './utils/is.js';
export { isBrowser } from './utils/isBrowser.js';
export { CONSOLE_LEVELS, consoleSandbox, logger, originalConsoleMethods } from './utils/logger.js';
export { addContextToFrame, addExceptionMechanism, addExceptionTypeValue, checkOrSetAlreadyCaught, getEventDescription, parseSemver, uuid4 } from './utils/misc.js';
export { isNodeEnv, loadModule } from './utils/node.js';
export { normalize, normalizeToSize, normalizeUrlToBase } from './utils/normalize.js';
export { addNonEnumerableProperty, convertToPlainObject, dropUndefinedKeys, extractExceptionKeysForMessage, fill, getOriginalFunction, markFunctionWrapped, objectify } from './utils/object.js';
export { basename, dirname, isAbsolute, join, normalizePath, relative, resolve } from './utils/path.js';
export { SENTRY_BUFFER_FULL_ERROR, makePromiseBuffer } from './utils/promisebuffer.js';
export { severityLevelFromString } from './utils/severity.js';
export { UNKNOWN_FUNCTION, createStackParser, getFramesFromEvent, getFunctionName, stackParserFromStackParserOptions, stripSentryFramesAndReverse } from './utils/stacktrace.js';
export { filenameIsInApp, node, nodeStackLineParser } from './utils/node-stack-trace.js';
export { isMatchingPattern, safeJoin, snipLine, stringMatchesSomePattern, truncate } from './utils/string.js';
export { isNativeFunction, supportsDOMError, supportsDOMException, supportsErrorEvent, supportsFetch, supportsHistory, supportsNativeFetch, supportsReferrerPolicy, supportsReportingObserver } from './utils/supports.js';
export { SyncPromise, rejectedSyncPromise, resolvedSyncPromise } from './utils/syncpromise.js';
export { browserPerformanceTimeOrigin, dateTimestampInSeconds, timestampInSeconds } from './utils/time.js';
export { TRACEPARENT_REGEXP, extractTraceparentData, generateSentryTraceHeader, propagationContextFromHeaders } from './utils/tracing.js';
export { getSDKSource, isBrowserBundle } from './utils/env.js';
export { addItemToEnvelope, createAttachmentEnvelopeItem, createEnvelope, createEventEnvelopeHeaders, createSpanEnvelopeItem, envelopeContainsItemType, envelopeItemTypeToDataCategory, forEachEnvelopeItem, getSdkMetadataForEnvelopeHeader, parseEnvelope, serializeEnvelope } from './utils/envelope.js';
export { createClientReportEnvelope } from './utils/clientreport.js';
export { DEFAULT_RETRY_AFTER, disabledUntil, isRateLimited, parseRetryAfterHeader, updateRateLimits } from './utils/ratelimit.js';
export { MAX_BAGGAGE_STRING_LENGTH, SENTRY_BAGGAGE_KEY_PREFIX, SENTRY_BAGGAGE_KEY_PREFIX_REGEX, baggageHeaderToDynamicSamplingContext, dynamicSamplingContextToSentryBaggageHeader, objectToBaggageHeader, parseBaggageHeader } from './utils/baggage.js';
export { getHttpSpanDetailsFromUrlObject, getSanitizedUrlString, getSanitizedUrlStringFromUrlObject, isURLObjectRelative, parseStringToURLObject, parseUrl, stripUrlQueryAndFragment } from './utils/url.js';
export { eventFromMessage, eventFromUnknownInput, exceptionFromError, parseStackFrames } from './utils/eventbuilder.js';
export { callFrameToStackFrame, watchdogTimer } from './utils/anr.js';
export { LRUMap } from './utils/lru.js';
export { generateSpanId, generateTraceId } from './utils/propagationContext.js';
export { vercelWaitUntil } from './utils/vercelWaitUntil.js';
export { SDK_VERSION } from './utils/version.js';
export { getDebugImagesForResources, getFilenameToDebugIdMap } from './utils/debug-ids.js';
export { escapeStringForRegex } from './vendor/escapeStringForRegex.js';
//# sourceMappingURL=index.js.map
