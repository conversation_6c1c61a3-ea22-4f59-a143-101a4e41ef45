{"version": 3, "file": "postgresjs.js", "sources": ["../../../../src/integrations/tracing/postgresjs.ts"], "sourcesContent": ["// Instrumentation for https://github.com/porsager/postgres\nimport { context, trace } from '@opentelemetry/api';\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  ATTR_DB_NAMESPACE,\n  ATTR_DB_OPERATION_NAME,\n  ATTR_DB_QUERY_TEXT,\n  ATTR_DB_RESPONSE_STATUS_CODE,\n  ATTR_DB_SYSTEM_NAME,\n  ATTR_ERROR_TYPE,\n  ATTR_SERVER_ADDRESS,\n  ATTR_SERVER_PORT,\n} from '@opentelemetry/semantic-conventions';\nimport type { IntegrationFn, Span } from '@sentry/core';\nimport {\n  defineIntegration,\n  getCurrentScope,\n  logger,\n  SDK_VERSION,\n  SPAN_STATUS_ERROR,\n  startSpanManual,\n} from '@sentry/core';\nimport { generateInstrumentOnce } from '../../otel/instrument';\nimport { addOriginToSpan } from '../../utils/addOriginToSpan';\n\nconst INTEGRATION_NAME = 'PostgresJs';\nconst SUPPORTED_VERSIONS = ['>=3.0.0 <4'];\n\ntype PostgresConnectionContext = {\n  ATTR_DB_NAMESPACE?: string; // Database name\n  ATTR_SERVER_ADDRESS?: string; // Hostname or IP address of the database server\n  ATTR_SERVER_PORT?: string; // Port number of the database server\n};\n\ntype PostgresJsInstrumentationConfig = InstrumentationConfig & {\n  /**\n   * Whether to require a parent span for the instrumentation.\n   * If set to true, the instrumentation will only create spans if there is a parent span\n   * available in the current scope.\n   * @default true\n   */\n  requireParentSpan?: boolean;\n  /**\n   * Hook to modify the span before it is started.\n   * This can be used to set additional attributes or modify the span in any way.\n   */\n  requestHook?: (span: Span, sanitizedSqlQuery: string, postgresConnectionContext?: PostgresConnectionContext) => void;\n};\n\nexport const instrumentPostgresJs = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  (options?: PostgresJsInstrumentationConfig) =>\n    new PostgresJsInstrumentation({\n      requireParentSpan: options?.requireParentSpan ?? true,\n      requestHook: options?.requestHook,\n    }),\n);\n\n/**\n * Instrumentation for the [postgres](https://www.npmjs.com/package/postgres) library.\n * This instrumentation captures postgresjs queries and their attributes,\n */\nexport class PostgresJsInstrumentation extends InstrumentationBase<PostgresJsInstrumentationConfig> {\n  public constructor(config: PostgresJsInstrumentationConfig) {\n    super('sentry-postgres-js', SDK_VERSION, config);\n  }\n\n  /**\n   * Initializes the instrumentation.\n   */\n  public init(): InstrumentationNodeModuleDefinition[] {\n    const instrumentationModule = new InstrumentationNodeModuleDefinition('postgres', SUPPORTED_VERSIONS);\n\n    ['src', 'cf/src', 'cjs/src'].forEach(path => {\n      instrumentationModule.files.push(\n        new InstrumentationNodeModuleFile(\n          `postgres/${path}/connection.js`,\n          ['*'],\n          this._patchConnection.bind(this),\n          this._unwrap.bind(this),\n        ),\n      );\n\n      instrumentationModule.files.push(\n        new InstrumentationNodeModuleFile(\n          `postgres/${path}/query.js`,\n          SUPPORTED_VERSIONS,\n          this._patchQuery.bind(this),\n          this._unwrap.bind(this),\n        ),\n      );\n    });\n\n    return [instrumentationModule];\n  }\n\n  /**\n   * Determines whether a span should be created based on the current context.\n   * If `requireParentSpan` is set to true in the configuration, a span will\n   * only be created if there is a parent span available.\n   */\n  private _shouldCreateSpans(): boolean {\n    const config = this.getConfig();\n    const hasParentSpan = trace.getSpan(context.active()) !== undefined;\n    return hasParentSpan || !config.requireParentSpan;\n  }\n\n  /**\n   * Patches the reject method of the Query class to set the span status and end it\n   */\n  private _patchReject(rejectTarget: any, span: Span): any {\n    return new Proxy(rejectTarget, {\n      apply: (\n        rejectTarget,\n        rejectThisArg,\n        rejectArgs: {\n          message?: string;\n          code?: string;\n          name?: string;\n        }[],\n      ) => {\n        span.setStatus({\n          code: SPAN_STATUS_ERROR,\n          // This message is the error message from the rejectArgs, when available\n          // e.g \"relation 'User' does not exist\"\n          message: rejectArgs?.[0]?.message || 'unknown_error',\n        });\n\n        const result = Reflect.apply(rejectTarget, rejectThisArg, rejectArgs);\n\n        // This status code is PG error code, e.g. '42P01' for \"relation does not exist\"\n        // https://www.postgresql.org/docs/current/errcodes-appendix.html\n        span.setAttribute(ATTR_DB_RESPONSE_STATUS_CODE, rejectArgs?.[0]?.code || 'Unknown error');\n        // This is the error type, e.g. 'PostgresError' for a Postgres error\n        span.setAttribute(ATTR_ERROR_TYPE, rejectArgs?.[0]?.name || 'Unknown error');\n\n        span.end();\n        return result;\n      },\n    });\n  }\n\n  /**\n   * Patches the resolve method of the Query class to end the span when the query is resolved.\n   */\n  private _patchResolve(resolveTarget: any, span: Span): any {\n    return new Proxy(resolveTarget, {\n      apply: (resolveTarget, resolveThisArg, resolveArgs: [{ command?: string }]) => {\n        const result = Reflect.apply(resolveTarget, resolveThisArg, resolveArgs);\n        const sqlCommand = resolveArgs?.[0]?.command;\n\n        if (sqlCommand) {\n          // SQL command is only available when the query is resolved successfully\n          span.setAttribute(ATTR_DB_OPERATION_NAME, sqlCommand);\n        }\n        span.end();\n        return result;\n      },\n    });\n  }\n\n  /**\n   * Patches the Query class to instrument the handle method.\n   */\n  private _patchQuery(moduleExports: {\n    Query: {\n      prototype: {\n        handle: any;\n      };\n    };\n  }): any {\n    moduleExports.Query.prototype.handle = new Proxy(moduleExports.Query.prototype.handle, {\n      apply: async (\n        handleTarget,\n        handleThisArg: {\n          resolve: any;\n          reject: any;\n          strings?: string[];\n        },\n        handleArgs,\n      ) => {\n        if (!this._shouldCreateSpans()) {\n          // If we don't need to create spans, just call the original method\n          return Reflect.apply(handleTarget, handleThisArg, handleArgs);\n        }\n\n        const sanitizedSqlQuery = this._sanitizeSqlQuery(handleThisArg.strings?.[0]);\n\n        return startSpanManual(\n          {\n            name: sanitizedSqlQuery || 'postgresjs.query',\n            op: 'db',\n          },\n          (span: Span) => {\n            const scope = getCurrentScope();\n            const postgresConnectionContext = scope.getScopeData().contexts['postgresjsConnection'] as\n              | PostgresConnectionContext\n              | undefined;\n\n            addOriginToSpan(span, 'auto.db.otel.postgres');\n\n            const { requestHook } = this.getConfig();\n\n            if (requestHook) {\n              safeExecuteInTheMiddle(\n                () => requestHook(span, sanitizedSqlQuery, postgresConnectionContext),\n                error => {\n                  if (error) {\n                    logger.error(`Error in requestHook for ${INTEGRATION_NAME} integration:`, error);\n                  }\n                },\n              );\n            }\n\n            // ATTR_DB_NAMESPACE is used to indicate the database name and the schema name\n            // It's only the database name as we don't have the schema information\n            const databaseName = postgresConnectionContext?.ATTR_DB_NAMESPACE || '<unknown database>';\n            const databaseHost = postgresConnectionContext?.ATTR_SERVER_ADDRESS || '<unknown host>';\n            const databasePort = postgresConnectionContext?.ATTR_SERVER_PORT || '<unknown port>';\n\n            span.setAttribute(ATTR_DB_SYSTEM_NAME, 'postgres');\n            span.setAttribute(ATTR_DB_NAMESPACE, databaseName);\n            span.setAttribute(ATTR_SERVER_ADDRESS, databaseHost);\n            span.setAttribute(ATTR_SERVER_PORT, databasePort);\n            span.setAttribute(ATTR_DB_QUERY_TEXT, sanitizedSqlQuery);\n\n            handleThisArg.resolve = this._patchResolve(handleThisArg.resolve, span);\n            handleThisArg.reject = this._patchReject(handleThisArg.reject, span);\n\n            try {\n              return Reflect.apply(handleTarget, handleThisArg, handleArgs);\n            } catch (error) {\n              span.setStatus({\n                code: SPAN_STATUS_ERROR,\n              });\n              span.end();\n              throw error; // Re-throw the error to propagate it\n            }\n          },\n        );\n      },\n    });\n\n    return moduleExports;\n  }\n\n  /**\n   * Patches the Connection class to set the database, host, and port attributes\n   * when a new connection is created.\n   */\n  private _patchConnection(Connection: any): any {\n    return new Proxy(Connection, {\n      apply: (connectionTarget, thisArg, connectionArgs: { database: string; host: string[]; port: number[] }[]) => {\n        const databaseName = connectionArgs[0]?.database || '<unknown database>';\n        const databaseHost = connectionArgs[0]?.host?.[0] || '<unknown host>';\n        const databasePort = connectionArgs[0]?.port?.[0] || '<unknown port>';\n\n        const scope = getCurrentScope();\n        scope.setContext('postgresjsConnection', {\n          ATTR_DB_NAMESPACE: databaseName,\n          ATTR_SERVER_ADDRESS: databaseHost,\n          ATTR_SERVER_PORT: databasePort,\n        });\n\n        return Reflect.apply(connectionTarget, thisArg, connectionArgs);\n      },\n    });\n  }\n\n  /**\n   * Sanitize SQL query as per the OTEL semantic conventions\n   * https://opentelemetry.io/docs/specs/semconv/database/database-spans/#sanitization-of-dbquerytext\n   */\n  private _sanitizeSqlQuery(sqlQuery: string | undefined): string {\n    if (!sqlQuery) {\n      return 'Unknown SQL Query';\n    }\n\n    return (\n      sqlQuery\n        .replace(/\\s+/g, ' ')\n        .trim() // Remove extra spaces including newlines and trim\n        .substring(0, 1024) // Truncate to 1024 characters\n        .replace(/--.*?(\\r?\\n|$)/g, '') // Single line comments\n        .replace(/\\/\\*[\\s\\S]*?\\*\\//g, '') // Multi-line comments\n        .replace(/;\\s*$/, '') // Remove trailing semicolons\n        .replace(/\\b\\d+\\b/g, '?') // Replace standalone numbers\n        // Collapse whitespace to a single space\n        .replace(/\\s+/g, ' ')\n        // Collapse IN and in clauses\n        // eg. IN (?, ?, ?, ?) to IN (?)\n        .replace(/\\bIN\\b\\s*\\(\\s*\\?(?:\\s*,\\s*\\?)*\\s*\\)/g, 'IN (?)')\n    );\n  }\n}\n\nconst _postgresJsIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentPostgresJs();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [postgres](https://www.npmjs.com/package/postgres) library.\n *\n * For more information, see the [`postgresIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/postgres/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.postgresJsIntegration()],\n * });\n * ```\n */\n\nexport const postgresJsIntegration = defineIntegration(_postgresJsIntegration);\n"], "names": ["generateInstrumentOnce", "InstrumentationBase", "SDK_VERSION", "InstrumentationNodeModuleDefinition", "InstrumentationNodeModuleFile", "trace", "context", "SPAN_STATUS_ERROR", "ATTR_DB_RESPONSE_STATUS_CODE", "ATTR_ERROR_TYPE", "ATTR_DB_OPERATION_NAME", "startSpanManual", "getCurrentScope", "addOriginToSpan", "safeExecuteInTheMiddle", "logger", "ATTR_DB_SYSTEM_NAME", "ATTR_DB_NAMESPACE", "ATTR_SERVER_ADDRESS", "ATTR_SERVER_PORT", "ATTR_DB_QUERY_TEXT", "defineIntegration"], "mappings": ";;;;;;;;;AAAA;;AA+BA,MAAM,gBAAA,GAAmB,YAAY;AACrC,MAAM,kBAAmB,GAAE,CAAC,YAAY,CAAC;;AAuBlC,MAAM,oBAAqB,GAAEA,iCAAsB;AAC1D,EAAE,gBAAgB;AAClB,EAAE,CAAC,OAAO;AACV,IAAI,IAAI,yBAAyB,CAAC;AAClC,MAAM,iBAAiB,EAAE,OAAO,EAAE,iBAAA,IAAqB,IAAI;AAC3D,MAAM,WAAW,EAAE,OAAO,EAAE,WAAW;AACvC,KAAK,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACO,MAAM,yBAAA,SAAkCC,mCAAmB,CAAkC;AACpG,GAAS,WAAW,CAAC,MAAM,EAAmC;AAC9D,IAAI,KAAK,CAAC,oBAAoB,EAAEC,gBAAW,EAAE,MAAM,CAAC;AACpD;;AAEA;AACA;AACA;AACA,GAAS,IAAI,GAA0C;AACvD,IAAI,MAAM,wBAAwB,IAAIC,mDAAmC,CAAC,UAAU,EAAE,kBAAkB,CAAC;;AAEzG,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,IAAA,IAAQ;AACjD,MAAM,qBAAqB,CAAC,KAAK,CAAC,IAAI;AACtC,QAAQ,IAAIC,6CAA6B;AACzC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC;AAC1C,UAAU,CAAC,GAAG,CAAC;AACf,UAAU,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AACjC,SAAS;AACT,OAAO;;AAEP,MAAM,qBAAqB,CAAC,KAAK,CAAC,IAAI;AACtC,QAAQ,IAAIA,6CAA6B;AACzC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;AACrC,UAAU,kBAAkB;AAC5B,UAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AACrC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AACjC,SAAS;AACT,OAAO;AACP,KAAK,CAAC;;AAEN,IAAI,OAAO,CAAC,qBAAqB,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAU,kBAAkB,GAAY;AACxC,IAAI,MAAM,MAAO,GAAE,IAAI,CAAC,SAAS,EAAE;AACnC,IAAI,MAAM,aAAA,GAAgBC,SAAK,CAAC,OAAO,CAACC,WAAO,CAAC,MAAM,EAAE,CAAA,KAAM,SAAS;AACvE,IAAI,OAAO,aAAc,IAAG,CAAC,MAAM,CAAC,iBAAiB;AACrD;;AAEA;AACA;AACA;AACA,GAAU,YAAY,CAAC,YAAY,EAAO,IAAI,EAAa;AAC3D,IAAI,OAAO,IAAI,KAAK,CAAC,YAAY,EAAE;AACnC,MAAM,KAAK,EAAE;AACb,QAAQ,YAAY;AACpB,QAAQ,aAAa;AACrB,QAAQ;;AAIA;AACR,WAAW;AACX,QAAQ,IAAI,CAAC,SAAS,CAAC;AACvB,UAAU,IAAI,EAAEC,sBAAiB;AACjC;AACA;AACA,UAAU,OAAO,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,OAAQ,IAAG,eAAe;AAC9D,SAAS,CAAC;;AAEV,QAAQ,MAAM,MAAA,GAAS,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;;AAE7E;AACA;AACA,QAAQ,IAAI,CAAC,YAAY,CAACC,gDAA4B,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,IAAK,IAAG,eAAe,CAAC;AACjG;AACA,QAAQ,IAAI,CAAC,YAAY,CAACC,mCAAe,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,IAAK,IAAG,eAAe,CAAC;;AAEpF,QAAQ,IAAI,CAAC,GAAG,EAAE;AAClB,QAAQ,OAAO,MAAM;AACrB,OAAO;AACP,KAAK,CAAC;AACN;;AAEA;AACA;AACA;AACA,GAAU,aAAa,CAAC,aAAa,EAAO,IAAI,EAAa;AAC7D,IAAI,OAAO,IAAI,KAAK,CAAC,aAAa,EAAE;AACpC,MAAM,KAAK,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,WAAW,KAA6B;AACrF,QAAQ,MAAM,MAAA,GAAS,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,cAAc,EAAE,WAAW,CAAC;AAChF,QAAQ,MAAM,aAAa,WAAW,GAAG,CAAC,CAAC,EAAE,OAAO;;AAEpD,QAAQ,IAAI,UAAU,EAAE;AACxB;AACA,UAAU,IAAI,CAAC,YAAY,CAACC,0CAAsB,EAAE,UAAU,CAAC;AAC/D;AACA,QAAQ,IAAI,CAAC,GAAG,EAAE;AAClB,QAAQ,OAAO,MAAM;AACrB,OAAO;AACP,KAAK,CAAC;AACN;;AAEA;AACA;AACA;AACA,GAAU,WAAW,CAAC;;AAMpB,EAAQ;AACV,IAAI,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE;AAC3F,MAAM,KAAK,EAAE;AACb,QAAQ,YAAY;AACpB,QAAQ;;AAIA;AACR,QAAQ,UAAU;AAClB,WAAW;AACX,QAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;AACxC;AACA,UAAU,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;AACvE;;AAEA,QAAQ,MAAM,iBAAA,GAAoB,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;;AAEpF,QAAQ,OAAOC,oBAAe;AAC9B,UAAU;AACV,YAAY,IAAI,EAAE,iBAAkB,IAAG,kBAAkB;AACzD,YAAY,EAAE,EAAE,IAAI;AACpB,WAAW;AACX,UAAU,CAAC,IAAI,KAAW;AAC1B,YAAY,MAAM,KAAA,GAAQC,oBAAe,EAAE;AAC3C,YAAY,MAAM,yBAAA,GAA4B,KAAK,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,sBAAsB;;AAEpF;;AAEd,YAAYC,+BAAe,CAAC,IAAI,EAAE,uBAAuB,CAAC;;AAE1D,YAAY,MAAM,EAAE,WAAY,EAAA,GAAI,IAAI,CAAC,SAAS,EAAE;;AAEpD,YAAY,IAAI,WAAW,EAAE;AAC7B,cAAcC,sCAAsB;AACpC,gBAAgB,MAAM,WAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,yBAAyB,CAAC;AACrF,gBAAgB,SAAS;AACzB,kBAAkB,IAAI,KAAK,EAAE;AAC7B,oBAAoBC,WAAM,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,gBAAgB,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC;AACpG;AACA,iBAAiB;AACjB,eAAe;AACf;;AAEA;AACA;AACA,YAAY,MAAM,YAAa,GAAE,yBAAyB,EAAE,iBAAA,IAAqB,oBAAoB;AACrG,YAAY,MAAM,YAAa,GAAE,yBAAyB,EAAE,mBAAA,IAAuB,gBAAgB;AACnG,YAAY,MAAM,YAAa,GAAE,yBAAyB,EAAE,gBAAA,IAAoB,gBAAgB;;AAEhG,YAAY,IAAI,CAAC,YAAY,CAACC,uCAAmB,EAAE,UAAU,CAAC;AAC9D,YAAY,IAAI,CAAC,YAAY,CAACC,qCAAiB,EAAE,YAAY,CAAC;AAC9D,YAAY,IAAI,CAAC,YAAY,CAACC,uCAAmB,EAAE,YAAY,CAAC;AAChE,YAAY,IAAI,CAAC,YAAY,CAACC,oCAAgB,EAAE,YAAY,CAAC;AAC7D,YAAY,IAAI,CAAC,YAAY,CAACC,sCAAkB,EAAE,iBAAiB,CAAC;;AAEpE,YAAY,aAAa,CAAC,OAAQ,GAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC;AACnF,YAAY,aAAa,CAAC,MAAO,GAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;;AAEhF,YAAY,IAAI;AAChB,cAAc,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;AAC3E,aAAc,CAAA,OAAO,KAAK,EAAE;AAC5B,cAAc,IAAI,CAAC,SAAS,CAAC;AAC7B,gBAAgB,IAAI,EAAEb,sBAAiB;AACvC,eAAe,CAAC;AAChB,cAAc,IAAI,CAAC,GAAG,EAAE;AACxB,cAAc,MAAM,KAAK,CAAA;AACzB;AACA,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK,CAAC;;AAEN,IAAI,OAAO,aAAa;AACxB;;AAEA;AACA;AACA;AACA;AACA,GAAU,gBAAgB,CAAC,UAAU,EAAY;AACjD,IAAI,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE;AACjC,MAAM,KAAK,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,KAA6D;AACpH,QAAQ,MAAM,YAAa,GAAE,cAAc,CAAC,CAAC,CAAC,EAAE,QAAS,IAAG,oBAAoB;AAChF,QAAQ,MAAM,YAAA,GAAe,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAA,IAAK,gBAAgB;AAC7E,QAAQ,MAAM,YAAA,GAAe,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAA,IAAK,gBAAgB;;AAE7E,QAAQ,MAAM,KAAA,GAAQK,oBAAe,EAAE;AACvC,QAAQ,KAAK,CAAC,UAAU,CAAC,sBAAsB,EAAE;AACjD,UAAU,iBAAiB,EAAE,YAAY;AACzC,UAAU,mBAAmB,EAAE,YAAY;AAC3C,UAAU,gBAAgB,EAAE,YAAY;AACxC,SAAS,CAAC;;AAEV,QAAQ,OAAO,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,CAAC;AACvE,OAAO;AACP,KAAK,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA,GAAU,iBAAiB,CAAC,QAAQ,EAA8B;AAClE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,mBAAmB;AAChC;;AAEA,IAAI;AACJ,MAAM;AACN,SAAS,OAAO,CAAC,MAAM,EAAE,GAAG;AAC5B,SAAS,IAAI,EAAC;AACd,SAAS,SAAS,CAAC,CAAC,EAAE,IAAI,CAAA;AAC1B,SAAS,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAA;AACtC,SAAS,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAA;AACxC,SAAS,OAAO,CAAC,OAAO,EAAE,EAAE,CAAA;AAC5B,SAAS,OAAO,CAAC,UAAU,EAAE,GAAG,CAAA;AAChC;AACA,SAAS,OAAO,CAAC,MAAM,EAAE,GAAG;AAC5B;AACA;AACA,SAAS,OAAO,CAAC,sCAAsC,EAAE,QAAQ;AACjE;AACA;AACA;;AAEA,MAAM,sBAAuB,IAAG,MAAM;AACtC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,SAAS,GAAG;AAChB,MAAM,oBAAoB,EAAE;AAC5B,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEa,qBAAsB,GAAES,sBAAiB,CAAC,sBAAsB;;;;;;"}