{"version": 3, "file": "fetch.js", "sources": ["../../src/fetch.ts"], "sourcesContent": ["import { getClient } from './currentScopes';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from './semanticAttributes';\nimport { setHttpStatus, SPAN_STATUS_ERROR, startInactiveSpan } from './tracing';\nimport { SentryNonRecordingSpan } from './tracing/sentryNonRecordingSpan';\nimport type { FetchBreadcrumbHint } from './types-hoist/breadcrumb';\nimport type { HandlerDataFetch } from './types-hoist/instrument';\nimport type { Span, SpanAttributes, SpanOrigin } from './types-hoist/span';\nimport { SENTRY_BAGGAGE_KEY_PREFIX } from './utils/baggage';\nimport { hasSpansEnabled } from './utils/hasSpansEnabled';\nimport { isInstanceOf, isRequest } from './utils/is';\nimport { getActiveSpan } from './utils/spanUtils';\nimport { getTraceData } from './utils/traceData';\nimport { getSanitizedUrlStringFromUrlObject, isURLObjectRelative, parseStringToURLObject } from './utils/url';\n\ntype PolymorphicRequestHeaders =\n  | Record<string, string | undefined>\n  | Array<[string, string]>\n  // the below is not precisely the Header type used in Request, but it'll pass duck-typing\n  | {\n      append: (key: string, value: string) => void;\n      get: (key: string) => string | null | undefined;\n    };\n\n/**\n * Create and track fetch request spans for usage in combination with `addFetchInstrumentationHandler`.\n *\n * @returns Span if a span was created, otherwise void.\n */\nexport function instrumentFetchRequest(\n  handlerData: HandlerDataFetch,\n  shouldCreateSpan: (url: string) => boolean,\n  shouldAttachHeaders: (url: string) => boolean,\n  spans: Record<string, Span>,\n  spanOrigin: SpanOrigin = 'auto.http.browser',\n): Span | undefined {\n  if (!handlerData.fetchData) {\n    return undefined;\n  }\n\n  const { method, url } = handlerData.fetchData;\n\n  const shouldCreateSpanResult = hasSpansEnabled() && shouldCreateSpan(url);\n\n  if (handlerData.endTimestamp && shouldCreateSpanResult) {\n    const spanId = handlerData.fetchData.__span;\n    if (!spanId) return;\n\n    const span = spans[spanId];\n    if (span) {\n      endSpan(span, handlerData);\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[spanId];\n    }\n    return undefined;\n  }\n\n  const hasParent = !!getActiveSpan();\n\n  const span =\n    shouldCreateSpanResult && hasParent\n      ? startInactiveSpan(getSpanStartOptions(url, method, spanOrigin))\n      : new SentryNonRecordingSpan();\n\n  handlerData.fetchData.__span = span.spanContext().spanId;\n  spans[span.spanContext().spanId] = span;\n\n  if (shouldAttachHeaders(handlerData.fetchData.url)) {\n    const request: string | Request = handlerData.args[0];\n\n    const options: { [key: string]: unknown } = handlerData.args[1] || {};\n\n    const headers = _addTracingHeadersToFetchRequest(\n      request,\n      options,\n      // If performance is disabled (TWP) or there's no active root span (pageload/navigation/interaction),\n      // we do not want to use the span as base for the trace headers,\n      // which means that the headers will be generated from the scope and the sampling decision is deferred\n      hasSpansEnabled() && hasParent ? span : undefined,\n    );\n    if (headers) {\n      // Ensure this is actually set, if no options have been passed previously\n      handlerData.args[1] = options;\n      options.headers = headers;\n    }\n  }\n\n  const client = getClient();\n\n  if (client) {\n    const fetchHint = {\n      input: handlerData.args,\n      response: handlerData.response,\n      startTimestamp: handlerData.startTimestamp,\n      endTimestamp: handlerData.endTimestamp,\n    } satisfies FetchBreadcrumbHint;\n\n    client.emit('beforeOutgoingRequestSpan', span, fetchHint);\n  }\n\n  return span;\n}\n\n/**\n * Adds sentry-trace and baggage headers to the various forms of fetch headers.\n * exported only for testing purposes\n *\n * When we determine if we should add a baggage header, there are 3 cases:\n * 1. No previous baggage header -> add baggage\n * 2. Previous baggage header has no sentry baggage values -> add our baggage\n * 3. Previous baggage header has sentry baggage values -> do nothing (might have been added manually by users)\n */\n// eslint-disable-next-line complexity -- yup it's this complicated :(\nexport function _addTracingHeadersToFetchRequest(\n  request: string | Request,\n  fetchOptionsObj: {\n    headers?:\n      | {\n          [key: string]: string[] | string | undefined;\n        }\n      | PolymorphicRequestHeaders;\n  },\n  span?: Span,\n): PolymorphicRequestHeaders | undefined {\n  const traceHeaders = getTraceData({ span });\n  const sentryTrace = traceHeaders['sentry-trace'];\n  const baggage = traceHeaders.baggage;\n\n  // Nothing to do, when we return undefined here, the original headers will be used\n  if (!sentryTrace) {\n    return undefined;\n  }\n\n  const originalHeaders = fetchOptionsObj.headers || (isRequest(request) ? request.headers : undefined);\n\n  if (!originalHeaders) {\n    return { ...traceHeaders };\n  } else if (isHeaders(originalHeaders)) {\n    const newHeaders = new Headers(originalHeaders);\n\n    // We don't want to override manually added sentry headers\n    if (!newHeaders.get('sentry-trace')) {\n      newHeaders.set('sentry-trace', sentryTrace);\n    }\n\n    if (baggage) {\n      const prevBaggageHeader = newHeaders.get('baggage');\n\n      if (!prevBaggageHeader) {\n        newHeaders.set('baggage', baggage);\n      } else if (!baggageHeaderHasSentryBaggageValues(prevBaggageHeader)) {\n        newHeaders.set('baggage', `${prevBaggageHeader},${baggage}`);\n      }\n    }\n\n    return newHeaders;\n  } else if (Array.isArray(originalHeaders)) {\n    const newHeaders = [...originalHeaders];\n\n    if (!originalHeaders.find(header => header[0] === 'sentry-trace')) {\n      newHeaders.push(['sentry-trace', sentryTrace]);\n    }\n\n    const prevBaggageHeaderWithSentryValues = originalHeaders.find(\n      header => header[0] === 'baggage' && baggageHeaderHasSentryBaggageValues(header[1]),\n    );\n\n    if (baggage && !prevBaggageHeaderWithSentryValues) {\n      // If there are multiple entries with the same key, the browser will merge the values into a single request header.\n      // Its therefore safe to simply push a \"baggage\" entry, even though there might already be another baggage header.\n      newHeaders.push(['baggage', baggage]);\n    }\n\n    return newHeaders as PolymorphicRequestHeaders;\n  } else {\n    const existingSentryTraceHeader = 'sentry-trace' in originalHeaders ? originalHeaders['sentry-trace'] : undefined;\n\n    const existingBaggageHeader = 'baggage' in originalHeaders ? originalHeaders.baggage : undefined;\n    const newBaggageHeaders: string[] = existingBaggageHeader\n      ? Array.isArray(existingBaggageHeader)\n        ? [...existingBaggageHeader]\n        : [existingBaggageHeader]\n      : [];\n\n    const prevBaggageHeaderWithSentryValues =\n      existingBaggageHeader &&\n      (Array.isArray(existingBaggageHeader)\n        ? existingBaggageHeader.find(headerItem => baggageHeaderHasSentryBaggageValues(headerItem))\n        : baggageHeaderHasSentryBaggageValues(existingBaggageHeader));\n\n    if (baggage && !prevBaggageHeaderWithSentryValues) {\n      newBaggageHeaders.push(baggage);\n    }\n\n    return {\n      ...(originalHeaders as Exclude<typeof originalHeaders, Headers>),\n      'sentry-trace': (existingSentryTraceHeader as string | undefined) ?? sentryTrace,\n      baggage: newBaggageHeaders.length > 0 ? newBaggageHeaders.join(',') : undefined,\n    };\n  }\n}\n\nfunction endSpan(span: Span, handlerData: HandlerDataFetch): void {\n  if (handlerData.response) {\n    setHttpStatus(span, handlerData.response.status);\n\n    const contentLength = handlerData.response?.headers && handlerData.response.headers.get('content-length');\n\n    if (contentLength) {\n      const contentLengthNum = parseInt(contentLength);\n      if (contentLengthNum > 0) {\n        span.setAttribute('http.response_content_length', contentLengthNum);\n      }\n    }\n  } else if (handlerData.error) {\n    span.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n  }\n  span.end();\n}\n\nfunction baggageHeaderHasSentryBaggageValues(baggageHeader: string): boolean {\n  return baggageHeader.split(',').some(baggageEntry => baggageEntry.trim().startsWith(SENTRY_BAGGAGE_KEY_PREFIX));\n}\n\nfunction isHeaders(headers: unknown): headers is Headers {\n  return typeof Headers !== 'undefined' && isInstanceOf(headers, Headers);\n}\n\nfunction getSpanStartOptions(\n  url: string,\n  method: string,\n  spanOrigin: SpanOrigin,\n): Parameters<typeof startInactiveSpan>[0] {\n  const parsedUrl = parseStringToURLObject(url);\n  return {\n    name: parsedUrl ? `${method} ${getSanitizedUrlStringFromUrlObject(parsedUrl)}` : method,\n    attributes: getFetchSpanAttributes(url, parsedUrl, method, spanOrigin),\n  };\n}\n\nfunction getFetchSpanAttributes(\n  url: string,\n  parsedUrl: ReturnType<typeof parseStringToURLObject>,\n  method: string,\n  spanOrigin: SpanOrigin,\n): SpanAttributes {\n  const attributes: SpanAttributes = {\n    url,\n    type: 'fetch',\n    'http.method': method,\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: spanOrigin,\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'http.client',\n  };\n  if (parsedUrl) {\n    if (!isURLObjectRelative(parsedUrl)) {\n      attributes['http.url'] = parsedUrl.href;\n      attributes['server.address'] = parsedUrl.host;\n    }\n    if (parsedUrl.search) {\n      attributes['http.query'] = parsedUrl.search;\n    }\n    if (parsedUrl.hash) {\n      attributes['http.fragment'] = parsedUrl.hash;\n    }\n  }\n  return attributes;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAuBA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB;AACtC,EAAE,WAAW;AACb,EAAE,gBAAgB;AAClB,EAAE,mBAAmB;AACrB,EAAE,KAAK;AACP,EAAE,UAAU,GAAe,mBAAmB;AAC9C,EAAoB;AACpB,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;AAC9B,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,MAAM,EAAE,MAAM,EAAE,KAAM,GAAE,WAAW,CAAC,SAAS;;AAE/C,EAAE,MAAM,sBAAuB,GAAE,eAAe,MAAM,gBAAgB,CAAC,GAAG,CAAC;;AAE3E,EAAE,IAAI,WAAW,CAAC,YAAa,IAAG,sBAAsB,EAAE;AAC1D,IAAI,MAAM,MAAO,GAAE,WAAW,CAAC,SAAS,CAAC,MAAM;AAC/C,IAAI,IAAI,CAAC,MAAM,EAAE;;AAEjB,IAAI,MAAM,IAAK,GAAE,KAAK,CAAC,MAAM,CAAC;AAC9B,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC;;AAEhC;AACA,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC;AAC1B;AACA,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,MAAM,SAAU,GAAE,CAAC,CAAC,aAAa,EAAE;;AAErC,EAAE,MAAM,IAAK;AACb,IAAI,0BAA0B;AAC9B,QAAQ,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC;AACtE,QAAQ,IAAI,sBAAsB,EAAE;;AAEpC,EAAE,WAAW,CAAC,SAAS,CAAC,MAAO,GAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;AAC1D,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAE,GAAE,IAAI;;AAEzC,EAAE,IAAI,mBAAmB,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;AACtD,IAAI,MAAM,OAAO,GAAqB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;;AAEzD,IAAI,MAAM,OAAO,GAA+B,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA,IAAK,EAAE;;AAEzE,IAAI,MAAM,OAAQ,GAAE,gCAAgC;AACpD,MAAM,OAAO;AACb,MAAM,OAAO;AACb;AACA;AACA;AACA,MAAM,eAAe,EAAG,IAAG,YAAY,IAAA,GAAO,SAAS;AACvD,KAAK;AACL,IAAI,IAAI,OAAO,EAAE;AACjB;AACA,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA,GAAI,OAAO;AACnC,MAAM,OAAO,CAAC,OAAQ,GAAE,OAAO;AAC/B;AACA;;AAEA,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;;AAE5B,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,YAAY;AACtB,MAAM,KAAK,EAAE,WAAW,CAAC,IAAI;AAC7B,MAAM,QAAQ,EAAE,WAAW,CAAC,QAAQ;AACpC,MAAM,cAAc,EAAE,WAAW,CAAC,cAAc;AAChD,MAAM,YAAY,EAAE,WAAW,CAAC,YAAY;AAC5C,KAAM;;AAEN,IAAI,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,IAAI,EAAE,SAAS,CAAC;AAC7D;;AAEA,EAAE,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gCAAgC;AAChD,EAAE,OAAO;AACT,EAAE;;AAMA;AACF,EAAE,IAAI;AACN,EAAyC;AACzC,EAAE,MAAM,eAAe,YAAY,CAAC,EAAE,IAAA,EAAM,CAAC;AAC7C,EAAE,MAAM,WAAY,GAAE,YAAY,CAAC,cAAc,CAAC;AAClD,EAAE,MAAM,OAAA,GAAU,YAAY,CAAC,OAAO;;AAEtC;AACA,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,MAAM,eAAgB,GAAE,eAAe,CAAC,OAAA,KAAY,SAAS,CAAC,OAAO,IAAI,OAAO,CAAC,OAAQ,GAAE,SAAS,CAAC;;AAEvG,EAAE,IAAI,CAAC,eAAe,EAAE;AACxB,IAAI,OAAO,EAAE,GAAG,YAAA,EAAc;AAC9B,GAAE,MAAO,IAAI,SAAS,CAAC,eAAe,CAAC,EAAE;AACzC,IAAI,MAAM,UAAW,GAAE,IAAI,OAAO,CAAC,eAAe,CAAC;;AAEnD;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACzC,MAAM,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;AACjD;;AAEA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,oBAAoB,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;;AAEzD,MAAM,IAAI,CAAC,iBAAiB,EAAE;AAC9B,QAAQ,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC;AAC1C,OAAM,MAAO,IAAI,CAAC,mCAAmC,CAAC,iBAAiB,CAAC,EAAE;AAC1E,QAAQ,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,EAAA,iBAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA;AACA;;AAEA,IAAA,OAAA,UAAA;AACA,GAAA,MAAA,IAAA,KAAA,CAAA,OAAA,CAAA,eAAA,CAAA,EAAA;AACA,IAAA,MAAA,UAAA,GAAA,CAAA,GAAA,eAAA,CAAA;;AAEA,IAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,KAAA,cAAA,CAAA,EAAA;AACA,MAAA,UAAA,CAAA,IAAA,CAAA,CAAA,cAAA,EAAA,WAAA,CAAA,CAAA;AACA;;AAEA,IAAA,MAAA,iCAAA,GAAA,eAAA,CAAA,IAAA;AACA,MAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,KAAA,SAAA,IAAA,mCAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA,KAAA;;AAEA,IAAA,IAAA,OAAA,IAAA,CAAA,iCAAA,EAAA;AACA;AACA;AACA,MAAA,UAAA,CAAA,IAAA,CAAA,CAAA,SAAA,EAAA,OAAA,CAAA,CAAA;AACA;;AAEA,IAAA,OAAA,UAAA;AACA,GAAA,MAAA;AACA,IAAA,MAAA,yBAAA,GAAA,cAAA,IAAA,eAAA,GAAA,eAAA,CAAA,cAAA,CAAA,GAAA,SAAA;;AAEA,IAAA,MAAA,qBAAA,GAAA,SAAA,IAAA,eAAA,GAAA,eAAA,CAAA,OAAA,GAAA,SAAA;AACA,IAAA,MAAA,iBAAA,GAAA;AACA,QAAA,KAAA,CAAA,OAAA,CAAA,qBAAA;AACA,UAAA,CAAA,GAAA,qBAAA;AACA,UAAA,CAAA,qBAAA;AACA,QAAA,EAAA;;AAEA,IAAA,MAAA,iCAAA;AACA,MAAA,qBAAA;AACA,OAAA,KAAA,CAAA,OAAA,CAAA,qBAAA;AACA,UAAA,qBAAA,CAAA,IAAA,CAAA,UAAA,IAAA,mCAAA,CAAA,UAAA,CAAA;AACA,UAAA,mCAAA,CAAA,qBAAA,CAAA,CAAA;;AAEA,IAAA,IAAA,OAAA,IAAA,CAAA,iCAAA,EAAA;AACA,MAAA,iBAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA;;AAEA,IAAA,OAAA;AACA,MAAA,IAAA,eAAA,EAAA;AACA,MAAA,cAAA,EAAA,CAAA,yBAAA,MAAA,WAAA;AACA,MAAA,OAAA,EAAA,iBAAA,CAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,SAAA;AACA,KAAA;AACA;AACA;;AAEA,SAAA,OAAA,CAAA,IAAA,EAAA,WAAA,EAAA;AACA,EAAA,IAAA,WAAA,CAAA,QAAA,EAAA;AACA,IAAA,aAAA,CAAA,IAAA,EAAA,WAAA,CAAA,QAAA,CAAA,MAAA,CAAA;;AAEA,IAAA,MAAA,aAAA,GAAA,WAAA,CAAA,QAAA,EAAA,OAAA,IAAA,WAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,CAAA;;AAEA,IAAA,IAAA,aAAA,EAAA;AACA,MAAA,MAAA,gBAAA,GAAA,QAAA,CAAA,aAAA,CAAA;AACA,MAAA,IAAA,gBAAA,GAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,YAAA,CAAA,8BAAA,EAAA,gBAAA,CAAA;AACA;AACA;AACA,GAAA,MAAA,IAAA,WAAA,CAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,GAAA,EAAA;AACA;;AAEA,SAAA,mCAAA,CAAA,aAAA,EAAA;AACA,EAAA,OAAA,aAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,YAAA,IAAA,YAAA,CAAA,IAAA,EAAA,CAAA,UAAA,CAAA,yBAAA,CAAA,CAAA;AACA;;AAEA,SAAA,SAAA,CAAA,OAAA,EAAA;AACA,EAAA,OAAA,OAAA,OAAA,KAAA,WAAA,IAAA,YAAA,CAAA,OAAA,EAAA,OAAA,CAAA;AACA;;AAEA,SAAA,mBAAA;AACA,EAAA,GAAA;AACA,EAAA,MAAA;AACA,EAAA,UAAA;AACA,EAAA;AACA,EAAA,MAAA,SAAA,GAAA,sBAAA,CAAA,GAAA,CAAA;AACA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,SAAA,GAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,kCAAA,CAAA,SAAA,CAAA,CAAA,CAAA,GAAA,MAAA;AACA,IAAA,UAAA,EAAA,sBAAA,CAAA,GAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,CAAA;AACA,GAAA;AACA;;AAEA,SAAA,sBAAA;AACA,EAAA,GAAA;AACA,EAAA,SAAA;AACA,EAAA,MAAA;AACA,EAAA,UAAA;AACA,EAAA;AACA,EAAA,MAAA,UAAA,GAAA;AACA,IAAA,GAAA;AACA,IAAA,IAAA,EAAA,OAAA;AACA,IAAA,aAAA,EAAA,MAAA;AACA,IAAA,CAAA,gCAAA,GAAA,UAAA;AACA,IAAA,CAAA,4BAAA,GAAA,aAAA;AACA,GAAA;AACA,EAAA,IAAA,SAAA,EAAA;AACA,IAAA,IAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAA,UAAA,CAAA,UAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA,MAAA,UAAA,CAAA,gBAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,MAAA,EAAA;AACA,MAAA,UAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,MAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACA,MAAA,UAAA,CAAA,eAAA,CAAA,GAAA,SAAA,CAAA,IAAA;AACA;AACA;AACA,EAAA,OAAA,UAAA;AACA;;;;"}