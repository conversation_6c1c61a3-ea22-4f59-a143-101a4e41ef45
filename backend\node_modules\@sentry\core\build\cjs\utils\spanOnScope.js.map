{"version": 3, "file": "spanOnScope.js", "sources": ["../../../src/utils/spanOnScope.ts"], "sourcesContent": ["import type { Scope } from '../scope';\nimport type { Span } from '../types-hoist/span';\nimport { addNonEnumerableProperty } from '../utils/object';\n\nconst SCOPE_SPAN_FIELD = '_sentrySpan';\n\ntype ScopeWithMaybeSpan = Scope & {\n  [SCOPE_SPAN_FIELD]?: Span;\n};\n\n/**\n * Set the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nexport function _setSpanForScope(scope: Scope, span: Span | undefined): void {\n  if (span) {\n    addNonEnumerableProperty(scope as ScopeWithMaybeSpan, SCOPE_SPAN_FIELD, span);\n  } else {\n    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n    delete (scope as ScopeWithMaybeSpan)[SCOPE_SPAN_FIELD];\n  }\n}\n\n/**\n * Get the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nexport function _getSpanForScope(scope: ScopeWithMaybeSpan): Span | undefined {\n  return scope[SCOPE_SPAN_FIELD];\n}\n"], "names": ["addNonEnumerableProperty"], "mappings": ";;;;AAIA,MAAM,gBAAA,GAAmB,aAAa;;AAMtC;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,KAAK,EAAS,IAAI,EAA0B;AAC7E,EAAE,IAAI,IAAI,EAAE;AACZ,IAAIA,+BAAwB,CAAC,KAAA,GAA6B,gBAAgB,EAAE,IAAI,CAAC;AACjF,SAAS;AACT;AACA,IAAI,OAAO,CAAC,KAAA,GAA6B,gBAAgB,CAAC;AAC1D;AACA;;AAEA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,KAAK,EAAwC;AAC9E,EAAE,OAAO,KAAK,CAAC,gBAAgB,CAAC;AAChC;;;;;"}