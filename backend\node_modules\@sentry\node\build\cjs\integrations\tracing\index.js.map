{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/tracing/index.ts"], "sourcesContent": ["import type { Integration } from '@sentry/core';\nimport { instrumentOtelHttp } from '../http';\nimport { amqplibIntegration, instrumentAmqplib } from './amqplib';\nimport { connectIntegration, instrumentConnect } from './connect';\nimport { expressIntegration, instrumentExpress, instrumentExpressV5 } from './express';\nimport { fastifyIntegration, instrumentFastify, instrumentFastifyV3 } from './fastify';\nimport { genericPoolIntegration, instrumentGenericPool } from './genericPool';\nimport { graphqlIntegration, instrumentGraphql } from './graphql';\nimport { hapiIntegration, instrumentHapi } from './hapi';\nimport { instrumentKafka, kafkaIntegration } from './kafka';\nimport { instrumentKoa, koaIntegration } from './koa';\nimport { instrumentLruMemoizer, lruMemoizerIntegration } from './lrumemoizer';\nimport { instrumentMongo, mongoIntegration } from './mongo';\nimport { instrumentMongoose, mongooseIntegration } from './mongoose';\nimport { instrumentMysql, mysqlIntegration } from './mysql';\nimport { instrumentMysql2, mysql2Integration } from './mysql2';\nimport { instrumentPostgres, postgresIntegration } from './postgres';\nimport { instrumentPostgresJs, postgresJsIntegration } from './postgresjs';\nimport { prismaIntegration } from './prisma';\nimport { instrumentRedis, redisIntegration } from './redis';\nimport { instrumentTedious, tediousIntegration } from './tedious';\nimport { instrumentVercelAi, vercelAIIntegration } from './vercelai';\n\n/**\n * With OTEL, all performance integrations will be added, as OTEL only initializes them when the patched package is actually required.\n */\nexport function getAutoPerformanceIntegrations(): Integration[] {\n  return [\n    expressIntegration(),\n    fastifyIntegration(),\n    graphqlIntegration(),\n    mongoIntegration(),\n    mongooseIntegration(),\n    mysqlIntegration(),\n    mysql2Integration(),\n    redisIntegration(),\n    postgresIntegration(),\n    prismaIntegration(),\n    hapiIntegration(),\n    koaIntegration(),\n    connectIntegration(),\n    tediousIntegration(),\n    genericPoolIntegration(),\n    kafkaIntegration(),\n    amqplibIntegration(),\n    lruMemoizerIntegration(),\n    vercelAIIntegration(),\n    postgresJsIntegration(),\n  ];\n}\n\n/**\n * Get a list of methods to instrument OTEL, when preload instrumentation.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function getOpenTelemetryInstrumentationToPreload(): (((options?: any) => void) & { id: string })[] {\n  return [\n    instrumentOtelHttp,\n    instrumentExpress,\n    instrumentExpressV5,\n    instrumentConnect,\n    instrumentFastify,\n    instrumentFastifyV3,\n    instrumentHapi,\n    instrumentKafka,\n    instrumentKoa,\n    instrumentLruMemoizer,\n    instrumentMongo,\n    instrumentMongoose,\n    instrumentMysql,\n    instrumentMysql2,\n    instrumentPostgres,\n    instrumentHapi,\n    instrumentGraphql,\n    instrumentRedis,\n    instrumentTedious,\n    instrumentGenericPool,\n    instrumentAmqplib,\n    instrumentVercelAi,\n    instrumentPostgresJs,\n  ];\n}\n"], "names": ["expressIntegration", "fastifyIntegration", "graphqlIntegration", "mongoIntegration", "mongooseIntegration", "mysqlIntegration", "mysql2Integration", "redisIntegration", "postgresIntegration", "prismaIntegration", "hapiIntegration", "koaIntegration", "connectIntegration", "tediousIntegration", "genericPoolIntegration", "kafkaIntegration", "amqplibIntegration", "lruMemoizerIntegration", "vercelAIIntegration", "postgresJsIntegration", "instrumentOtelHttp", "instrumentExpress", "instrumentExpressV5", "instrumentConnect", "instrumentFastify", "instrumentFastifyV3", "instrumentHapi", "instrumentKafka", "instrumentKoa", "instrumentLruMemoizer", "instrumentMongo", "instrumentMongoose", "instrumentMysql", "instrumentMysql2", "instrumentPostgres", "instrumentGraphql", "instrumentRedis", "instrumentTedious", "instrumentGenericPool", "instrumentAmqplib", "instrumentVercelAi", "instrumentPostgresJs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;AACO,SAAS,8BAA8B,GAAkB;AAChE,EAAE,OAAO;AACT,IAAIA,0BAAkB,EAAE;AACxB,IAAIC,0BAAkB,EAAE;AACxB,IAAIC,0BAAkB,EAAE;AACxB,IAAIC,sBAAgB,EAAE;AACtB,IAAIC,4BAAmB,EAAE;AACzB,IAAIC,sBAAgB,EAAE;AACtB,IAAIC,wBAAiB,EAAE;AACvB,IAAIC,sBAAgB,EAAE;AACtB,IAAIC,4BAAmB,EAAE;AACzB,IAAIC,wBAAiB,EAAE;AACvB,IAAIC,uBAAe,EAAE;AACrB,IAAIC,kBAAc,EAAE;AACpB,IAAIC,0BAAkB,EAAE;AACxB,IAAIC,0BAAkB,EAAE;AACxB,IAAIC,kCAAsB,EAAE;AAC5B,IAAIC,sBAAgB,EAAE;AACtB,IAAIC,0BAAkB,EAAE;AACxB,IAAIC,kCAAsB,EAAE;AAC5B,IAAIC,2BAAmB,EAAE;AACzB,IAAIC,gCAAqB,EAAE;AAC3B,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACO,SAAS,wCAAwC,GAAmD;AAC3G,EAAE,OAAO;AACT,IAAIC,wBAAkB;AACtB,IAAIC,yBAAiB;AACrB,IAAIC,2BAAmB;AACvB,IAAIC,yBAAiB;AACrB,IAAIC,yBAAiB;AACrB,IAAIC,2BAAmB;AACvB,IAAIC,sBAAc;AAClB,IAAIC,qBAAe;AACnB,IAAIC,iBAAa;AACjB,IAAIC,iCAAqB;AACzB,IAAIC,qBAAe;AACnB,IAAIC,2BAAkB;AACtB,IAAIC,qBAAe;AACnB,IAAIC,uBAAgB;AACpB,IAAIC,2BAAkB;AACtB,IAAIR,sBAAc;AAClB,IAAIS,yBAAiB;AACrB,IAAIC,qBAAe;AACnB,IAAIC,yBAAiB;AACrB,IAAIC,iCAAqB;AACzB,IAAIC,yBAAiB;AACrB,IAAIC,0BAAkB;AACtB,IAAIC,+BAAoB;AACxB,GAAG;AACH;;;;;"}