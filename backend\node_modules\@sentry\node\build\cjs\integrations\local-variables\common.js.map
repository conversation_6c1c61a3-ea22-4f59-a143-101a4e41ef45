{"version": 3, "file": "common.js", "sources": ["../../../../src/integrations/local-variables/common.ts"], "sourcesContent": ["import type { Debugger } from 'node:inspector';\n\nexport type Variables = Record<string, unknown>;\n\nexport type RateLimitIncrement = () => void;\n\n/**\n * The key used to store the local variables on the error object.\n */\nexport const LOCAL_VARIABLES_KEY = '__SENTRY_ERROR_LOCAL_VARIABLES__';\n\n/**\n * Creates a rate limiter that will call the disable callback when the rate limit is reached and the enable callback\n * when a timeout has occurred.\n * @param maxPerSecond Maximum number of calls per second\n * @param enable Callback to enable capture\n * @param disable Callback to disable capture\n * @returns A function to call to increment the rate limiter count\n */\nexport function createRateLimiter(\n  maxPerSecond: number,\n  enable: () => void,\n  disable: (seconds: number) => void,\n): RateLimitIncrement {\n  let count = 0;\n  let retrySeconds = 5;\n  let disabledTimeout = 0;\n\n  setInterval(() => {\n    if (disabledTimeout === 0) {\n      if (count > maxPerSecond) {\n        retrySeconds *= 2;\n        disable(retrySeconds);\n\n        // Cap at one day\n        if (retrySeconds > 86400) {\n          retrySeconds = 86400;\n        }\n        disabledTimeout = retrySeconds;\n      }\n    } else {\n      disabledTimeout -= 1;\n\n      if (disabledTimeout === 0) {\n        enable();\n      }\n    }\n\n    count = 0;\n  }, 1_000).unref();\n\n  return () => {\n    count += 1;\n  };\n}\n\n// Add types for the exception event data\nexport type PausedExceptionEvent = Debugger.PausedEventDataType & {\n  data: {\n    // This contains error.stack\n    description: string;\n    objectId?: string;\n  };\n};\n\n/** Could this be an anonymous function? */\nexport function isAnonymous(name: string | undefined): boolean {\n  return name !== undefined && (name.length === 0 || name === '?' || name === '<anonymous>');\n}\n\n/** Do the function names appear to match? */\nexport function functionNamesMatch(a: string | undefined, b: string | undefined): boolean {\n  return a === b || `Object.${a}` === b || a === `Object.${b}` || (isAnonymous(a) && isAnonymous(b));\n}\n\nexport interface FrameVariables {\n  function: string;\n  vars?: Variables;\n}\n\nexport interface LocalVariablesIntegrationOptions {\n  /**\n   * Capture local variables for both caught and uncaught exceptions\n   *\n   * - When false, only uncaught exceptions will have local variables\n   * - When true, both caught and uncaught exceptions will have local variables.\n   *\n   * Defaults to `true`.\n   *\n   * Capturing local variables for all exceptions can be expensive since the debugger pauses for every throw to collect\n   * local variables.\n   *\n   * To reduce the likelihood of this feature impacting app performance or throughput, this feature is rate-limited.\n   * Once the rate limit is reached, local variables will only be captured for uncaught exceptions until a timeout has\n   * been reached.\n   */\n  captureAllExceptions?: boolean;\n  /**\n   * Maximum number of exceptions to capture local variables for per second before rate limiting is triggered.\n   */\n  maxExceptionsPerSecond?: number;\n}\n\nexport interface LocalVariablesWorkerArgs extends LocalVariablesIntegrationOptions {\n  /**\n   * Whether to enable debug logging.\n   */\n  debug: boolean;\n  /**\n   * Base path used to calculate module name.\n   *\n   * Defaults to `dirname(process.argv[1])` and falls back to `process.cwd()`\n   */\n  basePath?: string;\n}\n"], "names": [], "mappings": ";;AAMA;AACA;AACA;AACO,MAAM,mBAAoB,GAAE;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB;AACjC,EAAE,YAAY;AACd,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAsB;AACtB,EAAE,IAAI,KAAM,GAAE,CAAC;AACf,EAAE,IAAI,YAAa,GAAE,CAAC;AACtB,EAAE,IAAI,eAAgB,GAAE,CAAC;;AAEzB,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,IAAI,eAAgB,KAAI,CAAC,EAAE;AAC/B,MAAM,IAAI,KAAM,GAAE,YAAY,EAAE;AAChC,QAAQ,YAAA,IAAgB,CAAC;AACzB,QAAQ,OAAO,CAAC,YAAY,CAAC;;AAE7B;AACA,QAAQ,IAAI,YAAa,GAAE,KAAK,EAAE;AAClC,UAAU,YAAA,GAAe,KAAK;AAC9B;AACA,QAAQ,eAAA,GAAkB,YAAY;AACtC;AACA,WAAW;AACX,MAAM,eAAA,IAAmB,CAAC;;AAE1B,MAAM,IAAI,eAAgB,KAAI,CAAC,EAAE;AACjC,QAAQ,MAAM,EAAE;AAChB;AACA;;AAEA,IAAI,KAAA,GAAQ,CAAC;AACb,GAAG,EAAE,IAAK,CAAC,CAAC,KAAK,EAAE;;AAEnB,EAAE,OAAO,MAAM;AACf,IAAI,KAAA,IAAS,CAAC;AACd,GAAG;AACH;;AAEA;;AASA;AACO,SAAS,WAAW,CAAC,IAAI,EAA+B;AAC/D,EAAE,OAAO,IAAK,KAAI,cAAc,IAAI,CAAC,MAAA,KAAW,CAAE,IAAG,SAAS,GAAA,IAAO,IAAK,KAAI,aAAa,CAAC;AAC5F;;AAEA;AACO,SAAS,kBAAkB,CAAC,CAAC,EAAsB,CAAC,EAA+B;AAC1F,EAAE,OAAO,CAAE,KAAI,CAAE,IAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,KAAA,WAAA,CAAA,CAAA,CAAA,IAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AACA;;;;;;;"}