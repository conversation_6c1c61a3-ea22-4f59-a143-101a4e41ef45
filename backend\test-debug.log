2025-07-03T05:29:12.661Z - MINIMAL TEST: Starting test suite
2025-07-03T05:29:12.666Z - MINIMAL TEST: Connecting to MongoDB
2025-07-03T05:29:13.095Z - MINIMAL TEST: Connection state: 1
2025-07-03T05:29:13.095Z - MINIMAL TEST: Connected successfully
2025-07-03T05:29:13.098Z - MINIMAL TEST: Running basic test
2025-07-03T05:29:13.102Z - MINIMAL TEST: Checking connection state
2025-07-03T05:29:13.103Z - Current connection state: 1
2025-07-03T05:29:13.104Z - MINIMAL TEST: Disconnecting from MongoDB
2025-07-03T05:29:13.110Z - MINIMAL TEST: Disconnected successfully
2025-07-03T05:32:41.782Z - AUTH TEST: Starting auth controller test suite
2025-07-03T05:32:41.787Z - AUTH TEST: Connecting to MongoDB
2025-07-03T05:32:43.865Z - AUTH TEST: Connection failed: MongoNetworkError: 600F0000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\ws\deps\openssl\openssl\ssl\record\rec_layer_s3.c:1605:SSL alert number 80

2025-07-03T05:33:14.495Z - AUTH TEST: Starting auth controller test suite
2025-07-03T05:33:14.500Z - AUTH TEST: Connecting to MongoDB
2025-07-03T05:33:17.290Z - AUTH TEST: Connection state: 1
2025-07-03T05:33:17.290Z - AUTH TEST: Connected successfully
2025-07-03T05:33:17.294Z - AUTH TEST: Testing registerUser with valid data
2025-07-03T05:33:20.320Z - AUTH TEST: Testing registerUser with missing fields
2025-07-03T05:33:20.322Z - AUTH TEST: registerUser test with missing fields passed
2025-07-03T05:33:20.362Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-03T05:33:20.788Z - AUTH TEST: registerUser test with duplicate email passed
2025-07-03T05:33:20.829Z - AUTH TEST: Registering test user for loginUser tests
2025-07-03T05:33:21.167Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-03T05:33:21.280Z - AUTH TEST: Registering test user for loginUser tests
2025-07-03T05:33:21.607Z - AUTH TEST: Testing loginUser with invalid password
2025-07-03T05:33:21.689Z - AUTH TEST: Registering test user for loginUser tests
2025-07-03T05:33:21.980Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-03T05:33:22.017Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-03T05:33:22.056Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:33:22.472Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:33:22.868Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:33:23.271Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:33:23.643Z - AUTH TEST: Disconnecting from MongoDB
2025-07-03T05:33:23.695Z - AUTH TEST: Disconnected successfully
2025-07-03T05:33:40.857Z - AUTH TEST: Starting auth controller test suite
2025-07-03T05:33:40.862Z - AUTH TEST: Connecting to MongoDB
2025-07-03T05:33:40.867Z - AUTH TEST: Connection failed: MongoAPIError: The 'tlsInsecure' option cannot be used with the 'tlsAllowInvalidCertificates' option
2025-07-03T05:35:21.052Z - AUTH TEST: Disconnecting from MongoDB
2025-07-03T05:35:21.053Z - AUTH TEST: Disconnected successfully
2025-07-03T05:35:30.800Z - MINIMAL TEST: Starting test suite
2025-07-03T05:35:30.801Z - MINIMAL TEST: Connecting to MongoDB
2025-07-03T05:35:33.348Z - MINIMAL TEST: Connection failed: MongoNetworkError: 64500000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\ws\deps\openssl\openssl\ssl\record\rec_layer_s3.c:1605:SSL alert number 80

2025-07-03T05:35:33.351Z - MINIMAL TEST: Disconnecting from MongoDB
2025-07-03T05:35:33.353Z - MINIMAL TEST: Disconnected successfully
2025-07-03T05:35:40.729Z - AUTH TEST: Starting auth controller test suite
2025-07-03T05:35:40.735Z - AUTH TEST: Connecting to MongoDB
2025-07-03T05:35:40.740Z - AUTH TEST: Connection failed: MongoAPIError: The 'tlsInsecure' option cannot be used with the 'tlsAllowInvalidCertificates' option
2025-07-03T05:37:20.918Z - AUTH TEST: Disconnecting from MongoDB
2025-07-03T05:37:20.919Z - AUTH TEST: Disconnected successfully
2025-07-03T05:41:17.399Z - AUTH TEST: Starting auth controller test suite
2025-07-03T05:41:17.404Z - AUTH TEST: Connecting to MongoDB
2025-07-03T05:41:17.904Z - AUTH TEST: Connection state: 1
2025-07-03T05:41:17.904Z - AUTH TEST: Connected successfully
2025-07-03T05:41:17.907Z - AUTH TEST: Testing registerUser with valid data
2025-07-03T05:41:20.210Z - AUTH TEST: Testing registerUser with missing fields
2025-07-03T05:41:20.212Z - AUTH TEST: registerUser test with missing fields passed
2025-07-03T05:41:20.984Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-03T05:41:23.622Z - AUTH TEST: registerUser test with duplicate email passed
2025-07-03T05:41:23.688Z - AUTH TEST: Registering test user for loginUser tests
2025-07-03T05:41:23.978Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-03T05:41:24.045Z - AUTH TEST: Registering test user for loginUser tests
2025-07-03T05:41:24.372Z - AUTH TEST: Testing loginUser with invalid password
2025-07-03T05:41:24.448Z - AUTH TEST: Registering test user for loginUser tests
2025-07-03T05:41:24.743Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-03T05:41:24.771Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-03T05:41:24.803Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:41:25.168Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:41:25.512Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:41:25.889Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-03T05:41:26.228Z - AUTH TEST: Disconnecting from MongoDB
2025-07-03T05:41:26.286Z - AUTH TEST: Disconnected successfully
2025-07-03T05:41:29.562Z - MINIMAL TEST: Starting test suite
2025-07-03T05:41:29.564Z - MINIMAL TEST: Connecting to MongoDB
2025-07-03T05:41:31.227Z - MINIMAL TEST: Connection state: 1
2025-07-03T05:41:31.228Z - MINIMAL TEST: Connected successfully
2025-07-03T05:41:31.231Z - MINIMAL TEST: Running basic test
2025-07-03T05:41:31.235Z - MINIMAL TEST: Checking connection state
2025-07-03T05:41:31.236Z - Current connection state: 1
2025-07-03T05:41:31.238Z - MINIMAL TEST: Disconnecting from MongoDB
2025-07-03T05:41:31.295Z - MINIMAL TEST: Disconnected successfully
2025-07-03T05:42:22.375Z - GLOBAL SETUP ERROR: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
2025-07-03T05:42:44.801Z - GLOBAL SETUP ERROR: MongooseError: The `uri` parameter to `openUri()` must be a string, got "undefined". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
2025-07-03T05:47:12.406Z - AUTH TEST: Starting auth controller test suite
2025-07-03T05:47:12.411Z - AUTH TEST: Connecting to MongoDB
2025-07-03T05:47:13.275Z - AUTH TEST: Connection failed: MongoNetworkError: 20780000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\ws\deps\openssl\openssl\ssl\record\rec_layer_s3.c:1605:SSL alert number 80

2025-07-04T09:30:22.998Z - AUTH TEST: Starting auth controller test suite
2025-07-04T09:30:23.019Z - AUTH TEST: Connecting to MongoDB
2025-07-04T09:30:31.786Z - AUTH TEST: Connection failed: MongooseServerSelectionError: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/
2025-07-04T09:30:31.848Z - AUTH TEST: Disconnecting from MongoDB
2025-07-04T09:30:31.862Z - AUTH TEST: Disconnected successfully
2025-07-04T09:30:35.134Z - MINIMAL TEST: Starting test suite
2025-07-04T09:30:35.136Z - MINIMAL TEST: Connecting to MongoDB
2025-07-04T09:30:35.524Z - MINIMAL TEST: Connection failed: MongoNetworkError: C87B0000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\ws\deps\openssl\openssl\ssl\record\rec_layer_s3.c:1605:SSL alert number 80

2025-07-04T09:30:35.528Z - MINIMAL TEST: Disconnecting from MongoDB
2025-07-04T09:30:35.533Z - MINIMAL TEST: Disconnected successfully
2025-07-04T09:31:16.306Z - AUTH TEST: Starting auth controller test suite
2025-07-04T09:31:16.316Z - AUTH TEST: Connecting to MongoDB
2025-07-04T09:31:24.797Z - AUTH TEST: Connection failed: MongooseServerSelectionError: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/
2025-07-04T09:31:24.858Z - AUTH TEST: Disconnecting from MongoDB
2025-07-04T09:31:24.865Z - AUTH TEST: Disconnected successfully
2025-07-04T09:31:25.268Z - MINIMAL TEST: Starting test suite
2025-07-04T09:31:25.270Z - MINIMAL TEST: Connecting to MongoDB
2025-07-04T09:31:26.668Z - MINIMAL TEST: Connection failed: MongoNetworkError: EC380000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\ws\deps\openssl\openssl\ssl\record\rec_layer_s3.c:1605:SSL alert number 80

2025-07-04T09:31:26.673Z - MINIMAL TEST: Disconnecting from MongoDB
2025-07-04T09:31:26.675Z - MINIMAL TEST: Disconnected successfully
2025-07-08T08:44:11.416Z - AUTH TEST: Starting auth controller test suite
2025-07-08T08:44:11.430Z - AUTH TEST: Connecting to MongoDB
2025-07-08T08:44:19.941Z - AUTH TEST: Connection failed: MongooseServerSelectionError: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/
2025-07-08T08:44:19.998Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T08:44:20.016Z - AUTH TEST: Disconnected successfully
2025-07-08T08:53:04.806Z - AUTH TEST: Starting auth controller test suite
2025-07-08T08:53:04.814Z - AUTH TEST: Test environment ready
2025-07-08T08:53:04.815Z - AUTH TEST: Connection state: 0
2025-07-08T08:53:04.821Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T08:53:04.876Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T08:53:04.883Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T08:53:04.888Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T08:53:05.020Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:53:05.041Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T08:53:05.053Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:53:05.072Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T08:53:05.081Z - AUTH TEST: loginUser test with invalid password passed
2025-07-08T08:53:05.084Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:53:05.104Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T08:53:05.112Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-08T08:53:05.129Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:53:05.150Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:53:05.172Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:53:05.209Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:53:05.255Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T08:53:05.256Z - AUTH TEST: Disconnected successfully
2025-07-08T08:55:11.695Z - AUTH TEST: Starting auth controller test suite
2025-07-08T08:55:11.702Z - AUTH TEST: Test environment ready
2025-07-08T08:55:11.702Z - AUTH TEST: Connection state: 0
2025-07-08T08:55:11.709Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T08:55:11.763Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T08:55:11.771Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T08:55:11.777Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T08:55:11.909Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:55:11.931Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T08:55:11.943Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:55:11.961Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T08:55:11.969Z - AUTH TEST: loginUser test with invalid password passed
2025-07-08T08:55:11.971Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:55:11.988Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T08:55:11.999Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-08T08:55:12.017Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:55:12.042Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:55:12.080Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:55:12.109Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:55:12.159Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T08:55:12.160Z - AUTH TEST: Disconnected successfully
2025-07-08T08:55:25.791Z - MINIMAL TEST: Starting test suite
2025-07-08T08:55:25.793Z - MINIMAL TEST: Connecting to MongoDB
2025-07-08T08:55:28.895Z - MINIMAL TEST: Connection failed: MongoNetworkError: 3C860000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\ws\deps\openssl\openssl\ssl\record\rec_layer_s3.c:1605:SSL alert number 80

2025-07-08T08:55:28.899Z - MINIMAL TEST: Disconnecting from MongoDB
2025-07-08T08:55:28.903Z - MINIMAL TEST: Disconnected successfully
2025-07-08T08:56:26.074Z - MINIMAL TEST: Starting test suite
2025-07-08T08:56:26.077Z - MINIMAL TEST: Connecting to MongoDB
2025-07-08T08:56:27.018Z - MINIMAL TEST: Connection state: 1
2025-07-08T08:56:27.019Z - MINIMAL TEST: Connected successfully
2025-07-08T08:56:27.021Z - MINIMAL TEST: Running basic test
2025-07-08T08:56:27.025Z - MINIMAL TEST: Checking connection state
2025-07-08T08:56:27.025Z - Current connection state: 1
2025-07-08T08:56:27.027Z - MINIMAL TEST: Disconnecting from MongoDB
2025-07-08T08:56:27.071Z - MINIMAL TEST: Disconnected successfully
2025-07-08T08:56:27.488Z - AUTH TEST: Starting auth controller test suite
2025-07-08T08:56:27.492Z - AUTH TEST: Test environment ready
2025-07-08T08:56:27.493Z - AUTH TEST: Connection state: 0
2025-07-08T08:56:27.497Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T08:56:27.510Z - AUTH TEST: registerUser test with valid data passed
2025-07-08T08:56:27.514Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T08:56:27.521Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T08:56:27.525Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T08:56:27.660Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:56:27.672Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T08:56:27.682Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:56:27.695Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T08:56:27.701Z - AUTH TEST: loginUser test with invalid password passed
2025-07-08T08:56:27.703Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T08:56:27.716Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T08:56:27.723Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-08T08:56:27.742Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:56:27.757Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:56:27.775Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:56:27.793Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T08:56:27.848Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T08:56:27.849Z - AUTH TEST: Disconnected successfully
2025-07-08T09:00:36.253Z - AUTH TEST: Starting auth controller test suite
2025-07-08T09:00:36.263Z - AUTH TEST: Test environment ready
2025-07-08T09:00:36.263Z - AUTH TEST: Connection state: 0
2025-07-08T09:00:36.273Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T09:00:36.312Z - AUTH TEST: registerUser test with valid data passed
2025-07-08T09:00:36.336Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T09:00:36.346Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T09:00:36.353Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T09:00:36.491Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:00:36.509Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T09:00:36.544Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:00:36.560Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T09:00:36.577Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:00:36.592Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T09:00:36.629Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:00:36.652Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:00:36.677Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:00:36.698Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:00:36.745Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T09:00:36.745Z - AUTH TEST: Disconnected successfully
2025-07-08T09:01:55.255Z - AUTH TEST: Starting auth controller test suite
2025-07-08T09:01:55.267Z - AUTH TEST: Test environment ready
2025-07-08T09:01:55.267Z - AUTH TEST: Connection state: 0
2025-07-08T09:01:55.275Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T09:01:55.315Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T09:01:55.323Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T09:01:55.328Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T09:01:55.342Z - AUTH TEST: registerUser test with duplicate email passed
2025-07-08T09:01:55.415Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:01:55.424Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T09:01:55.436Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:01:55.453Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T09:01:55.459Z - AUTH TEST: loginUser test with invalid password passed
2025-07-08T09:01:55.462Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:01:55.470Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T09:01:55.477Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-08T09:01:55.507Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:01:55.529Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:01:55.545Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:01:55.562Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:01:55.607Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T09:01:55.608Z - AUTH TEST: Disconnected successfully
2025-07-08T09:02:42.377Z - AUTH TEST: Starting auth controller test suite
2025-07-08T09:02:42.384Z - AUTH TEST: Test environment ready
2025-07-08T09:02:42.385Z - AUTH TEST: Connection state: 0
2025-07-08T09:02:42.391Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T09:02:42.425Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T09:02:42.436Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T09:02:42.441Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T09:02:42.453Z - AUTH TEST: registerUser test with duplicate email passed
2025-07-08T09:02:42.539Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:02:42.550Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T09:02:42.560Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:02:42.574Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T09:02:42.580Z - AUTH TEST: loginUser test with invalid password passed
2025-07-08T09:02:42.583Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:02:42.591Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T09:02:42.599Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-08T09:02:42.626Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:02:42.640Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:02:42.656Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:02:42.672Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:02:42.720Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T09:02:42.722Z - AUTH TEST: Disconnected successfully
2025-07-08T09:03:23.279Z - AUTH TEST: Starting auth controller test suite
2025-07-08T09:03:23.289Z - AUTH TEST: Test environment ready
2025-07-08T09:03:23.290Z - AUTH TEST: Connection state: 0
2025-07-08T09:03:23.298Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T09:03:23.341Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T09:03:23.347Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T09:03:23.352Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T09:03:23.366Z - AUTH TEST: registerUser test with duplicate email passed
2025-07-08T09:03:23.456Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:03:23.468Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T09:03:23.478Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:03:23.491Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T09:03:23.500Z - AUTH TEST: loginUser test with invalid password passed
2025-07-08T09:03:23.503Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:03:23.518Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T09:03:23.527Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-08T09:03:23.556Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:03:23.571Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:03:23.587Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:03:23.608Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:03:23.649Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T09:03:23.650Z - AUTH TEST: Disconnected successfully
2025-07-08T09:04:31.061Z - AUTH TEST: Starting auth controller test suite
2025-07-08T09:04:31.069Z - AUTH TEST: Test environment ready
2025-07-08T09:04:31.070Z - AUTH TEST: Connection state: 0
2025-07-08T09:04:31.075Z - AUTH TEST: Testing registerUser with valid data
2025-07-08T09:04:31.099Z - AUTH TEST: registerUser test with valid data passed
2025-07-08T09:04:31.105Z - AUTH TEST: Testing registerUser with missing fields
2025-07-08T09:04:31.113Z - AUTH TEST: registerUser test with missing fields passed
2025-07-08T09:04:31.119Z - AUTH TEST: Testing registerUser with duplicate email
2025-07-08T09:04:31.238Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:04:31.256Z - AUTH TEST: Testing loginUser with valid credentials
2025-07-08T09:04:31.268Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:04:31.282Z - AUTH TEST: Testing loginUser with invalid password
2025-07-08T09:04:31.290Z - AUTH TEST: loginUser test with invalid password passed
2025-07-08T09:04:31.293Z - AUTH TEST: Registering test user for loginUser tests
2025-07-08T09:04:31.308Z - AUTH TEST: Testing loginUser with non-existent email
2025-07-08T09:04:31.317Z - AUTH TEST: loginUser test with non-existent email passed
2025-07-08T09:04:31.350Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:04:31.371Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:04:31.397Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:04:31.419Z - AUTH TEST: Registering and logging in test user for refreshToken tests
2025-07-08T09:04:31.472Z - AUTH TEST: Disconnecting from MongoDB
2025-07-08T09:04:31.473Z - AUTH TEST: Disconnected successfully
