{"roots": ["budget_tracker"], "packages": [{"name": "budget_tracker", "version": "1.0.0+1", "dependencies": ["connectivity_plus", "cupertino_icons", "dio", "firebase_auth", "firebase_core", "fl_chart", "flutter", "flutter_dotenv", "flutter_local_notifications", "flutter_secure_storage", "flutter_slidable", "google_sign_in", "hive", "hive_flutter", "http", "image_picker", "intl", "mongo_dart", "path_provider", "pdf", "printing", "provider", "shared_preferences", "sqflite", "url_launcher"], "devDependencies": ["build_runner", "flutter_lints", "flutter_test", "hive_generator", "integration_test", "<PERSON><PERSON>", "mocktail", "test"]}, {"name": "hive_generator", "version": "2.0.1", "dependencies": ["analyzer", "build", "hive", "source_gen", "source_helper"]}, {"name": "test", "version": "1.25.15", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "mocktail", "version": "1.0.4", "dependencies": ["collection", "matcher", "test_api"]}, {"name": "build_runner", "version": "2.5.4", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "<PERSON><PERSON>", "version": "5.4.5", "dependencies": ["analyzer", "build", "code_builder", "collection", "dart_style", "matcher", "meta", "path", "source_gen", "test_api"]}, {"name": "flutter_lints", "version": "6.0.0", "dependencies": ["lints"]}, {"name": "integration_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "file", "flutter", "flutter_driver", "flutter_test", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "sync_http", "term_glyph", "test_api", "vector_math", "vm_service", "webdriver"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "flutter_dotenv", "version": "5.2.1", "dependencies": ["flutter"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "flutter_local_notifications", "version": "19.3.0", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "flutter_local_notifications_windows", "timezone"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "printing", "version": "5.14.2", "dependencies": ["ffi", "flutter", "flutter_web_plugins", "http", "image", "meta", "pdf", "pdf_widget_wrapper", "plugin_platform_interface", "web"]}, {"name": "pdf", "version": "3.11.3", "dependencies": ["archive", "barcode", "bidi", "crypto", "image", "meta", "path_parsing", "vector_math", "xml"]}, {"name": "flutter_slidable", "version": "4.0.0", "dependencies": ["flutter"]}, {"name": "fl_chart", "version": "1.0.0", "dependencies": ["equatable", "flutter", "vector_math"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "mongo_dart", "version": "0.10.5", "dependencies": ["basic_utils", "bson", "collection", "crypto", "decimal", "fixnum", "logging", "mongo_dart_query", "path", "pool", "sasl_scram", "uuid", "vy_string_utils"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "google_sign_in", "version": "7.1.0", "dependencies": ["flutter", "google_sign_in_android", "google_sign_in_ios", "google_sign_in_platform_interface", "google_sign_in_web"]}, {"name": "firebase_core", "version": "3.15.1", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_auth", "version": "5.6.2", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "connectivity_plus", "version": "6.1.4", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "analyzer", "version": "6.11.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "macros", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "source_gen", "version": "1.5.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "source_span", "yaml"]}, {"name": "build", "version": "2.5.4", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "test_core", "version": "0.6.8", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "coverage", "version": "1.14.1", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service", "yaml"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "2.3.8", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "build_runner_core", "version": "9.1.2", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.5.4", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "lints", "version": "6.0.0", "dependencies": []}, {"name": "webdriver", "version": "3.1.0", "dependencies": ["matcher", "path", "stack_trace", "sync_http"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "sync_http", "version": "0.3.1", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "flutter_driver", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "file", "flutter", "flutter_test", "fuchsia_remote_debug_protocol", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "platform", "process", "source_span", "stack_trace", "stream_channel", "string_scanner", "sync_http", "term_glyph", "test_api", "vector_math", "vm_service", "webdriver"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "timezone", "version": "0.10.1", "dependencies": ["http", "path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "9.1.0", "dependencies": ["plugin_platform_interface"]}, {"name": "flutter_local_notifications_windows", "version": "1.0.0", "dependencies": ["ffi", "flutter", "flutter_local_notifications_platform_interface", "meta", "timezone", "xml"]}, {"name": "flutter_local_notifications_linux", "version": "6.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+23", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "pdf_widget_wrapper", "version": "1.0.4", "dependencies": ["flutter", "pdf"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "bidi", "version": "2.0.13", "dependencies": []}, {"name": "barcode", "version": "2.2.9", "dependencies": ["meta", "qr"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "vy_string_utils", "version": "0.4.6", "dependencies": ["power_extensions"]}, {"name": "sasl_scram", "version": "0.1.1", "dependencies": ["buffer", "collection", "crypto", "saslprep"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "decimal", "version": "3.2.4", "dependencies": ["intl", "rational"]}, {"name": "basic_utils", "version": "5.8.2", "dependencies": ["archive", "http", "json_annotation", "logging", "pointycastle"]}, {"name": "mongo_dart_query", "version": "5.0.2", "dependencies": ["bson", "fixnum", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "bson", "version": "5.0.7", "dependencies": ["decimal", "fixnum", "packages_extensions", "power_extensions", "rational", "uuid"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "google_sign_in_web", "version": "1.0.0", "dependencies": ["flutter", "flutter_web_plugins", "google_identity_services_web", "google_sign_in_platform_interface", "http", "web"]}, {"name": "google_sign_in_platform_interface", "version": "3.0.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_sign_in_ios", "version": "6.0.0", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "google_sign_in_android", "version": "7.0.1", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "firebase_core_web", "version": "2.24.1", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_core_platform_interface", "version": "6.0.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_auth_web", "version": "5.15.2", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"]}, {"name": "firebase_auth_platform_interface", "version": "7.7.2", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "macros", "version": "0.1.3-main.0", "dependencies": ["_macros"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "_fe_analyzer_shared", "version": "76.0.0", "dependencies": ["meta"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "process", "version": "5.0.3", "dependencies": ["file", "path", "platform"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "fuchsia_remote_debug_protocol", "version": "0.0.0", "dependencies": ["file", "meta", "path", "platform", "process", "vm_service"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "qr", "version": "3.0.2", "dependencies": ["meta"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "power_extensions", "version": "0.2.3", "dependencies": []}, {"name": "saslprep", "version": "1.0.3", "dependencies": ["unorm_dart"]}, {"name": "buffer", "version": "1.2.3", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "rational", "version": "2.2.3", "dependencies": []}, {"name": "pointycastle", "version": "4.0.0", "dependencies": ["collection", "convert"]}, {"name": "packages_extensions", "version": "0.1.1", "dependencies": ["decimal", "power_extensions", "rational"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "google_identity_services_web", "version": "0.3.3+1", "dependencies": ["meta", "web"]}, {"name": "_flutterfire_internals", "version": "1.3.58", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "_macros", "version": "0.3.3", "dependencies": []}, {"name": "unorm_dart", "version": "0.3.1+1", "dependencies": []}], "configVersion": 1}