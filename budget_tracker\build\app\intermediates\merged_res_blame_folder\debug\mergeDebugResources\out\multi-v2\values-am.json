{"logs": [{"outputFile": "com.example.budget_tracker.app-mergeDebugResources-49:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,248,355,467,583,703,819,934,1026,1158,1283", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "156,243,350,462,578,698,814,929,1021,1153,1278,1383"}, "to": {"startLines": "56,59,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5642,5910,6377,6484,6596,6712,6832,6948,7063,7155,7287,7412", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "5743,5992,6479,6591,6707,6827,6943,7058,7150,7282,7407,7512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "58,61,62,63", "startColumns": "4,4,4,4", "startOffsets": "5814,6079,6174,6280", "endColumns": "95,94,105,96", "endOffsets": "5905,6169,6275,6372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e1f6d2e0b1aa38467964f5b59b4f29f9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3636,3738,3877,3999,4101,4228,4351,4459,4693,4821,4924,5069,5192,5327,5454,5514,5571", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "3733,3872,3994,4096,4223,4346,4454,4556,4816,4919,5064,5187,5322,5449,5509,5566,5637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "31,32,33,34,35,36,37,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2949,3042,3142,3239,3338,3434,3536,7802", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3037,3137,3234,3333,3429,3531,3631,7898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4561", "endColumns": "131", "endOffsets": "4688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,253,327,458,627,708", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "166,248,322,453,622,703,781"}, "to": {"startLines": "57,60,74,75,78,79,80", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5748,5997,7517,7591,7903,8072,8153", "endColumns": "65,81,73,130,168,80,77", "endOffsets": "5809,6074,7586,7717,8067,8148,8226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,7722", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,7797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\006877986797b3b6be5cf579d190afc8\\transformed\\jetified-credentials-1.5.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,114", "endOffsets": "161,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2723,2834", "endColumns": "110,114", "endOffsets": "2829,2944"}}]}]}