{"version": 3, "file": "client.js", "sources": ["../../src/client.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport { getEnvelopeEndpointWithUrlEncodedAuth } from './api';\nimport { DEFAULT_ENVIRONMENT } from './constants';\nimport { getCurrentScope, getIsolationScope, getTraceContextFromScope, withScope } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport { createEventEnvelope, createSessionEnvelope } from './envelope';\nimport type { IntegrationIndex } from './integration';\nimport { afterSetupIntegrations, setupIntegration, setupIntegrations } from './integration';\nimport type { Scope } from './scope';\nimport { updateSession } from './session';\nimport {\n  getDynamicSamplingContextFromScope,\n  getDynamicSamplingContextFromSpan,\n} from './tracing/dynamicSamplingContext';\nimport type { Breadcrumb, BreadcrumbHint, FetchBreadcrumbHint, XhrBreadcrumbHint } from './types-hoist/breadcrumb';\nimport type { CheckIn, MonitorConfig } from './types-hoist/checkin';\nimport type { EventDropReason, Outcome } from './types-hoist/clientreport';\nimport type { TraceContext } from './types-hoist/context';\nimport type { DataCategory } from './types-hoist/datacategory';\nimport type { DsnComponents } from './types-hoist/dsn';\nimport type { DynamicSamplingContext, Envelope } from './types-hoist/envelope';\nimport type { ErrorEvent, Event, EventHint, TransactionEvent } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport type { FeedbackEvent } from './types-hoist/feedback';\nimport type { Integration } from './types-hoist/integration';\nimport type { Log } from './types-hoist/log';\nimport type { ClientOptions } from './types-hoist/options';\nimport type { ParameterizedString } from './types-hoist/parameterize';\nimport type { SdkMetadata } from './types-hoist/sdkmetadata';\nimport type { Session, SessionAggregates } from './types-hoist/session';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { Span, SpanAttributes, SpanContextData, SpanJSON } from './types-hoist/span';\nimport type { StartSpanOptions } from './types-hoist/startSpanOptions';\nimport type { Transport, TransportMakeRequestResponse } from './types-hoist/transport';\nimport { createClientReportEnvelope } from './utils/clientreport';\nimport { dsnToString, makeDsn } from './utils/dsn';\nimport { addItemToEnvelope, createAttachmentEnvelopeItem } from './utils/envelope';\nimport { getPossibleEventMessages } from './utils/eventUtils';\nimport { isParameterizedString, isPlainObject, isPrimitive, isThenable } from './utils/is';\nimport { logger } from './utils/logger';\nimport { merge } from './utils/merge';\nimport { checkOrSetAlreadyCaught, uuid4 } from './utils/misc';\nimport { parseSampleRate } from './utils/parseSampleRate';\nimport { prepareEvent } from './utils/prepareEvent';\nimport { getActiveSpan, showSpanDropWarning, spanToTraceContext } from './utils/spanUtils';\nimport { rejectedSyncPromise, resolvedSyncPromise, SyncPromise } from './utils/syncpromise';\nimport { convertSpanJsonToTransactionEvent, convertTransactionEventToSpanJson } from './utils/transactionEvent';\n\nconst ALREADY_SEEN_ERROR = \"Not capturing exception because it's already been captured.\";\nconst MISSING_RELEASE_FOR_SESSION_ERROR = 'Discarded session because of missing or non-string release';\n\nconst INTERNAL_ERROR_SYMBOL = Symbol.for('SentryInternalError');\nconst DO_NOT_SEND_EVENT_SYMBOL = Symbol.for('SentryDoNotSendEventError');\n\ninterface InternalError {\n  message: string;\n  [INTERNAL_ERROR_SYMBOL]: true;\n}\n\ninterface DoNotSendEventError {\n  message: string;\n  [DO_NOT_SEND_EVENT_SYMBOL]: true;\n}\n\nfunction _makeInternalError(message: string): InternalError {\n  return {\n    message,\n    [INTERNAL_ERROR_SYMBOL]: true,\n  };\n}\n\nfunction _makeDoNotSendEventError(message: string): DoNotSendEventError {\n  return {\n    message,\n    [DO_NOT_SEND_EVENT_SYMBOL]: true,\n  };\n}\n\nfunction _isInternalError(error: unknown): error is InternalError {\n  return !!error && typeof error === 'object' && INTERNAL_ERROR_SYMBOL in error;\n}\n\nfunction _isDoNotSendEventError(error: unknown): error is DoNotSendEventError {\n  return !!error && typeof error === 'object' && DO_NOT_SEND_EVENT_SYMBOL in error;\n}\n\n/**\n * Base implementation for all JavaScript SDK clients.\n *\n * Call the constructor with the corresponding options\n * specific to the client subclass. To access these options later, use\n * {@link Client.getOptions}.\n *\n * If a Dsn is specified in the options, it will be parsed and stored. Use\n * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is\n * invalid, the constructor will throw a {@link SentryException}. Note that\n * without a valid Dsn, the SDK will not send any events to Sentry.\n *\n * Before sending an event, it is passed through\n * {@link Client._prepareEvent} to add SDK information and scope data\n * (breadcrumbs and context). To add more custom information, override this\n * method and extend the resulting prepared event.\n *\n * To issue automatically created events (e.g. via instrumentation), use\n * {@link Client.captureEvent}. It will prepare the event and pass it through\n * the callback lifecycle. To issue auto-breadcrumbs, use\n * {@link Client.addBreadcrumb}.\n *\n * @example\n * class NodeClient extends Client<NodeOptions> {\n *   public constructor(options: NodeOptions) {\n *     super(options);\n *   }\n *\n *   // ...\n * }\n */\nexport abstract class Client<O extends ClientOptions = ClientOptions> {\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */\n  protected readonly _dsn?: DsnComponents;\n\n  protected readonly _transport?: Transport;\n\n  /** Array of set up integrations. */\n  protected _integrations: IntegrationIndex;\n\n  /** Number of calls being processed */\n  protected _numProcessing: number;\n\n  protected _eventProcessors: EventProcessor[];\n\n  /** Holds flushable  */\n  private _outcomes: { [key: string]: number };\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  private _hooks: Record<string, Function[]>;\n\n  /**\n   * Initializes this client instance.\n   *\n   * @param options Options for the client.\n   */\n  protected constructor(options: O) {\n    this._options = options;\n    this._integrations = {};\n    this._numProcessing = 0;\n    this._outcomes = {};\n    this._hooks = {};\n    this._eventProcessors = [];\n\n    if (options.dsn) {\n      this._dsn = makeDsn(options.dsn);\n    } else {\n      DEBUG_BUILD && logger.warn('No DSN provided, client will not send events.');\n    }\n\n    if (this._dsn) {\n      const url = getEnvelopeEndpointWithUrlEncodedAuth(\n        this._dsn,\n        options.tunnel,\n        options._metadata ? options._metadata.sdk : undefined,\n      );\n      this._transport = options.transport({\n        tunnel: this._options.tunnel,\n        recordDroppedEvent: this.recordDroppedEvent.bind(this),\n        ...options.transportOptions,\n        url,\n      });\n    }\n  }\n\n  /**\n   * Captures an exception event and sends it to Sentry.\n   *\n   * Unlike `captureException` exported from every SDK, this method requires that you pass it the current scope.\n   */\n  public captureException(exception: unknown, hint?: EventHint, scope?: Scope): string {\n    const eventId = uuid4();\n\n    // ensure we haven't captured this very object before\n    if (checkOrSetAlreadyCaught(exception)) {\n      DEBUG_BUILD && logger.log(ALREADY_SEEN_ERROR);\n      return eventId;\n    }\n\n    const hintWithEventId = {\n      event_id: eventId,\n      ...hint,\n    };\n\n    this._process(\n      this.eventFromException(exception, hintWithEventId).then(event =>\n        this._captureEvent(event, hintWithEventId, scope),\n      ),\n    );\n\n    return hintWithEventId.event_id;\n  }\n\n  /**\n   * Captures a message event and sends it to Sentry.\n   *\n   * Unlike `captureMessage` exported from every SDK, this method requires that you pass it the current scope.\n   */\n  public captureMessage(\n    message: ParameterizedString,\n    level?: SeverityLevel,\n    hint?: EventHint,\n    currentScope?: Scope,\n  ): string {\n    const hintWithEventId = {\n      event_id: uuid4(),\n      ...hint,\n    };\n\n    const eventMessage = isParameterizedString(message) ? message : String(message);\n\n    const promisedEvent = isPrimitive(message)\n      ? this.eventFromMessage(eventMessage, level, hintWithEventId)\n      : this.eventFromException(message, hintWithEventId);\n\n    this._process(promisedEvent.then(event => this._captureEvent(event, hintWithEventId, currentScope)));\n\n    return hintWithEventId.event_id;\n  }\n\n  /**\n   * Captures a manually created event and sends it to Sentry.\n   *\n   * Unlike `captureEvent` exported from every SDK, this method requires that you pass it the current scope.\n   */\n  public captureEvent(event: Event, hint?: EventHint, currentScope?: Scope): string {\n    const eventId = uuid4();\n\n    // ensure we haven't captured this very object before\n    if (hint?.originalException && checkOrSetAlreadyCaught(hint.originalException)) {\n      DEBUG_BUILD && logger.log(ALREADY_SEEN_ERROR);\n      return eventId;\n    }\n\n    const hintWithEventId = {\n      event_id: eventId,\n      ...hint,\n    };\n\n    const sdkProcessingMetadata = event.sdkProcessingMetadata || {};\n    const capturedSpanScope: Scope | undefined = sdkProcessingMetadata.capturedSpanScope;\n    const capturedSpanIsolationScope: Scope | undefined = sdkProcessingMetadata.capturedSpanIsolationScope;\n\n    this._process(\n      this._captureEvent(event, hintWithEventId, capturedSpanScope || currentScope, capturedSpanIsolationScope),\n    );\n\n    return hintWithEventId.event_id;\n  }\n\n  /**\n   * Captures a session.\n   */\n  public captureSession(session: Session): void {\n    this.sendSession(session);\n    // After sending, we set init false to indicate it's not the first occurrence\n    updateSession(session, { init: false });\n  }\n\n  /**\n   * Create a cron monitor check in and send it to Sentry. This method is not available on all clients.\n   *\n   * @param checkIn An object that describes a check in.\n   * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n   * to create a monitor automatically when sending a check in.\n   * @param scope An optional scope containing event metadata.\n   * @returns A string representing the id of the check in.\n   */\n  public captureCheckIn?(checkIn: CheckIn, monitorConfig?: MonitorConfig, scope?: Scope): string;\n\n  /**\n   * Get the current Dsn.\n   */\n  public getDsn(): DsnComponents | undefined {\n    return this._dsn;\n  }\n\n  /**\n   * Get the current options.\n   */\n  public getOptions(): O {\n    return this._options;\n  }\n\n  /**\n   * Get the SDK metadata.\n   * @see SdkMetadata\n   */\n  public getSdkMetadata(): SdkMetadata | undefined {\n    return this._options._metadata;\n  }\n\n  /**\n   * Returns the transport that is used by the client.\n   * Please note that the transport gets lazy initialized so it will only be there once the first event has been sent.\n   */\n  public getTransport(): Transport | undefined {\n    return this._transport;\n  }\n\n  /**\n   * Wait for all events to be sent or the timeout to expire, whichever comes first.\n   *\n   * @param timeout Maximum time in ms the client should wait for events to be flushed. Omitting this parameter will\n   *   cause the client to wait until all events are sent before resolving the promise.\n   * @returns A promise that will resolve with `true` if all events are sent before the timeout, or `false` if there are\n   * still events in the queue when the timeout is reached.\n   */\n  public flush(timeout?: number): PromiseLike<boolean> {\n    const transport = this._transport;\n    if (transport) {\n      this.emit('flush');\n      return this._isClientDoneProcessing(timeout).then(clientFinished => {\n        return transport.flush(timeout).then(transportFlushed => clientFinished && transportFlushed);\n      });\n    } else {\n      return resolvedSyncPromise(true);\n    }\n  }\n\n  /**\n   * Flush the event queue and set the client to `enabled = false`. See {@link Client.flush}.\n   *\n   * @param {number} timeout Maximum time in ms the client should wait before shutting down. Omitting this parameter will cause\n   *   the client to wait until all events are sent before disabling itself.\n   * @returns {Promise<boolean>} A promise which resolves to `true` if the flush completes successfully before the timeout, or `false` if\n   * it doesn't.\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this.flush(timeout).then(result => {\n      this.getOptions().enabled = false;\n      this.emit('close');\n      return result;\n    });\n  }\n\n  /**\n   * Get all installed event processors.\n   */\n  public getEventProcessors(): EventProcessor[] {\n    return this._eventProcessors;\n  }\n\n  /**\n   * Adds an event processor that applies to any event processed by this client.\n   */\n  public addEventProcessor(eventProcessor: EventProcessor): void {\n    this._eventProcessors.push(eventProcessor);\n  }\n\n  /**\n   * Initialize this client.\n   * Call this after the client was set on a scope.\n   */\n  public init(): void {\n    if (\n      this._isEnabled() ||\n      // Force integrations to be setup even if no DSN was set when we have\n      // Spotlight enabled. This is particularly important for browser as we\n      // don't support the `spotlight` option there and rely on the users\n      // adding the `spotlightBrowserIntegration()` to their integrations which\n      // wouldn't get initialized with the check below when there's no DSN set.\n      this._options.integrations.some(({ name }) => name.startsWith('Spotlight'))\n    ) {\n      this._setupIntegrations();\n    }\n  }\n\n  /**\n   * Gets an installed integration by its name.\n   *\n   * @returns {Integration|undefined} The installed integration or `undefined` if no integration with that `name` was installed.\n   */\n  public getIntegrationByName<T extends Integration = Integration>(integrationName: string): T | undefined {\n    return this._integrations[integrationName] as T | undefined;\n  }\n\n  /**\n   * Add an integration to the client.\n   * This can be used to e.g. lazy load integrations.\n   * In most cases, this should not be necessary,\n   * and you're better off just passing the integrations via `integrations: []` at initialization time.\n   * However, if you find the need to conditionally load & add an integration, you can use `addIntegration` to do so.\n   */\n  public addIntegration(integration: Integration): void {\n    const isAlreadyInstalled = this._integrations[integration.name];\n\n    // This hook takes care of only installing if not already installed\n    setupIntegration(this, integration, this._integrations);\n    // Here we need to check manually to make sure to not run this multiple times\n    if (!isAlreadyInstalled) {\n      afterSetupIntegrations(this, [integration]);\n    }\n  }\n\n  /**\n   * Send a fully prepared event to Sentry.\n   */\n  public sendEvent(event: Event, hint: EventHint = {}): void {\n    this.emit('beforeSendEvent', event, hint);\n\n    let env = createEventEnvelope(event, this._dsn, this._options._metadata, this._options.tunnel);\n\n    for (const attachment of hint.attachments || []) {\n      env = addItemToEnvelope(env, createAttachmentEnvelopeItem(attachment));\n    }\n\n    const promise = this.sendEnvelope(env);\n    if (promise) {\n      promise.then(sendResponse => this.emit('afterSendEvent', event, sendResponse), null);\n    }\n  }\n\n  /**\n   * Send a session or session aggregrates to Sentry.\n   */\n  public sendSession(session: Session | SessionAggregates): void {\n    // Backfill release and environment on session\n    const { release: clientReleaseOption, environment: clientEnvironmentOption = DEFAULT_ENVIRONMENT } = this._options;\n    if ('aggregates' in session) {\n      const sessionAttrs = session.attrs || {};\n      if (!sessionAttrs.release && !clientReleaseOption) {\n        DEBUG_BUILD && logger.warn(MISSING_RELEASE_FOR_SESSION_ERROR);\n        return;\n      }\n      sessionAttrs.release = sessionAttrs.release || clientReleaseOption;\n      sessionAttrs.environment = sessionAttrs.environment || clientEnvironmentOption;\n      session.attrs = sessionAttrs;\n    } else {\n      if (!session.release && !clientReleaseOption) {\n        DEBUG_BUILD && logger.warn(MISSING_RELEASE_FOR_SESSION_ERROR);\n        return;\n      }\n      session.release = session.release || clientReleaseOption;\n      session.environment = session.environment || clientEnvironmentOption;\n    }\n\n    this.emit('beforeSendSession', session);\n\n    const env = createSessionEnvelope(session, this._dsn, this._options._metadata, this._options.tunnel);\n\n    // sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.sendEnvelope(env);\n  }\n\n  /**\n   * Record on the client that an event got dropped (ie, an event that will not be sent to Sentry).\n   */\n  public recordDroppedEvent(reason: EventDropReason, category: DataCategory, count: number = 1): void {\n    if (this._options.sendClientReports) {\n      // We want to track each category (error, transaction, session, replay_event) separately\n      // but still keep the distinction between different type of outcomes.\n      // We could use nested maps, but it's much easier to read and type this way.\n      // A correct type for map-based implementation if we want to go that route\n      // would be `Partial<Record<SentryRequestType, Partial<Record<Outcome, number>>>>`\n      // With typescript 4.1 we could even use template literal types\n      const key = `${reason}:${category}`;\n      DEBUG_BUILD && logger.log(`Recording outcome: \"${key}\"${count > 1 ? ` (${count} times)` : ''}`);\n      this._outcomes[key] = (this._outcomes[key] || 0) + count;\n    }\n  }\n\n  /* eslint-disable @typescript-eslint/unified-signatures */\n  /**\n   * Register a callback for whenever a span is started.\n   * Receives the span as argument.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'spanStart', callback: (span: Span) => void): () => void;\n\n  /**\n   * Register a callback before span sampling runs. Receives a `samplingDecision` object argument with a `decision`\n   * property that can be used to make a sampling decision that will be enforced, before any span sampling runs.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(\n    hook: 'beforeSampling',\n    callback: (\n      samplingData: {\n        spanAttributes: SpanAttributes;\n        spanName: string;\n        parentSampled?: boolean;\n        parentSampleRate?: number;\n        parentContext?: SpanContextData;\n      },\n      samplingDecision: { decision: boolean },\n    ) => void,\n  ): void;\n\n  /**\n   * Register a callback for after a span is ended.\n   * NOTE: The span cannot be mutated anymore in this callback.\n   * Receives the span as argument.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'spanEnd', callback: (span: Span) => void): () => void;\n\n  /**\n   * Register a callback for when an idle span is allowed to auto-finish.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'idleSpanEnableAutoFinish', callback: (span: Span) => void): () => void;\n\n  /**\n   * Register a callback for transaction start and finish.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'beforeEnvelope', callback: (envelope: Envelope) => void): () => void;\n\n  /**\n   * Register a callback that runs when stack frame metadata should be applied to an event.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'applyFrameMetadata', callback: (event: Event) => void): () => void;\n\n  /**\n   * Register a callback for before sending an event.\n   * This is called right before an event is sent and should not be used to mutate the event.\n   * Receives an Event & EventHint as arguments.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'beforeSendEvent', callback: (event: Event, hint?: EventHint | undefined) => void): () => void;\n\n  /**\n   * Register a callback for before sending a session or session aggregrates..\n   * Receives the session/aggregate as second argument.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'beforeSendSession', callback: (session: Session | SessionAggregates) => void): () => void;\n\n  /**\n   * Register a callback for preprocessing an event,\n   * before it is passed to (global) event processors.\n   * Receives an Event & EventHint as arguments.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'preprocessEvent', callback: (event: Event, hint?: EventHint | undefined) => void): () => void;\n\n  /**\n   * Register a callback for postprocessing an event,\n   * after it was passed to (global) event processors, before it is being sent.\n   * Receives an Event & EventHint as arguments.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'postprocessEvent', callback: (event: Event, hint?: EventHint | undefined) => void): () => void;\n\n  /**\n   * Register a callback for when an event has been sent.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(\n    hook: 'afterSendEvent',\n    callback: (event: Event, sendResponse: TransportMakeRequestResponse) => void,\n  ): () => void;\n\n  /**\n   * Register a callback before a breadcrumb is added.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'beforeAddBreadcrumb', callback: (breadcrumb: Breadcrumb, hint?: BreadcrumbHint) => void): () => void;\n\n  /**\n   * Register a callback when a DSC (Dynamic Sampling Context) is created.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'createDsc', callback: (dsc: DynamicSamplingContext, rootSpan?: Span) => void): () => void;\n\n  /**\n   * Register a callback when a Feedback event has been prepared.\n   * This should be used to mutate the event. The options argument can hint\n   * about what kind of mutation it expects.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(\n    hook: 'beforeSendFeedback',\n    callback: (feedback: FeedbackEvent, options?: { includeReplay?: boolean }) => void,\n  ): () => void;\n\n  /**\n   * Register a callback when the feedback widget is opened in a user's browser\n   */\n  public on(hook: 'openFeedbackWidget', callback: () => void): () => void;\n\n  /**\n   * A hook for the browser tracing integrations to trigger a span start for a page load.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(\n    hook: 'startPageLoadSpan',\n    callback: (\n      options: StartSpanOptions,\n      traceOptions?: { sentryTrace?: string | undefined; baggage?: string | undefined },\n    ) => void,\n  ): () => void;\n\n  /**\n   * A hook for browser tracing integrations to trigger a span for a navigation.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'startNavigationSpan', callback: (options: StartSpanOptions) => void): () => void;\n\n  /**\n   * A hook for GraphQL client integration to enhance a span with request data.\n   * @returns A function that, when executed, removes the registered callback.\n   */\n  public on(\n    hook: 'beforeOutgoingRequestSpan',\n    callback: (span: Span, hint: XhrBreadcrumbHint | FetchBreadcrumbHint) => void,\n  ): () => void;\n\n  /**\n   * A hook for GraphQL client integration to enhance a breadcrumb with request data.\n   * @returns A function that, when executed, removes the registered callback.\n   */\n  public on(\n    hook: 'beforeOutgoingRequestBreadcrumb',\n    callback: (breadcrumb: Breadcrumb, hint: XhrBreadcrumbHint | FetchBreadcrumbHint) => void,\n  ): () => void;\n\n  /**\n   * A hook that is called when the client is flushing\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'flush', callback: () => void): () => void;\n\n  /**\n   * A hook that is called when the client is closing\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'close', callback: () => void): () => void;\n\n  /**\n   * A hook that is called before a log is captured. This hooks runs before `beforeSendLog` is fired.\n   *\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'beforeCaptureLog', callback: (log: Log) => void): () => void;\n\n  /**\n   * A hook that is called after a log is captured\n   *\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'afterCaptureLog', callback: (log: Log) => void): () => void;\n\n  /**\n   * A hook that is called when the client is flushing logs\n   *\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n  public on(hook: 'flushLogs', callback: () => void): () => void;\n\n  /**\n   * Register a hook on this client.\n   */\n  public on(hook: string, callback: unknown): () => void {\n    const hooks = (this._hooks[hook] = this._hooks[hook] || []);\n\n    // @ts-expect-error We assume the types are correct\n    hooks.push(callback);\n\n    // This function returns a callback execution handler that, when invoked,\n    // deregisters a callback. This is crucial for managing instances where callbacks\n    // need to be unregistered to prevent self-referencing in callback closures,\n    // ensuring proper garbage collection.\n    return () => {\n      // @ts-expect-error We assume the types are correct\n      const cbIndex = hooks.indexOf(callback);\n      if (cbIndex > -1) {\n        hooks.splice(cbIndex, 1);\n      }\n    };\n  }\n\n  /** Fire a hook whenever a span starts. */\n  public emit(hook: 'spanStart', span: Span): void;\n\n  /** A hook that is called every time before a span is sampled. */\n  public emit(\n    hook: 'beforeSampling',\n    samplingData: {\n      spanAttributes: SpanAttributes;\n      spanName: string;\n      parentSampled?: boolean;\n      parentSampleRate?: number;\n      parentContext?: SpanContextData;\n    },\n    samplingDecision: { decision: boolean },\n  ): void;\n\n  /** Fire a hook whenever a span ends. */\n  public emit(hook: 'spanEnd', span: Span): void;\n\n  /**\n   * Fire a hook indicating that an idle span is allowed to auto finish.\n   */\n  public emit(hook: 'idleSpanEnableAutoFinish', span: Span): void;\n\n  /*\n   * Fire a hook event for envelope creation and sending. Expects to be given an envelope as the\n   * second argument.\n   */\n  public emit(hook: 'beforeEnvelope', envelope: Envelope): void;\n\n  /*\n   * Fire a hook indicating that stack frame metadata should be applied to the event passed to the hook.\n   */\n  public emit(hook: 'applyFrameMetadata', event: Event): void;\n\n  /**\n   * Fire a hook event before sending an event.\n   * This is called right before an event is sent and should not be used to mutate the event.\n   * Expects to be given an Event & EventHint as the second/third argument.\n   */\n  public emit(hook: 'beforeSendEvent', event: Event, hint?: EventHint): void;\n\n  /**\n   * Fire a hook event before sending a session/aggregates.\n   * Expects to be given the prepared session/aggregates as second argument.\n   */\n  public emit(hook: 'beforeSendSession', session: Session | SessionAggregates): void;\n\n  /**\n   * Fire a hook event to process events before they are passed to (global) event processors.\n   * Expects to be given an Event & EventHint as the second/third argument.\n   */\n  public emit(hook: 'preprocessEvent', event: Event, hint?: EventHint): void;\n\n  /**\n   * Fire a hook event to process a user on an event before it is sent to Sentry, after all other processors have run.\n   * Expects to be given an Event & EventHint as the second/third argument.\n   */\n  public emit(hook: 'postprocessEvent', event: Event, hint?: EventHint): void;\n\n  /*\n   * Fire a hook event after sending an event. Expects to be given an Event as the\n   * second argument.\n   */\n  public emit(hook: 'afterSendEvent', event: Event, sendResponse: TransportMakeRequestResponse): void;\n\n  /**\n   * Fire a hook for when a breadcrumb is added. Expects the breadcrumb as second argument.\n   */\n  public emit(hook: 'beforeAddBreadcrumb', breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void;\n\n  /**\n   * Fire a hook for when a DSC (Dynamic Sampling Context) is created. Expects the DSC as second argument.\n   */\n  public emit(hook: 'createDsc', dsc: DynamicSamplingContext, rootSpan?: Span): void;\n\n  /**\n   * Fire a hook event for after preparing a feedback event. Events to be given\n   * a feedback event as the second argument, and an optional options object as\n   * third argument.\n   */\n  public emit(hook: 'beforeSendFeedback', feedback: FeedbackEvent, options?: { includeReplay?: boolean }): void;\n\n  /**\n   * Fire a hook event for when the feedback widget is opened in a user's browser\n   */\n  public emit(hook: 'openFeedbackWidget'): void;\n\n  /**\n   * Emit a hook event for browser tracing integrations to trigger a span start for a page load.\n   */\n  public emit(\n    hook: 'startPageLoadSpan',\n    options: StartSpanOptions,\n    traceOptions?: { sentryTrace?: string | undefined; baggage?: string | undefined },\n  ): void;\n\n  /**\n   * Emit a hook event for browser tracing integrations to trigger a span for a navigation.\n   */\n  public emit(hook: 'startNavigationSpan', options: StartSpanOptions): void;\n\n  /**\n   * Emit a hook event for GraphQL client integration to enhance a span with request data.\n   */\n  public emit(hook: 'beforeOutgoingRequestSpan', span: Span, hint: XhrBreadcrumbHint | FetchBreadcrumbHint): void;\n\n  /**\n   * Emit a hook event for GraphQL client integration to enhance a breadcrumb with request data.\n   */\n  public emit(\n    hook: 'beforeOutgoingRequestBreadcrumb',\n    breadcrumb: Breadcrumb,\n    hint: XhrBreadcrumbHint | FetchBreadcrumbHint,\n  ): void;\n\n  /**\n   * Emit a hook event for client flush\n   */\n  public emit(hook: 'flush'): void;\n\n  /**\n   * Emit a hook event for client close\n   */\n  public emit(hook: 'close'): void;\n\n  /**\n   * Emit a hook event for client before capturing a log. This hooks runs before `beforeSendLog` is fired.\n   */\n  public emit(hook: 'beforeCaptureLog', log: Log): void;\n\n  /**\n   * Emit a hook event for client after capturing a log.\n   */\n  public emit(hook: 'afterCaptureLog', log: Log): void;\n\n  /**\n   * Emit a hook event for client flush logs\n   */\n  public emit(hook: 'flushLogs'): void;\n\n  /**\n   * Emit a hook that was previously registered via `on()`.\n   */\n  public emit(hook: string, ...rest: unknown[]): void {\n    const callbacks = this._hooks[hook];\n    if (callbacks) {\n      callbacks.forEach(callback => callback(...rest));\n    }\n  }\n\n  /**\n   * Send an envelope to Sentry.\n   */\n  public sendEnvelope(envelope: Envelope): PromiseLike<TransportMakeRequestResponse> {\n    this.emit('beforeEnvelope', envelope);\n\n    if (this._isEnabled() && this._transport) {\n      return this._transport.send(envelope).then(null, reason => {\n        DEBUG_BUILD && logger.error('Error while sending envelope:', reason);\n        return reason;\n      });\n    }\n\n    DEBUG_BUILD && logger.error('Transport disabled');\n\n    return resolvedSyncPromise({});\n  }\n\n  /* eslint-enable @typescript-eslint/unified-signatures */\n\n  /** Setup integrations for this client. */\n  protected _setupIntegrations(): void {\n    const { integrations } = this._options;\n    this._integrations = setupIntegrations(this, integrations);\n    afterSetupIntegrations(this, integrations);\n  }\n\n  /** Updates existing session based on the provided event */\n  protected _updateSessionFromEvent(session: Session, event: Event): void {\n    let crashed = event.level === 'fatal';\n    let errored = false;\n    const exceptions = event.exception?.values;\n\n    if (exceptions) {\n      errored = true;\n\n      for (const ex of exceptions) {\n        const mechanism = ex.mechanism;\n        if (mechanism?.handled === false) {\n          crashed = true;\n          break;\n        }\n      }\n    }\n\n    // A session is updated and that session update is sent in only one of the two following scenarios:\n    // 1. Session with non terminal status and 0 errors + an error occurred -> Will set error count to 1 and send update\n    // 2. Session with non terminal status and 1 error + a crash occurred -> Will set status crashed and send update\n    const sessionNonTerminal = session.status === 'ok';\n    const shouldUpdateAndSend = (sessionNonTerminal && session.errors === 0) || (sessionNonTerminal && crashed);\n\n    if (shouldUpdateAndSend) {\n      updateSession(session, {\n        ...(crashed && { status: 'crashed' }),\n        errors: session.errors || Number(errored || crashed),\n      });\n      this.captureSession(session);\n    }\n  }\n\n  /**\n   * Determine if the client is finished processing. Returns a promise because it will wait `timeout` ms before saying\n   * \"no\" (resolving to `false`) in order to give the client a chance to potentially finish first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the client is still busy. Passing `0` (or not\n   * passing anything) will make the promise wait as long as it takes for processing to finish before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if processing is already done or finishes before the timeout, and\n   * `false` otherwise\n   */\n  protected _isClientDoneProcessing(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise(resolve => {\n      let ticked: number = 0;\n      const tick: number = 1;\n\n      const interval = setInterval(() => {\n        if (this._numProcessing == 0) {\n          clearInterval(interval);\n          resolve(true);\n        } else {\n          ticked += tick;\n          if (timeout && ticked >= timeout) {\n            clearInterval(interval);\n            resolve(false);\n          }\n        }\n      }, tick);\n    });\n  }\n\n  /** Determines whether this SDK is enabled and a transport is present. */\n  protected _isEnabled(): boolean {\n    return this.getOptions().enabled !== false && this._transport !== undefined;\n  }\n\n  /**\n   * Adds common information to events.\n   *\n   * The information includes release and environment from `options`,\n   * breadcrumbs and context (extra, tags and user) from the scope.\n   *\n   * Information that is already present in the event is never overwritten. For\n   * nested objects, such as the context, keys are merged.\n   *\n   * @param event The original event.\n   * @param hint May contain additional information about the original exception.\n   * @param currentScope A scope containing event metadata.\n   * @returns A new event with more information.\n   */\n  protected _prepareEvent(\n    event: Event,\n    hint: EventHint,\n    currentScope: Scope,\n    isolationScope: Scope,\n  ): PromiseLike<Event | null> {\n    const options = this.getOptions();\n    const integrations = Object.keys(this._integrations);\n    if (!hint.integrations && integrations?.length) {\n      hint.integrations = integrations;\n    }\n\n    this.emit('preprocessEvent', event, hint);\n\n    if (!event.type) {\n      isolationScope.setLastEventId(event.event_id || hint.event_id);\n    }\n\n    return prepareEvent(options, event, hint, currentScope, this, isolationScope).then(evt => {\n      if (evt === null) {\n        return evt;\n      }\n\n      this.emit('postprocessEvent', evt, hint);\n\n      evt.contexts = {\n        trace: getTraceContextFromScope(currentScope),\n        ...evt.contexts,\n      };\n\n      const dynamicSamplingContext = getDynamicSamplingContextFromScope(this, currentScope);\n\n      evt.sdkProcessingMetadata = {\n        dynamicSamplingContext,\n        ...evt.sdkProcessingMetadata,\n      };\n\n      return evt;\n    });\n  }\n\n  /**\n   * Processes the event and logs an error in case of rejection\n   * @param event\n   * @param hint\n   * @param scope\n   */\n  protected _captureEvent(\n    event: Event,\n    hint: EventHint = {},\n    currentScope = getCurrentScope(),\n    isolationScope = getIsolationScope(),\n  ): PromiseLike<string | undefined> {\n    if (DEBUG_BUILD && isErrorEvent(event)) {\n      logger.log(`Captured error event \\`${getPossibleEventMessages(event)[0] || '<unknown>'}\\``);\n    }\n\n    return this._processEvent(event, hint, currentScope, isolationScope).then(\n      finalEvent => {\n        return finalEvent.event_id;\n      },\n      reason => {\n        if (DEBUG_BUILD) {\n          if (_isDoNotSendEventError(reason)) {\n            logger.log(reason.message);\n          } else if (_isInternalError(reason)) {\n            logger.warn(reason.message);\n          } else {\n            logger.warn(reason);\n          }\n        }\n        return undefined;\n      },\n    );\n  }\n\n  /**\n   * Processes an event (either error or message) and sends it to Sentry.\n   *\n   * This also adds breadcrumbs and context information to the event. However,\n   * platform specific meta data (such as the User's IP address) must be added\n   * by the SDK implementor.\n   *\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param currentScope A scope containing event metadata.\n   * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.\n   */\n  protected _processEvent(\n    event: Event,\n    hint: EventHint,\n    currentScope: Scope,\n    isolationScope: Scope,\n  ): PromiseLike<Event> {\n    const options = this.getOptions();\n    const { sampleRate } = options;\n\n    const isTransaction = isTransactionEvent(event);\n    const isError = isErrorEvent(event);\n    const eventType = event.type || 'error';\n    const beforeSendLabel = `before send for type \\`${eventType}\\``;\n\n    // 1.0 === 100% events are sent\n    // 0.0 === 0% events are sent\n    // Sampling for transaction happens somewhere else\n    const parsedSampleRate = typeof sampleRate === 'undefined' ? undefined : parseSampleRate(sampleRate);\n    if (isError && typeof parsedSampleRate === 'number' && Math.random() > parsedSampleRate) {\n      this.recordDroppedEvent('sample_rate', 'error');\n      return rejectedSyncPromise(\n        _makeDoNotSendEventError(\n          `Discarding event because it's not included in the random sample (sampling rate = ${sampleRate})`,\n        ),\n      );\n    }\n\n    const dataCategory = (eventType === 'replay_event' ? 'replay' : eventType) satisfies DataCategory;\n\n    return this._prepareEvent(event, hint, currentScope, isolationScope)\n      .then(prepared => {\n        if (prepared === null) {\n          this.recordDroppedEvent('event_processor', dataCategory);\n          throw _makeDoNotSendEventError('An event processor returned `null`, will not send event.');\n        }\n\n        const isInternalException = hint.data && (hint.data as { __sentry__: boolean }).__sentry__ === true;\n        if (isInternalException) {\n          return prepared;\n        }\n\n        const result = processBeforeSend(this, options, prepared, hint);\n        return _validateBeforeSendResult(result, beforeSendLabel);\n      })\n      .then(processedEvent => {\n        if (processedEvent === null) {\n          this.recordDroppedEvent('before_send', dataCategory);\n          if (isTransaction) {\n            const spans = event.spans || [];\n            // the transaction itself counts as one span, plus all the child spans that are added\n            const spanCount = 1 + spans.length;\n            this.recordDroppedEvent('before_send', 'span', spanCount);\n          }\n          throw _makeDoNotSendEventError(`${beforeSendLabel} returned \\`null\\`, will not send event.`);\n        }\n\n        const session = currentScope.getSession() || isolationScope.getSession();\n        if (isError && session) {\n          this._updateSessionFromEvent(session, processedEvent);\n        }\n\n        if (isTransaction) {\n          const spanCountBefore = processedEvent.sdkProcessingMetadata?.spanCountBeforeProcessing || 0;\n          const spanCountAfter = processedEvent.spans ? processedEvent.spans.length : 0;\n\n          const droppedSpanCount = spanCountBefore - spanCountAfter;\n          if (droppedSpanCount > 0) {\n            this.recordDroppedEvent('before_send', 'span', droppedSpanCount);\n          }\n        }\n\n        // None of the Sentry built event processor will update transaction name,\n        // so if the transaction name has been changed by an event processor, we know\n        // it has to come from custom event processor added by a user\n        const transactionInfo = processedEvent.transaction_info;\n        if (isTransaction && transactionInfo && processedEvent.transaction !== event.transaction) {\n          const source = 'custom';\n          processedEvent.transaction_info = {\n            ...transactionInfo,\n            source,\n          };\n        }\n\n        this.sendEvent(processedEvent, hint);\n        return processedEvent;\n      })\n      .then(null, reason => {\n        if (_isDoNotSendEventError(reason) || _isInternalError(reason)) {\n          throw reason;\n        }\n\n        this.captureException(reason, {\n          data: {\n            __sentry__: true,\n          },\n          originalException: reason,\n        });\n        throw _makeInternalError(\n          `Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\\nReason: ${reason}`,\n        );\n      });\n  }\n\n  /**\n   * Occupies the client with processing and event\n   */\n  protected _process<T>(promise: PromiseLike<T>): void {\n    this._numProcessing++;\n    void promise.then(\n      value => {\n        this._numProcessing--;\n        return value;\n      },\n      reason => {\n        this._numProcessing--;\n        return reason;\n      },\n    );\n  }\n\n  /**\n   * Clears outcomes on this client and returns them.\n   */\n  protected _clearOutcomes(): Outcome[] {\n    const outcomes = this._outcomes;\n    this._outcomes = {};\n    return Object.entries(outcomes).map(([key, quantity]) => {\n      const [reason, category] = key.split(':') as [EventDropReason, DataCategory];\n      return {\n        reason,\n        category,\n        quantity,\n      };\n    });\n  }\n\n  /**\n   * Sends client reports as an envelope.\n   */\n  protected _flushOutcomes(): void {\n    DEBUG_BUILD && logger.log('Flushing outcomes...');\n\n    const outcomes = this._clearOutcomes();\n\n    if (outcomes.length === 0) {\n      DEBUG_BUILD && logger.log('No outcomes to send');\n      return;\n    }\n\n    // This is really the only place where we want to check for a DSN and only send outcomes then\n    if (!this._dsn) {\n      DEBUG_BUILD && logger.log('No dsn provided, will not send outcomes');\n      return;\n    }\n\n    DEBUG_BUILD && logger.log('Sending outcomes:', outcomes);\n\n    const envelope = createClientReportEnvelope(outcomes, this._options.tunnel && dsnToString(this._dsn));\n\n    // sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.sendEnvelope(envelope);\n  }\n\n  /**\n   * Creates an {@link Event} from all inputs to `captureException` and non-primitive inputs to `captureMessage`.\n   */\n  public abstract eventFromException(_exception: unknown, _hint?: EventHint): PromiseLike<Event>;\n\n  /**\n   * Creates an {@link Event} from primitive inputs to `captureMessage`.\n   */\n  public abstract eventFromMessage(\n    _message: ParameterizedString,\n    _level?: SeverityLevel,\n    _hint?: EventHint,\n  ): PromiseLike<Event>;\n}\n\n/**\n * @deprecated Use `Client` instead. This alias may be removed in a future major version.\n */\n// TODO(v10): Remove\nexport type BaseClient = Client;\n\n/**\n * @deprecated Use `Client` instead. This alias may be removed in a future major version.\n */\n// TODO(v10): Remove\nexport const BaseClient = Client;\n\n/**\n * Verifies that return value of configured `beforeSend` or `beforeSendTransaction` is of expected type, and returns the value if so.\n */\nfunction _validateBeforeSendResult(\n  beforeSendResult: PromiseLike<Event | null> | Event | null,\n  beforeSendLabel: string,\n): PromiseLike<Event | null> | Event | null {\n  const invalidValueError = `${beforeSendLabel} must return \\`null\\` or a valid event.`;\n  if (isThenable(beforeSendResult)) {\n    return beforeSendResult.then(\n      event => {\n        if (!isPlainObject(event) && event !== null) {\n          throw _makeInternalError(invalidValueError);\n        }\n        return event;\n      },\n      e => {\n        throw _makeInternalError(`${beforeSendLabel} rejected with ${e}`);\n      },\n    );\n  } else if (!isPlainObject(beforeSendResult) && beforeSendResult !== null) {\n    throw _makeInternalError(invalidValueError);\n  }\n  return beforeSendResult;\n}\n\n/**\n * Process the matching `beforeSendXXX` callback.\n */\nfunction processBeforeSend(\n  client: Client,\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n): PromiseLike<Event | null> | Event | null {\n  const { beforeSend, beforeSendTransaction, beforeSendSpan } = options;\n  let processedEvent = event;\n\n  if (isErrorEvent(processedEvent) && beforeSend) {\n    return beforeSend(processedEvent, hint);\n  }\n\n  if (isTransactionEvent(processedEvent)) {\n    if (beforeSendSpan) {\n      // process root span\n      const processedRootSpanJson = beforeSendSpan(convertTransactionEventToSpanJson(processedEvent));\n      if (!processedRootSpanJson) {\n        showSpanDropWarning();\n      } else {\n        // update event with processed root span values\n        processedEvent = merge(event, convertSpanJsonToTransactionEvent(processedRootSpanJson));\n      }\n\n      // process child spans\n      if (processedEvent.spans) {\n        const processedSpans: SpanJSON[] = [];\n        for (const span of processedEvent.spans) {\n          const processedSpan = beforeSendSpan(span);\n          if (!processedSpan) {\n            showSpanDropWarning();\n            processedSpans.push(span);\n          } else {\n            processedSpans.push(processedSpan);\n          }\n        }\n        processedEvent.spans = processedSpans;\n      }\n    }\n\n    if (beforeSendTransaction) {\n      if (processedEvent.spans) {\n        // We store the # of spans before processing in SDK metadata,\n        // so we can compare it afterwards to determine how many spans were dropped\n        const spanCountBefore = processedEvent.spans.length;\n        processedEvent.sdkProcessingMetadata = {\n          ...event.sdkProcessingMetadata,\n          spanCountBeforeProcessing: spanCountBefore,\n        };\n      }\n      return beforeSendTransaction(processedEvent as TransactionEvent, hint);\n    }\n  }\n\n  return processedEvent;\n}\n\nfunction isErrorEvent(event: Event): event is ErrorEvent {\n  return event.type === undefined;\n}\n\nfunction isTransactionEvent(event: Event): event is TransactionEvent {\n  return event.type === 'transaction';\n}\n\n/** Extract trace information from scope */\nexport function _getTraceInfoFromScope(\n  client: Client,\n  scope: Scope | undefined,\n): [dynamicSamplingContext: Partial<DynamicSamplingContext> | undefined, traceContext: TraceContext | undefined] {\n  if (!scope) {\n    return [undefined, undefined];\n  }\n\n  return withScope(scope, () => {\n    const span = getActiveSpan();\n    const traceContext = span ? spanToTraceContext(span) : getTraceContextFromScope(scope);\n    const dynamicSamplingContext = span\n      ? getDynamicSamplingContextFromSpan(span)\n      : getDynamicSamplingContextFromScope(client, scope);\n    return [dynamicSamplingContext, traceContext];\n  });\n}\n"], "names": ["makeDsn", "DEBUG_BUILD", "logger", "getEnvelopeEndpointWithUrlEncodedAuth", "uuid4", "checkOrSetAlreadyCaught", "isParameterizedString", "isPrimitive", "session", "updateSession", "resolvedSyncPromise", "integration", "setupIntegration", "afterSetupIntegrations", "createEventEnvelope", "addItemToEnvelope", "createAttachmentEnvelopeItem", "DEFAULT_ENVIRONMENT", "createSessionEnvelope", "setupIntegrations", "SyncPromise", "prepareEvent", "getTraceContextFromScope", "dynamicSamplingContext", "getDynamicSamplingContextFromScope", "getCurrentScope", "getIsolationScope", "getPossibleEventMessages", "parseSampleRate", "rejectedSyncPromise", "createClientReportEnvelope", "dsnToString", "isThenable", "isPlainObject", "convertTransactionEventToSpanJson", "showSpanDropWarning", "merge", "convertSpanJsonToTransactionEvent", "withScope", "getActiveSpan", "spanToTraceContext", "getDynamicSamplingContextFromSpan"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAgDA,MAAM,kBAAA,GAAqB,6DAA6D;AACxF,MAAM,iCAAA,GAAoC,4DAA4D;;AAEtG,MAAM,wBAAwB,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC;AAC/D,MAAM,2BAA2B,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC;;AAYxE,SAAS,kBAAkB,CAAC,OAAO,EAAyB;AAC5D,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,CAAC,qBAAqB,GAAG,IAAI;AACjC,GAAG;AACH;;AAEA,SAAS,wBAAwB,CAAC,OAAO,EAA+B;AACxE,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,CAAC,wBAAwB,GAAG,IAAI;AACpC,GAAG;AACH;;AAEA,SAAS,gBAAgB,CAAC,KAAK,EAAmC;AAClE,EAAE,OAAO,CAAC,CAAC,KAAA,IAAS,OAAO,KAAM,KAAI,QAAS,IAAG,qBAAsB,IAAG,KAAK;AAC/E;;AAEA,SAAS,sBAAsB,CAAC,KAAK,EAAyC;AAC9E,EAAE,OAAO,CAAC,CAAC,KAAA,IAAS,OAAO,KAAM,KAAI,QAAS,IAAG,wBAAyB,IAAG,KAAK;AAClF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACgB,MAAM,MAAM,CAA0C;AACtE;;AAGA;;AAKA;;AAGA;;AAKA;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA,GAAY,WAAW,CAAC,OAAO,EAAK;AACpC,IAAI,IAAI,CAAC,QAAS,GAAE,OAAO;AAC3B,IAAI,IAAI,CAAC,aAAc,GAAE,EAAE;AAC3B,IAAI,IAAI,CAAC,cAAe,GAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE;AACvB,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE;AACpB,IAAI,IAAI,CAAC,gBAAiB,GAAE,EAAE;;AAE9B,IAAI,IAAI,OAAO,CAAC,GAAG,EAAE;AACrB,MAAM,IAAI,CAAC,IAAK,GAAEA,WAAO,CAAC,OAAO,CAAC,GAAG,CAAC;AACtC,WAAW;AACX,MAAMC,0BAAeC,aAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC;AACjF;;AAEA,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AACnB,MAAM,MAAM,GAAI,GAAEC,yCAAqC;AACvD,QAAQ,IAAI,CAAC,IAAI;AACjB,QAAQ,OAAO,CAAC,MAAM;AACtB,QAAQ,OAAO,CAAC,SAAA,GAAY,OAAO,CAAC,SAAS,CAAC,GAAI,GAAE,SAAS;AAC7D,OAAO;AACP,MAAM,IAAI,CAAC,UAAA,GAAa,OAAO,CAAC,SAAS,CAAC;AAC1C,QAAQ,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;AACpC,QAAQ,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9D,QAAQ,GAAG,OAAO,CAAC,gBAAgB;AACnC,QAAQ,GAAG;AACX,OAAO,CAAC;AACR;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAc,KAAK,EAAkB;AACvF,IAAI,MAAM,OAAA,GAAUC,UAAK,EAAE;;AAE3B;AACA,IAAI,IAAIC,4BAAuB,CAAC,SAAS,CAAC,EAAE;AAC5C,MAAMJ,0BAAeC,aAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACnD,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,MAAM,kBAAkB;AAC5B,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,GAAG,IAAI;AACb,KAAK;;AAEL,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,KAAM;AACrE,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,eAAe,EAAE,KAAK,CAAC;AACzD,OAAO;AACP,KAAK;;AAEL,IAAI,OAAO,eAAe,CAAC,QAAQ;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc;AACvB,IAAI,OAAO;AACX,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,YAAY;AAChB,IAAY;AACZ,IAAI,MAAM,kBAAkB;AAC5B,MAAM,QAAQ,EAAEE,UAAK,EAAE;AACvB,MAAM,GAAG,IAAI;AACb,KAAK;;AAEL,IAAI,MAAM,YAAA,GAAeE,wBAAqB,CAAC,OAAO,CAAE,GAAE,OAAQ,GAAE,MAAM,CAAC,OAAO,CAAC;;AAEnF,IAAI,MAAM,aAAA,GAAgBC,cAAW,CAAC,OAAO;AAC7C,QAAQ,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,eAAe;AAClE,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,eAAe,CAAC;;AAEzD,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,KAAM,IAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC,CAAC;;AAExG,IAAI,OAAO,eAAe,CAAC,QAAQ;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAc,YAAY,EAAkB;AACpF,IAAI,MAAM,OAAA,GAAUH,UAAK,EAAE;;AAE3B;AACA,IAAI,IAAI,IAAI,EAAE,iBAAkB,IAAGC,4BAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;AACpF,MAAMJ,0BAAeC,aAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACnD,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,MAAM,kBAAkB;AAC5B,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,GAAG,IAAI;AACb,KAAK;;AAEL,IAAI,MAAM,wBAAwB,KAAK,CAAC,qBAAsB,IAAG,EAAE;AACnE,IAAI,MAAM,iBAAiB,GAAsB,qBAAqB,CAAC,iBAAiB;AACxF,IAAI,MAAM,0BAA0B,GAAsB,qBAAqB,CAAC,0BAA0B;;AAE1G,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,eAAe,EAAE,iBAAkB,IAAG,YAAY,EAAE,0BAA0B,CAAC;AAC/G,KAAK;;AAEL,IAAI,OAAO,eAAe,CAAC,QAAQ;AACnC;;AAEA;AACA;AACA;AACA,GAAS,cAAc,CAACM,SAAO,EAAiB;AAChD,IAAI,IAAI,CAAC,WAAW,CAACA,SAAO,CAAC;AAC7B;AACA,IAAIC,qBAAa,CAACD,SAAO,EAAE,EAAE,IAAI,EAAE,KAAM,EAAC,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA,GAAS,MAAM,GAA8B;AAC7C,IAAI,OAAO,IAAI,CAAC,IAAI;AACpB;;AAEA;AACA;AACA;AACA,GAAS,UAAU,GAAM;AACzB,IAAI,OAAO,IAAI,CAAC,QAAQ;AACxB;;AAEA;AACA;AACA;AACA;AACA,GAAS,cAAc,GAA4B;AACnD,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS;AAClC;;AAEA;AACA;AACA;AACA;AACA,GAAS,YAAY,GAA0B;AAC/C,IAAI,OAAO,IAAI,CAAC,UAAU;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,KAAK,CAAC,OAAO,EAAiC;AACvD,IAAI,MAAM,SAAA,GAAY,IAAI,CAAC,UAAU;AACrC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACxB,MAAM,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAA,IAAkB;AAC1E,QAAQ,OAAO,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAiB,IAAG,cAAe,IAAG,gBAAgB,CAAC;AACpG,OAAO,CAAC;AACR,WAAW;AACX,MAAM,OAAOE,+BAAmB,CAAC,IAAI,CAAC;AACtC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,KAAK,CAAC,OAAO,EAAiC;AACvD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAA,IAAU;AAC9C,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,OAAA,GAAU,KAAK;AACvC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACxB,MAAM,OAAO,MAAM;AACnB,KAAK,CAAC;AACN;;AAEA;AACA;AACA;AACA,GAAS,kBAAkB,GAAqB;AAChD,IAAI,OAAO,IAAI,CAAC,gBAAgB;AAChC;;AAEA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,cAAc,EAAwB;AACjE,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC;AAC9C;;AAEA;AACA;AACA;AACA;AACA,GAAS,IAAI,GAAS;AACtB,IAAI;AACJ,MAAM,IAAI,CAAC,UAAU,EAAG;AACxB;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAK,EAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;AAChF,MAAM;AACN,MAAM,IAAI,CAAC,kBAAkB,EAAE;AAC/B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,oBAAoB,CAAsC,eAAe,EAAyB;AAC3G,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAE;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAACC,aAAW,EAAqB;AACxD,IAAI,MAAM,kBAAmB,GAAE,IAAI,CAAC,aAAa,CAACA,aAAW,CAAC,IAAI,CAAC;;AAEnE;AACA,IAAIC,4BAAgB,CAAC,IAAI,EAAED,aAAW,EAAE,IAAI,CAAC,aAAa,CAAC;AAC3D;AACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC7B,MAAME,kCAAsB,CAAC,IAAI,EAAE,CAACF,aAAW,CAAC,CAAC;AACjD;AACA;;AAEA;AACA;AACA;AACA,GAAS,SAAS,CAAC,KAAK,EAAS,IAAI,GAAc,EAAE,EAAQ;AAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC;;AAE7C,IAAI,IAAI,MAAMG,4BAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;;AAElG,IAAI,KAAK,MAAM,UAAW,IAAG,IAAI,CAAC,WAAY,IAAG,EAAE,EAAE;AACrD,MAAM,GAAA,GAAMC,4BAAiB,CAAC,GAAG,EAAEC,uCAA4B,CAAC,UAAU,CAAC,CAAC;AAC5E;;AAEA,IAAI,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;AAC1C,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,CAAC,IAAI,CAAC,YAAa,IAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC;AAC1F;AACA;;AAEA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,EAAqC;AACjE;AACA,IAAI,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,uBAAA,GAA0BC,6BAAoB,EAAA,GAAI,IAAI,CAAC,QAAQ;AACtH,IAAI,IAAI,YAAa,IAAG,OAAO,EAAE;AACjC,MAAM,MAAM,eAAe,OAAO,CAAC,KAAM,IAAG,EAAE;AAC9C,MAAM,IAAI,CAAC,YAAY,CAAC,OAAQ,IAAG,CAAC,mBAAmB,EAAE;AACzD,QAAQhB,0BAAeC,aAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC;AACrE,QAAQ;AACR;AACA,MAAM,YAAY,CAAC,OAAQ,GAAE,YAAY,CAAC,OAAA,IAAW,mBAAmB;AACxE,MAAM,YAAY,CAAC,WAAY,GAAE,YAAY,CAAC,WAAA,IAAe,uBAAuB;AACpF,MAAM,OAAO,CAAC,KAAM,GAAE,YAAY;AAClC,WAAW;AACX,MAAM,IAAI,CAAC,OAAO,CAAC,OAAQ,IAAG,CAAC,mBAAmB,EAAE;AACpD,QAAQD,0BAAeC,aAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC;AACrE,QAAQ;AACR;AACA,MAAM,OAAO,CAAC,OAAQ,GAAE,OAAO,CAAC,OAAA,IAAW,mBAAmB;AAC9D,MAAM,OAAO,CAAC,WAAY,GAAE,OAAO,CAAC,WAAA,IAAe,uBAAuB;AAC1E;;AAEA,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC;;AAE3C,IAAI,MAAM,MAAMgB,8BAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;;AAExG;AACA;AACA,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;AAC1B;;AAEA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,MAAM,EAAmB,QAAQ,EAAgB,KAAK,GAAW,CAAC,EAAQ;AACtG,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAA,GAAM,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,QAAA,CAAA,CAAA;AACA,MAAAjB,sBAAA,IAAAC,aAAA,CAAA,GAAA,CAAA,CAAA,oBAAA,EAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,CAAA,GAAA,CAAA,EAAA,EAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,KAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAyLA;AACA;AACA;AACA,GAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA;AACA,IAAA,MAAA,KAAA,IAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA;;AAEA;AACA,IAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA,IAAA,OAAA,MAAA;AACA;AACA,MAAA,MAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA;AACA,MAAA,IAAA,OAAA,GAAA,EAAA,EAAA;AACA,QAAA,KAAA,CAAA,MAAA,CAAA,OAAA,EAAA,CAAA,CAAA;AACA;AACA,KAAA;AACA;;AAEA;;AA6IA;AACA;AACA;AACA,GAAA,IAAA,CAAA,IAAA,EAAA,GAAA,IAAA,EAAA;AACA,IAAA,MAAA,SAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AACA,IAAA,IAAA,SAAA,EAAA;AACA,MAAA,SAAA,CAAA,OAAA,CAAA,QAAA,IAAA,QAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAA,YAAA,CAAA,QAAA,EAAA;AACA,IAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,QAAA,CAAA;;AAEA,IAAA,IAAA,IAAA,CAAA,UAAA,EAAA,IAAA,IAAA,CAAA,UAAA,EAAA;AACA,MAAA,OAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,MAAA,IAAA;AACA,QAAAD,sBAAA,IAAAC,aAAA,CAAA,KAAA,CAAA,+BAAA,EAAA,MAAA,CAAA;AACA,QAAA,OAAA,MAAA;AACA,OAAA,CAAA;AACA;;AAEA,IAAAD,sBAAA,IAAAC,aAAA,CAAA,KAAA,CAAA,oBAAA,CAAA;;AAEA,IAAA,OAAAQ,+BAAA,CAAA,EAAA,CAAA;AACA;;AAEA;;AAEA;AACA,GAAA,kBAAA,GAAA;AACA,IAAA,MAAA,EAAA,YAAA,EAAA,GAAA,IAAA,CAAA,QAAA;AACA,IAAA,IAAA,CAAA,aAAA,GAAAS,6BAAA,CAAA,IAAA,EAAA,YAAA,CAAA;AACA,IAAAN,kCAAA,CAAA,IAAA,EAAA,YAAA,CAAA;AACA;;AAEA;AACA,GAAA,uBAAA,CAAAL,SAAA,EAAA,KAAA,EAAA;AACA,IAAA,IAAA,OAAA,GAAA,KAAA,CAAA,KAAA,KAAA,OAAA;AACA,IAAA,IAAA,OAAA,GAAA,KAAA;AACA,IAAA,MAAA,UAAA,GAAA,KAAA,CAAA,SAAA,EAAA,MAAA;;AAEA,IAAA,IAAA,UAAA,EAAA;AACA,MAAA,OAAA,GAAA,IAAA;;AAEA,MAAA,KAAA,MAAA,EAAA,IAAA,UAAA,EAAA;AACA,QAAA,MAAA,SAAA,GAAA,EAAA,CAAA,SAAA;AACA,QAAA,IAAA,SAAA,EAAA,OAAA,KAAA,KAAA,EAAA;AACA,UAAA,OAAA,GAAA,IAAA;AACA,UAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAA,MAAA,kBAAA,GAAAA,SAAA,CAAA,MAAA,KAAA,IAAA;AACA,IAAA,MAAA,mBAAA,GAAA,CAAA,kBAAA,IAAAA,SAAA,CAAA,MAAA,KAAA,CAAA,MAAA,kBAAA,IAAA,OAAA,CAAA;;AAEA,IAAA,IAAA,mBAAA,EAAA;AACA,MAAAC,qBAAA,CAAAD,SAAA,EAAA;AACA,QAAA,IAAA,OAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,CAAA;AACA,QAAA,MAAA,EAAAA,SAAA,CAAA,MAAA,IAAA,MAAA,CAAA,OAAA,IAAA,OAAA,CAAA;AACA,OAAA,CAAA;AACA,MAAA,IAAA,CAAA,cAAA,CAAAA,SAAA,CAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,uBAAA,CAAA,OAAA,EAAA;AACA,IAAA,OAAA,IAAAY,uBAAA,CAAA,OAAA,IAAA;AACA,MAAA,IAAA,MAAA,GAAA,CAAA;AACA,MAAA,MAAA,IAAA,GAAA,CAAA;;AAEA,MAAA,MAAA,QAAA,GAAA,WAAA,CAAA,MAAA;AACA,QAAA,IAAA,IAAA,CAAA,cAAA,IAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,QAAA,CAAA;AACA,UAAA,OAAA,CAAA,IAAA,CAAA;AACA,SAAA,MAAA;AACA,UAAA,MAAA,IAAA,IAAA;AACA,UAAA,IAAA,OAAA,IAAA,MAAA,IAAA,OAAA,EAAA;AACA,YAAA,aAAA,CAAA,QAAA,CAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA;AACA;AACA;AACA,OAAA,EAAA,IAAA,CAAA;AACA,KAAA,CAAA;AACA;;AAEA;AACA,GAAA,UAAA,GAAA;AACA,IAAA,OAAA,IAAA,CAAA,UAAA,EAAA,CAAA,OAAA,KAAA,KAAA,IAAA,IAAA,CAAA,UAAA,KAAA,SAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,aAAA;AACA,IAAA,KAAA;AACA,IAAA,IAAA;AACA,IAAA,YAAA;AACA,IAAA,cAAA;AACA,IAAA;AACA,IAAA,MAAA,OAAA,GAAA,IAAA,CAAA,UAAA,EAAA;AACA,IAAA,MAAA,YAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,aAAA,CAAA;AACA,IAAA,IAAA,CAAA,IAAA,CAAA,YAAA,IAAA,YAAA,EAAA,MAAA,EAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,YAAA;AACA;;AAEA,IAAA,IAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,CAAA;;AAEA,IAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,MAAA,cAAA,CAAA,cAAA,CAAA,KAAA,CAAA,QAAA,IAAA,IAAA,CAAA,QAAA,CAAA;AACA;;AAEA,IAAA,OAAAC,yBAAA,CAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,YAAA,EAAA,IAAA,EAAA,cAAA,CAAA,CAAA,IAAA,CAAA,GAAA,IAAA;AACA,MAAA,IAAA,GAAA,KAAA,IAAA,EAAA;AACA,QAAA,OAAA,GAAA;AACA;;AAEA,MAAA,IAAA,CAAA,IAAA,CAAA,kBAAA,EAAA,GAAA,EAAA,IAAA,CAAA;;AAEA,MAAA,GAAA,CAAA,QAAA,GAAA;AACA,QAAA,KAAA,EAAAC,sCAAA,CAAA,YAAA,CAAA;AACA,QAAA,GAAA,GAAA,CAAA,QAAA;AACA,OAAA;;AAEA,MAAA,MAAAC,wBAAA,GAAAC,yDAAA,CAAA,IAAA,EAAA,YAAA,CAAA;;AAEA,MAAA,GAAA,CAAA,qBAAA,GAAA;AACA,gCAAAD,wBAAA;AACA,QAAA,GAAA,GAAA,CAAA,qBAAA;AACA,OAAA;;AAEA,MAAA,OAAA,GAAA;AACA,KAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,aAAA;AACA,IAAA,KAAA;AACA,IAAA,IAAA,GAAA,EAAA;AACA,IAAA,YAAA,GAAAE,6BAAA,EAAA;AACA,IAAA,cAAA,GAAAC,+BAAA,EAAA;AACA,IAAA;AACA,IAAA,IAAAzB,sBAAA,IAAA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,MAAAC,aAAA,CAAA,GAAA,CAAA,CAAA,uBAAA,EAAAyB,mCAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AACA;;AAEA,IAAA,OAAA,IAAA,CAAA,aAAA,CAAA,KAAA,EAAA,IAAA,EAAA,YAAA,EAAA,cAAA,CAAA,CAAA,IAAA;AACA,MAAA,UAAA,IAAA;AACA,QAAA,OAAA,UAAA,CAAA,QAAA;AACA,OAAA;AACA,MAAA,MAAA,IAAA;AACA,QAAA,IAAA1B,sBAAA,EAAA;AACA,UAAA,IAAA,sBAAA,CAAA,MAAA,CAAA,EAAA;AACA,YAAAC,aAAA,CAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,WAAA,MAAA,IAAA,gBAAA,CAAA,MAAA,CAAA,EAAA;AACA,YAAAA,aAAA,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,WAAA,MAAA;AACA,YAAAA,aAAA,CAAA,IAAA,CAAA,MAAA,CAAA;AACA;AACA;AACA,QAAA,OAAA,SAAA;AACA,OAAA;AACA,KAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,aAAA;AACA,IAAA,KAAA;AACA,IAAA,IAAA;AACA,IAAA,YAAA;AACA,IAAA,cAAA;AACA,IAAA;AACA,IAAA,MAAA,OAAA,GAAA,IAAA,CAAA,UAAA,EAAA;AACA,IAAA,MAAA,EAAA,UAAA,EAAA,GAAA,OAAA;;AAEA,IAAA,MAAA,aAAA,GAAA,kBAAA,CAAA,KAAA,CAAA;AACA,IAAA,MAAA,OAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AACA,IAAA,MAAA,SAAA,GAAA,KAAA,CAAA,IAAA,IAAA,OAAA;AACA,IAAA,MAAA,eAAA,GAAA,CAAA,uBAAA,EAAA,SAAA,CAAA,EAAA,CAAA;;AAEA;AACA;AACA;AACA,IAAA,MAAA,gBAAA,GAAA,OAAA,UAAA,KAAA,WAAA,GAAA,SAAA,GAAA0B,+BAAA,CAAA,UAAA,CAAA;AACA,IAAA,IAAA,OAAA,IAAA,OAAA,gBAAA,KAAA,QAAA,IAAA,IAAA,CAAA,MAAA,EAAA,GAAA,gBAAA,EAAA;AACA,MAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,OAAA,CAAA;AACA,MAAA,OAAAC,+BAAA;AACA,QAAA,wBAAA;AACA,UAAA,CAAA,iFAAA,EAAA,UAAA,CAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA;AACA;;AAEA,IAAA,MAAA,YAAA,IAAA,SAAA,KAAA,cAAA,GAAA,QAAA,GAAA,SAAA,CAAA;;AAEA,IAAA,OAAA,IAAA,CAAA,aAAA,CAAA,KAAA,EAAA,IAAA,EAAA,YAAA,EAAA,cAAA;AACA,OAAA,IAAA,CAAA,QAAA,IAAA;AACA,QAAA,IAAA,QAAA,KAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,kBAAA,CAAA,iBAAA,EAAA,YAAA,CAAA;AACA,UAAA,MAAA,wBAAA,CAAA,0DAAA,CAAA;AACA;;AAEA,QAAA,MAAA,mBAAA,GAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,UAAA,KAAA,IAAA;AACA,QAAA,IAAA,mBAAA,EAAA;AACA,UAAA,OAAA,QAAA;AACA;;AAEA,QAAA,MAAA,MAAA,GAAA,iBAAA,CAAA,IAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,CAAA;AACA,QAAA,OAAA,yBAAA,CAAA,MAAA,EAAA,eAAA,CAAA;AACA,OAAA;AACA,OAAA,IAAA,CAAA,cAAA,IAAA;AACA,QAAA,IAAA,cAAA,KAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,YAAA,CAAA;AACA,UAAA,IAAA,aAAA,EAAA;AACA,YAAA,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,IAAA,EAAA;AACA;AACA,YAAA,MAAA,SAAA,GAAA,CAAA,GAAA,KAAA,CAAA,MAAA;AACA,YAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,MAAA,EAAA,SAAA,CAAA;AACA;AACA,UAAA,MAAA,wBAAA,CAAA,CAAA,EAAA,eAAA,CAAA,wCAAA,CAAA,CAAA;AACA;;AAEA,QAAA,MAAA,OAAA,GAAA,YAAA,CAAA,UAAA,EAAA,IAAA,cAAA,CAAA,UAAA,EAAA;AACA,QAAA,IAAA,OAAA,IAAA,OAAA,EAAA;AACA,UAAA,IAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,cAAA,CAAA;AACA;;AAEA,QAAA,IAAA,aAAA,EAAA;AACA,UAAA,MAAA,eAAA,GAAA,cAAA,CAAA,qBAAA,EAAA,yBAAA,IAAA,CAAA;AACA,UAAA,MAAA,cAAA,GAAA,cAAA,CAAA,KAAA,GAAA,cAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA;;AAEA,UAAA,MAAA,gBAAA,GAAA,eAAA,GAAA,cAAA;AACA,UAAA,IAAA,gBAAA,GAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,MAAA,EAAA,gBAAA,CAAA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAA,MAAA,eAAA,GAAA,cAAA,CAAA,gBAAA;AACA,QAAA,IAAA,aAAA,IAAA,eAAA,IAAA,cAAA,CAAA,WAAA,KAAA,KAAA,CAAA,WAAA,EAAA;AACA,UAAA,MAAA,MAAA,GAAA,QAAA;AACA,UAAA,cAAA,CAAA,gBAAA,GAAA;AACA,YAAA,GAAA,eAAA;AACA,YAAA,MAAA;AACA,WAAA;AACA;;AAEA,QAAA,IAAA,CAAA,SAAA,CAAA,cAAA,EAAA,IAAA,CAAA;AACA,QAAA,OAAA,cAAA;AACA,OAAA;AACA,OAAA,IAAA,CAAA,IAAA,EAAA,MAAA,IAAA;AACA,QAAA,IAAA,sBAAA,CAAA,MAAA,CAAA,IAAA,gBAAA,CAAA,MAAA,CAAA,EAAA;AACA,UAAA,MAAA,MAAA;AACA;;AAEA,QAAA,IAAA,CAAA,gBAAA,CAAA,MAAA,EAAA;AACA,UAAA,IAAA,EAAA;AACA,YAAA,UAAA,EAAA,IAAA;AACA,WAAA;AACA,UAAA,iBAAA,EAAA,MAAA;AACA,SAAA,CAAA;AACA,QAAA,MAAA,kBAAA;AACA,UAAA,CAAA,2HAAA,EAAA,MAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,GAAA,QAAA,CAAA,OAAA,EAAA;AACA,IAAA,IAAA,CAAA,cAAA,EAAA;AACA,IAAA,KAAA,OAAA,CAAA,IAAA;AACA,MAAA,KAAA,IAAA;AACA,QAAA,IAAA,CAAA,cAAA,EAAA;AACA,QAAA,OAAA,KAAA;AACA,OAAA;AACA,MAAA,MAAA,IAAA;AACA,QAAA,IAAA,CAAA,cAAA,EAAA;AACA,QAAA,OAAA,MAAA;AACA,OAAA;AACA,KAAA;AACA;;AAEA;AACA;AACA;AACA,GAAA,cAAA,GAAA;AACA,IAAA,MAAA,QAAA,GAAA,IAAA,CAAA,SAAA;AACA,IAAA,IAAA,CAAA,SAAA,GAAA,EAAA;AACA,IAAA,OAAA,MAAA,CAAA,OAAA,CAAA,QAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,EAAA,QAAA,CAAA,KAAA;AACA,MAAA,MAAA,CAAA,MAAA,EAAA,QAAA,CAAA,GAAA,GAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA,MAAA,OAAA;AACA,QAAA,MAAA;AACA,QAAA,QAAA;AACA,QAAA,QAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,GAAA,cAAA,GAAA;AACA,IAAA5B,sBAAA,IAAAC,aAAA,CAAA,GAAA,CAAA,sBAAA,CAAA;;AAEA,IAAA,MAAA,QAAA,GAAA,IAAA,CAAA,cAAA,EAAA;;AAEA,IAAA,IAAA,QAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,MAAAD,sBAAA,IAAAC,aAAA,CAAA,GAAA,CAAA,qBAAA,CAAA;AACA,MAAA;AACA;;AAEA;AACA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,MAAAD,sBAAA,IAAAC,aAAA,CAAA,GAAA,CAAA,yCAAA,CAAA;AACA,MAAA;AACA;;AAEA,IAAAD,sBAAA,IAAAC,aAAA,CAAA,GAAA,CAAA,mBAAA,EAAA,QAAA,CAAA;;AAEA,IAAA,MAAA,QAAA,GAAA4B,uCAAA,CAAA,QAAA,EAAA,IAAA,CAAA,QAAA,CAAA,MAAA,IAAAC,eAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;AAEA;AACA;AACA,IAAA,IAAA,CAAA,YAAA,CAAA,QAAA,CAAA;AACA;;AAEA;AACA;AACA;;AAWA;;AAEA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,MAAA,UAAA,GAAA;;AAEA;AACA;AACA;AACA,SAAA,yBAAA;AACA,EAAA,gBAAA;AACA,EAAA,eAAA;AACA,EAAA;AACA,EAAA,MAAA,iBAAA,GAAA,CAAA,EAAA,eAAA,CAAA,uCAAA,CAAA;AACA,EAAA,IAAAC,aAAA,CAAA,gBAAA,CAAA,EAAA;AACA,IAAA,OAAA,gBAAA,CAAA,IAAA;AACA,MAAA,KAAA,IAAA;AACA,QAAA,IAAA,CAAAC,gBAAA,CAAA,KAAA,CAAA,IAAA,KAAA,KAAA,IAAA,EAAA;AACA,UAAA,MAAA,kBAAA,CAAA,iBAAA,CAAA;AACA;AACA,QAAA,OAAA,KAAA;AACA,OAAA;AACA,MAAA,CAAA,IAAA;AACA,QAAA,MAAA,kBAAA,CAAA,CAAA,EAAA,eAAA,CAAA,eAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,MAAA,IAAA,CAAAA,gBAAA,CAAA,gBAAA,CAAA,IAAA,gBAAA,KAAA,IAAA,EAAA;AACA,IAAA,MAAA,kBAAA,CAAA,iBAAA,CAAA;AACA;AACA,EAAA,OAAA,gBAAA;AACA;;AAEA;AACA;AACA;AACA,SAAA,iBAAA;AACA,EAAA,MAAA;AACA,EAAA,OAAA;AACA,EAAA,KAAA;AACA,EAAA,IAAA;AACA,EAAA;AACA,EAAA,MAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,GAAA,OAAA;AACA,EAAA,IAAA,cAAA,GAAA,KAAA;;AAEA,EAAA,IAAA,YAAA,CAAA,cAAA,CAAA,IAAA,UAAA,EAAA;AACA,IAAA,OAAA,UAAA,CAAA,cAAA,EAAA,IAAA,CAAA;AACA;;AAEA,EAAA,IAAA,kBAAA,CAAA,cAAA,CAAA,EAAA;AACA,IAAA,IAAA,cAAA,EAAA;AACA;AACA,MAAA,MAAA,qBAAA,GAAA,cAAA,CAAAC,kDAAA,CAAA,cAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,qBAAA,EAAA;AACA,QAAAC,6BAAA,EAAA;AACA,OAAA,MAAA;AACA;AACA,QAAA,cAAA,GAAAC,WAAA,CAAA,KAAA,EAAAC,kDAAA,CAAA,qBAAA,CAAA,CAAA;AACA;;AAEA;AACA,MAAA,IAAA,cAAA,CAAA,KAAA,EAAA;AACA,QAAA,MAAA,cAAA,GAAA,EAAA;AACA,QAAA,KAAA,MAAA,IAAA,IAAA,cAAA,CAAA,KAAA,EAAA;AACA,UAAA,MAAA,aAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,CAAA,aAAA,EAAA;AACA,YAAAF,6BAAA,EAAA;AACA,YAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAAA,MAAA;AACA,YAAA,cAAA,CAAA,IAAA,CAAA,aAAA,CAAA;AACA;AACA;AACA,QAAA,cAAA,CAAA,KAAA,GAAA,cAAA;AACA;AACA;;AAEA,IAAA,IAAA,qBAAA,EAAA;AACA,MAAA,IAAA,cAAA,CAAA,KAAA,EAAA;AACA;AACA;AACA,QAAA,MAAA,eAAA,GAAA,cAAA,CAAA,KAAA,CAAA,MAAA;AACA,QAAA,cAAA,CAAA,qBAAA,GAAA;AACA,UAAA,GAAA,KAAA,CAAA,qBAAA;AACA,UAAA,yBAAA,EAAA,eAAA;AACA,SAAA;AACA;AACA,MAAA,OAAA,qBAAA,CAAA,cAAA,GAAA,IAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,cAAA;AACA;;AAEA,SAAA,YAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,KAAA,CAAA,IAAA,KAAA,SAAA;AACA;;AAEA,SAAA,kBAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,KAAA,CAAA,IAAA,KAAA,aAAA;AACA;;AAEA;AACA,SAAA,sBAAA;AACA,EAAA,MAAA;AACA,EAAA,KAAA;AACA,EAAA;AACA,EAAA,IAAA,CAAA,KAAA,EAAA;AACA,IAAA,OAAA,CAAA,SAAA,EAAA,SAAA,CAAA;AACA;;AAEA,EAAA,OAAAG,uBAAA,CAAA,KAAA,EAAA,MAAA;AACA,IAAA,MAAA,IAAA,GAAAC,uBAAA,EAAA;AACA,IAAA,MAAA,YAAA,GAAA,IAAA,GAAAC,4BAAA,CAAA,IAAA,CAAA,GAAAlB,sCAAA,CAAA,KAAA,CAAA;AACA,IAAA,MAAAC,wBAAA,GAAA;AACA,QAAAkB,wDAAA,CAAA,IAAA;AACA,QAAAjB,yDAAA,CAAA,MAAA,EAAA,KAAA,CAAA;AACA,IAAA,OAAA,CAAAD,wBAAA,EAAA,YAAA,CAAA;AACA,GAAA,CAAA;AACA;;;;;;"}