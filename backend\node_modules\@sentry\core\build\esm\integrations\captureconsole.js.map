{"version": 3, "file": "captureconsole.js", "sources": ["../../../src/integrations/captureconsole.ts"], "sourcesContent": ["import { getClient, withScope } from '../currentScopes';\nimport { captureException, captureMessage } from '../exports';\nimport { addConsoleInstrumentationHandler } from '../instrument/console';\nimport { defineIntegration } from '../integration';\nimport type { CaptureContext } from '../scope';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport { CONSOLE_LEVELS } from '../utils/logger';\nimport { addExceptionMechanism } from '../utils/misc';\nimport { severityLevelFromString } from '../utils/severity';\nimport { safeJoin } from '../utils/string';\nimport { GLOBAL_OBJ } from '../utils/worldwide';\n\ninterface CaptureConsoleOptions {\n  levels?: string[];\n\n  /**\n   * By default, Sentry will mark captured console messages as handled.\n   * Set this to `false` if you want to mark them as unhandled instead.\n   *\n   * @default true\n   */\n  handled?: boolean;\n}\n\nconst INTEGRATION_NAME = 'CaptureConsole';\n\nconst _captureConsoleIntegration = ((options: CaptureConsoleOptions = {}) => {\n  const levels = options.levels || CONSOLE_LEVELS;\n  const handled = options.handled ?? true;\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      if (!('console' in GLOBAL_OBJ)) {\n        return;\n      }\n\n      addConsoleInstrumentationHandler(({ args, level }) => {\n        if (getClient() !== client || !levels.includes(level)) {\n          return;\n        }\n\n        consoleHandler(args, level, handled);\n      });\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Send Console API calls as Sentry Events.\n */\nexport const captureConsoleIntegration = defineIntegration(_captureConsoleIntegration);\n\nfunction consoleHandler(args: unknown[], level: string, handled: boolean): void {\n  const captureContext: CaptureContext = {\n    level: severityLevelFromString(level),\n    extra: {\n      arguments: args,\n    },\n  };\n\n  withScope(scope => {\n    scope.addEventProcessor(event => {\n      event.logger = 'console';\n\n      addExceptionMechanism(event, {\n        handled,\n        type: 'console',\n      });\n\n      return event;\n    });\n\n    if (level === 'assert') {\n      if (!args[0]) {\n        const message = `Assertion failed: ${safeJoin(args.slice(1), ' ') || 'console.assert'}`;\n        scope.setExtra('arguments', args.slice(1));\n        captureMessage(message, captureContext);\n      }\n      return;\n    }\n\n    const error = args.find(arg => arg instanceof Error);\n    if (error) {\n      captureException(error, captureContext);\n      return;\n    }\n\n    const message = safeJoin(args, ' ');\n    captureMessage(message, captureContext);\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAwBA,MAAM,gBAAA,GAAmB,gBAAgB;;AAEzC,MAAM,0BAAA,IAA8B,CAAC,OAAO,GAA0B,EAAE,KAAK;AAC7E,EAAE,MAAM,MAAO,GAAE,OAAO,CAAC,MAAA,IAAU,cAAc;AACjD,EAAE,MAAM,OAAQ,GAAE,OAAO,CAAC,OAAA,IAAW,IAAI;;AAEzC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,IAAI,EAAE,aAAa,UAAU,CAAC,EAAE;AACtC,QAAQ;AACR;;AAEA,MAAM,gCAAgC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAA,EAAO,KAAK;AAC5D,QAAQ,IAAI,SAAS,OAAO,MAAA,IAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC/D,UAAU;AACV;;AAEA,QAAQ,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;AAC5C,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;MACa,yBAA0B,GAAE,iBAAiB,CAAC,0BAA0B;;AAErF,SAAS,cAAc,CAAC,IAAI,EAAa,KAAK,EAAU,OAAO,EAAiB;AAChF,EAAE,MAAM,cAAc,GAAmB;AACzC,IAAI,KAAK,EAAE,uBAAuB,CAAC,KAAK,CAAC;AACzC,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,IAAI;AACrB,KAAK;AACL,GAAG;;AAEH,EAAE,SAAS,CAAC,KAAA,IAAS;AACrB,IAAI,KAAK,CAAC,iBAAiB,CAAC,SAAS;AACrC,MAAM,KAAK,CAAC,MAAO,GAAE,SAAS;;AAE9B,MAAM,qBAAqB,CAAC,KAAK,EAAE;AACnC,QAAQ,OAAO;AACf,QAAQ,IAAI,EAAE,SAAS;AACvB,OAAO,CAAC;;AAER,MAAM,OAAO,KAAK;AAClB,KAAK,CAAC;;AAEN,IAAI,IAAI,KAAM,KAAI,QAAQ,EAAE;AAC5B,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACpB,QAAQ,MAAM,UAAU,CAAC,kBAAkB,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,gBAAgB,CAAC,CAAA;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,QAAA,cAAA,CAAA,OAAA,EAAA,cAAA,CAAA;AACA;AACA,MAAA;AACA;;AAEA,IAAA,MAAA,KAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,IAAA,GAAA,YAAA,KAAA,CAAA;AACA,IAAA,IAAA,KAAA,EAAA;AACA,MAAA,gBAAA,CAAA,KAAA,EAAA,cAAA,CAAA;AACA,MAAA;AACA;;AAEA,IAAA,MAAA,OAAA,GAAA,QAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,IAAA,cAAA,CAAA,OAAA,EAAA,cAAA,CAAA;AACA,GAAA,CAAA;AACA;;;;"}