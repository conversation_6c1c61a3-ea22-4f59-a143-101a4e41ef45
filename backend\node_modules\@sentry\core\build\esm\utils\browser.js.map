{"version": 3, "file": "browser.js", "sources": ["../../../src/utils/browser.ts"], "sourcesContent": ["import { isString } from './is';\nimport { GLOBAL_OBJ } from './worldwide';\n\nconst WINDOW = GLOBAL_OBJ as unknown as Window;\n\nconst DEFAULT_MAX_STRING_LENGTH = 80;\n\ntype SimpleNode = {\n  parentNode: SimpleNode;\n} | null;\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(\n  elem: unknown,\n  options: string[] | { keyAttrs?: string[]; maxStringLength?: number } = {},\n): string {\n  if (!elem) {\n    return '<unknown>';\n  }\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n    const keyAttrs = Array.isArray(options) ? options : options.keyAttrs;\n    const maxStringLength = (!Array.isArray(options) && options.maxStringLength) || DEFAULT_MAX_STRING_LENGTH;\n\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem, keyAttrs);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds maxStringLength\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= maxStringLength)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown, keyAttrs?: string[]): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n\n  if (!elem?.tagName) {\n    return '';\n  }\n\n  // @ts-expect-error WINDOW has HTMLElement\n  if (WINDOW.HTMLElement) {\n    // If using the component name annotation plugin, this value may be available on the DOM node\n    if (elem instanceof HTMLElement && elem.dataset) {\n      if (elem.dataset['sentryComponent']) {\n        return elem.dataset['sentryComponent'];\n      }\n      if (elem.dataset['sentryElement']) {\n        return elem.dataset['sentryElement'];\n      }\n    }\n  }\n\n  out.push(elem.tagName.toLowerCase());\n\n  // Pairs of attribute keys defined in `serializeAttribute` and their values on element.\n  const keyAttrPairs = keyAttrs?.length\n    ? keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)])\n    : null;\n\n  if (keyAttrPairs?.length) {\n    keyAttrPairs.forEach(keyAttrPair => {\n      out.push(`[${keyAttrPair[0]}=\"${keyAttrPair[1]}\"]`);\n    });\n  } else {\n    if (elem.id) {\n      out.push(`#${elem.id}`);\n    }\n\n    const className = elem.className;\n    if (className && isString(className)) {\n      const classes = className.split(/\\s+/);\n      for (const c of classes) {\n        out.push(`.${c}`);\n      }\n    }\n  }\n  const allowedAttrs = ['aria-label', 'type', 'name', 'title', 'alt'];\n  for (const k of allowedAttrs) {\n    const attr = elem.getAttribute(k);\n    if (attr) {\n      out.push(`[${k}=\"${attr}\"]`);\n    }\n  }\n\n  return out.join('');\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return WINDOW.document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n/**\n * Given a DOM element, traverses up the tree until it finds the first ancestor node\n * that has the `data-sentry-component` or `data-sentry-element` attribute with `data-sentry-component` taking\n * precedence. This attribute is added at build-time by projects that have the component name annotation plugin installed.\n *\n * @returns a string representation of the component for the provided DOM element, or `null` if not found\n */\nexport function getComponentName(elem: unknown): string | null {\n  // @ts-expect-error WINDOW has HTMLElement\n  if (!WINDOW.HTMLElement) {\n    return null;\n  }\n\n  let currentElem = elem as SimpleNode;\n  const MAX_TRAVERSE_HEIGHT = 5;\n  for (let i = 0; i < MAX_TRAVERSE_HEIGHT; i++) {\n    if (!currentElem) {\n      return null;\n    }\n\n    if (currentElem instanceof HTMLElement) {\n      if (currentElem.dataset['sentryComponent']) {\n        return currentElem.dataset['sentryComponent'];\n      }\n      if (currentElem.dataset['sentryElement']) {\n        return currentElem.dataset['sentryElement'];\n      }\n    }\n\n    currentElem = currentElem.parentNode;\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAGA,MAAM,MAAA,GAAS,UAAW;;AAE1B,MAAM,yBAAA,GAA4B,EAAE;;AAMpC;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB;AAChC,EAAE,IAAI;AACN,EAAE,OAAO,GAAiE,EAAE;AAC5E,EAAU;AACV,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,WAAW;AACtB;;AAEA;AACA;AACA;AACA;AACA,EAAE,IAAI;AACN,IAAI,IAAI,WAAY,GAAE,IAAK;AAC3B,IAAI,MAAM,mBAAoB,GAAE,CAAC;AACjC,IAAI,MAAM,GAAA,GAAM,EAAE;AAClB,IAAI,IAAI,MAAO,GAAE,CAAC;AAClB,IAAI,IAAI,GAAI,GAAE,CAAC;AACf,IAAI,MAAM,SAAU,GAAE,KAAK;AAC3B,IAAI,MAAM,SAAA,GAAY,SAAS,CAAC,MAAM;AACtC,IAAI,IAAI,OAAO;AACf,IAAI,MAAM,QAAA,GAAW,KAAK,CAAC,OAAO,CAAC,OAAO,CAAA,GAAI,OAAA,GAAU,OAAO,CAAC,QAAQ;AACxE,IAAI,MAAM,eAAgB,GAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,eAAe,KAAK,yBAAyB;;AAE7G,IAAI,OAAO,WAAY,IAAG,MAAM,EAAG,GAAE,mBAAmB,EAAE;AAC1D,MAAM,UAAU,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC;AAC3D;AACA;AACA;AACA;AACA,MAAM,IAAI,OAAA,KAAY,MAAA,KAAW,MAAA,GAAS,CAAA,IAAK,GAAA,GAAM,GAAG,CAAC,MAAO,GAAE,SAAU,GAAE,OAAO,CAAC,MAAO,IAAG,eAAe,CAAC,EAAE;AAClH,QAAQ;AACR;;AAEA,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;;AAEvB,MAAM,GAAI,IAAG,OAAO,CAAC,MAAM;AAC3B,MAAM,WAAY,GAAE,WAAW,CAAC,UAAU;AAC1C;;AAEA,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;AACxC,GAAI,CAAA,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,WAAW;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,EAAE,EAAW,QAAQ,EAAqB;AACxE,EAAE,MAAM,IAAK,GAAE;;AAKb;;AAEF,EAAE,MAAM,GAAA,GAAM,EAAE;;AAEhB,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACtB,IAAI,OAAO,EAAE;AACb;;AAEA;AACA,EAAE,IAAI,MAAM,CAAC,WAAW,EAAE;AAC1B;AACA,IAAI,IAAI,IAAK,YAAW,eAAe,IAAI,CAAC,OAAO,EAAE;AACrD,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;AAC3C,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;AAC9C;AACA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;AACzC,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;AAC5C;AACA;AACA;;AAEA,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;;AAEtC;AACA,EAAE,MAAM,YAAA,GAAe,QAAQ,EAAE;AACjC,MAAM,QAAQ,CAAC,MAAM,CAAC,OAAA,IAAW,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAA,IAAW,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACjH,MAAM,IAAI;;AAEV,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE;AAC5B,IAAI,YAAY,CAAC,OAAO,CAAC,eAAe;AACxC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACzD,KAAK,CAAC;AACN,SAAS;AACT,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;AACjB,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA,CAAA;AACA;;AAEA,IAAA,MAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,IAAA,IAAA,SAAA,IAAA,QAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAA,MAAA,OAAA,GAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AACA,MAAA,KAAA,MAAA,CAAA,IAAA,OAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA;AACA,EAAA,MAAA,YAAA,GAAA,CAAA,YAAA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,KAAA,CAAA;AACA,EAAA,KAAA,MAAA,CAAA,IAAA,YAAA,EAAA;AACA,IAAA,MAAA,IAAA,GAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,IAAA,EAAA;AACA,MAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,GAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,SAAA,eAAA,GAAA;AACA,EAAA,IAAA;AACA,IAAA,OAAA,MAAA,CAAA,QAAA,CAAA,QAAA,CAAA,IAAA;AACA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,gBAAA,CAAA,IAAA,EAAA;AACA;AACA,EAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA;AACA,IAAA,OAAA,IAAA;AACA;;AAEA,EAAA,IAAA,WAAA,GAAA,IAAA;AACA,EAAA,MAAA,mBAAA,GAAA,CAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,mBAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,IAAA,CAAA,WAAA,EAAA;AACA,MAAA,OAAA,IAAA;AACA;;AAEA,IAAA,IAAA,WAAA,YAAA,WAAA,EAAA;AACA,MAAA,IAAA,WAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,EAAA;AACA,QAAA,OAAA,WAAA,CAAA,OAAA,CAAA,iBAAA,CAAA;AACA;AACA,MAAA,IAAA,WAAA,CAAA,OAAA,CAAA,eAAA,CAAA,EAAA;AACA,QAAA,OAAA,WAAA,CAAA,OAAA,CAAA,eAAA,CAAA;AACA;AACA;;AAEA,IAAA,WAAA,GAAA,WAAA,CAAA,UAAA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;;;"}