import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

part 'transaction_model.g.dart';

@HiveType(typeId: 0)
class Transaction extends HiveObject {
  @HiveField(0)
  final String? id;
  @HiveField(1)
  final String userId;
  @HiveField(2)
  final double amount;
  @HiveField(3)
  final String type; // 'income' or 'expense'
  @HiveField(4)
  final String categoryId;
  @HiveField(5)
  final String? categoryName; // For displaying purposes
  @HiveField(6)
  final int? categoryColorValue; // Storing color as int

  Color? get categoryColor =>
      categoryColorValue != null ? Color(categoryColorValue!) : null;

  @HiveField(7)
  final String? description;
  @HiveField(8)
  final DateTime date;
  @HiveField(9)
  final bool isRecurring;
  @HiveField(10)
  final String? recurringFrequency; // 'daily', 'weekly', 'monthly', 'yearly'
  @HiveField(11)
  final DateTime? recurringEndDate;

  Transaction({
    this.id,
    required this.userId,
    required this.amount,
    required this.type,
    required this.categoryId,
    this.categoryName,
    this.categoryColorValue,
    this.description,
    required this.date,
    this.isRecurring = false,
    this.recurringFrequency,
    this.recurringEndDate,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    // Handle category data which might be an object or just an ID
    String categoryId;
    String? categoryName;
    int? categoryColorValue;

    if (json['category'] is Map) {
      categoryId = json['category']['_id'];
      categoryName = json['category']['name'];
      // Convert color hex string to Color value
      if (json['category']['color'] != null) {
        try {
          final colorHex = json['category']['color'].toString().replaceAll(
            '#',
            '',
          );
          // ignore: deprecated_member_use
          categoryColorValue = Color(int.parse('FF$colorHex', radix: 16)).value;
        } catch (e) {
          // ignore: deprecated_member_use
          categoryColorValue = Colors.grey.value; // Default color
        }
      }
    } else {
      categoryId = json['category'];
    }

    return Transaction(
      id: json['_id'],
      userId: json['user'],
      amount: json['amount']?.toDouble() ?? 0.0,
      type: json['type'] ?? 'expense',
      categoryId: categoryId,
      categoryName: categoryName,
      categoryColorValue: categoryColorValue,
      description: json['description'],
      date: json['date'] != null
          ? DateTime.parse(json['date'])
          : DateTime.now(),
      isRecurring: json['isRecurring'] ?? false,
      recurringFrequency: json['recurringFrequency'],
      recurringEndDate: json['recurringEndDate'] != null
          ? DateTime.parse(json['recurringEndDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'user': userId,
      'amount': amount,
      'type': type,
      'category': categoryId,
      'description': description,
      'date': date.toIso8601String(),
      'isRecurring': isRecurring,
      'recurringFrequency': recurringFrequency,
      'recurringEndDate': recurringEndDate?.toIso8601String(),
    };
  }

  Transaction copyWith({
    String? id,
    String? userId,
    double? amount,
    String? type,
    String? categoryId,
    String? categoryName,
    int? categoryColorValue,
    String? description,
    DateTime? date,
    bool? isRecurring,
    String? recurringFrequency,
    DateTime? recurringEndDate,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      categoryColorValue: categoryColorValue ?? this.categoryColorValue,
      description: description ?? this.description,
      date: date ?? this.date,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringFrequency: recurringFrequency ?? this.recurringFrequency,
      recurringEndDate: recurringEndDate ?? this.recurringEndDate,
    );
  }
}
