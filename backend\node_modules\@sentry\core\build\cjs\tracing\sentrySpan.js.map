{"version": 3, "file": "sentrySpan.js", "sources": ["../../../src/tracing/sentrySpan.ts"], "sourcesContent": ["import { getClient, getCurrentScope } from '../currentScopes';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { createSpanEnvelope } from '../envelope';\nimport {\n  SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME,\n  SEMANTIC_ATTRIBUTE_PROFILE_ID,\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { SpanEnvelope } from '../types-hoist/envelope';\nimport type { TransactionEvent } from '../types-hoist/event';\nimport type { SpanLink } from '../types-hoist/link';\nimport type {\n  SentrySpanArguments,\n  Span,\n  SpanAttributes,\n  SpanAttributeValue,\n  SpanContextData,\n  SpanJSO<PERSON>,\n  Span<PERSON>rigin,\n  SpanTimeInput,\n} from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\nimport type { TimedEvent } from '../types-hoist/timedEvent';\nimport type { TransactionSource } from '../types-hoist/transaction';\nimport { logger } from '../utils/logger';\nimport { generateSpanId, generateTraceId } from '../utils/propagationContext';\nimport {\n  convertSpanLinksForEnvelope,\n  getRootSpan,\n  getSpanDescendants,\n  getStatusMessage,\n  spanTimeInputToSeconds,\n  spanToJSON,\n  spanToTransactionTraceContext,\n  TRACE_FLAG_NONE,\n  TRACE_FLAG_SAMPLED,\n} from '../utils/spanUtils';\nimport { timestampInSeconds } from '../utils/time';\nimport { getDynamicSamplingContextFromSpan } from './dynamicSamplingContext';\nimport { logSpanEnd } from './logSpans';\nimport { timedEventsToMeasurements } from './measurement';\nimport { getCapturedScopesOnSpan } from './utils';\n\nconst MAX_SPAN_COUNT = 1000;\n\n/**\n * Span contains all data about a span\n */\nexport class SentrySpan implements Span {\n  protected _traceId: string;\n  protected _spanId: string;\n  protected _parentSpanId?: string | undefined;\n  protected _sampled: boolean | undefined;\n  protected _name?: string | undefined;\n  protected _attributes: SpanAttributes;\n  protected _links?: SpanLink[];\n  /** Epoch timestamp in seconds when the span started. */\n  protected _startTime: number;\n  /** Epoch timestamp in seconds when the span ended. */\n  protected _endTime?: number | undefined;\n  /** Internal keeper of the status */\n  protected _status?: SpanStatus;\n  /** The timed events added to this span. */\n  protected _events: TimedEvent[];\n\n  /** if true, treat span as a standalone span (not part of a transaction) */\n  private _isStandaloneSpan?: boolean;\n\n  /**\n   * You should never call the constructor manually, always use `Sentry.startSpan()`\n   * or other span methods.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(spanContext: SentrySpanArguments = {}) {\n    this._traceId = spanContext.traceId || generateTraceId();\n    this._spanId = spanContext.spanId || generateSpanId();\n    this._startTime = spanContext.startTimestamp || timestampInSeconds();\n    this._links = spanContext.links;\n\n    this._attributes = {};\n    this.setAttributes({\n      [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'manual',\n      [SEMANTIC_ATTRIBUTE_SENTRY_OP]: spanContext.op,\n      ...spanContext.attributes,\n    });\n\n    this._name = spanContext.name;\n\n    if (spanContext.parentSpanId) {\n      this._parentSpanId = spanContext.parentSpanId;\n    }\n    // We want to include booleans as well here\n    if ('sampled' in spanContext) {\n      this._sampled = spanContext.sampled;\n    }\n    if (spanContext.endTimestamp) {\n      this._endTime = spanContext.endTimestamp;\n    }\n\n    this._events = [];\n\n    this._isStandaloneSpan = spanContext.isStandalone;\n\n    // If the span is already ended, ensure we finalize the span immediately\n    if (this._endTime) {\n      this._onSpanEnded();\n    }\n  }\n\n  /** @inheritDoc */\n  public addLink(link: SpanLink): this {\n    if (this._links) {\n      this._links.push(link);\n    } else {\n      this._links = [link];\n    }\n    return this;\n  }\n\n  /** @inheritDoc */\n  public addLinks(links: SpanLink[]): this {\n    if (this._links) {\n      this._links.push(...links);\n    } else {\n      this._links = links;\n    }\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but it is needed for being compliant with the OTEL Span interface.\n   *\n   * @hidden\n   * @internal\n   */\n  public recordException(_exception: unknown, _time?: number | undefined): void {\n    // noop\n  }\n\n  /** @inheritdoc */\n  public spanContext(): SpanContextData {\n    const { _spanId: spanId, _traceId: traceId, _sampled: sampled } = this;\n    return {\n      spanId,\n      traceId,\n      traceFlags: sampled ? TRACE_FLAG_SAMPLED : TRACE_FLAG_NONE,\n    };\n  }\n\n  /** @inheritdoc */\n  public setAttribute(key: string, value: SpanAttributeValue | undefined): this {\n    if (value === undefined) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._attributes[key];\n    } else {\n      this._attributes[key] = value;\n    }\n\n    return this;\n  }\n\n  /** @inheritdoc */\n  public setAttributes(attributes: SpanAttributes): this {\n    Object.keys(attributes).forEach(key => this.setAttribute(key, attributes[key]));\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but we need it for browser tracing where we want to adjust the start time afterwards.\n   * USE THIS WITH CAUTION!\n   *\n   * @hidden\n   * @internal\n   */\n  public updateStartTime(timeInput: SpanTimeInput): void {\n    this._startTime = spanTimeInputToSeconds(timeInput);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setStatus(value: SpanStatus): this {\n    this._status = value;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public updateName(name: string): this {\n    this._name = name;\n    this.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, 'custom');\n    return this;\n  }\n\n  /** @inheritdoc */\n  public end(endTimestamp?: SpanTimeInput): void {\n    // If already ended, skip\n    if (this._endTime) {\n      return;\n    }\n\n    this._endTime = spanTimeInputToSeconds(endTimestamp);\n    logSpanEnd(this);\n\n    this._onSpanEnded();\n  }\n\n  /**\n   * Get JSON representation of this span.\n   *\n   * @hidden\n   * @internal This method is purely for internal purposes and should not be used outside\n   * of SDK code. If you need to get a JSON representation of a span,\n   * use `spanToJSON(span)` instead.\n   */\n  public getSpanJSON(): SpanJSON {\n    return {\n      data: this._attributes,\n      description: this._name,\n      op: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP],\n      parent_span_id: this._parentSpanId,\n      span_id: this._spanId,\n      start_timestamp: this._startTime,\n      status: getStatusMessage(this._status),\n      timestamp: this._endTime,\n      trace_id: this._traceId,\n      origin: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined,\n      profile_id: this._attributes[SEMANTIC_ATTRIBUTE_PROFILE_ID] as string | undefined,\n      exclusive_time: this._attributes[SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME] as number | undefined,\n      measurements: timedEventsToMeasurements(this._events),\n      is_segment: (this._isStandaloneSpan && getRootSpan(this) === this) || undefined,\n      segment_id: this._isStandaloneSpan ? getRootSpan(this).spanContext().spanId : undefined,\n      links: convertSpanLinksForEnvelope(this._links),\n    };\n  }\n\n  /** @inheritdoc */\n  public isRecording(): boolean {\n    return !this._endTime && !!this._sampled;\n  }\n\n  /**\n   * @inheritdoc\n   */\n  public addEvent(\n    name: string,\n    attributesOrStartTime?: SpanAttributes | SpanTimeInput,\n    startTime?: SpanTimeInput,\n  ): this {\n    DEBUG_BUILD && logger.log('[Tracing] Adding an event to span:', name);\n\n    const time = isSpanTimeInput(attributesOrStartTime) ? attributesOrStartTime : startTime || timestampInSeconds();\n    const attributes = isSpanTimeInput(attributesOrStartTime) ? {} : attributesOrStartTime || {};\n\n    const event: TimedEvent = {\n      name,\n      time: spanTimeInputToSeconds(time),\n      attributes,\n    };\n\n    this._events.push(event);\n\n    return this;\n  }\n\n  /**\n   * This method should generally not be used,\n   * but for now we need a way to publicly check if the `_isStandaloneSpan` flag is set.\n   * USE THIS WITH CAUTION!\n   * @internal\n   * @hidden\n   * @experimental\n   */\n  public isStandaloneSpan(): boolean {\n    return !!this._isStandaloneSpan;\n  }\n\n  /** Emit `spanEnd` when the span is ended. */\n  private _onSpanEnded(): void {\n    const client = getClient();\n    if (client) {\n      client.emit('spanEnd', this);\n    }\n\n    // A segment span is basically the root span of a local span tree.\n    // So for now, this is either what we previously refer to as the root span,\n    // or a standalone span.\n    const isSegmentSpan = this._isStandaloneSpan || this === getRootSpan(this);\n\n    if (!isSegmentSpan) {\n      return;\n    }\n\n    // if this is a standalone span, we send it immediately\n    if (this._isStandaloneSpan) {\n      if (this._sampled) {\n        sendSpanEnvelope(createSpanEnvelope([this], client));\n      } else {\n        DEBUG_BUILD &&\n          logger.log('[Tracing] Discarding standalone span because its trace was not chosen to be sampled.');\n        if (client) {\n          client.recordDroppedEvent('sample_rate', 'span');\n        }\n      }\n      return;\n    }\n\n    const transactionEvent = this._convertSpanToTransaction();\n    if (transactionEvent) {\n      const scope = getCapturedScopesOnSpan(this).scope || getCurrentScope();\n      scope.captureEvent(transactionEvent);\n    }\n  }\n\n  /**\n   * Finish the transaction & prepare the event to send to Sentry.\n   */\n  private _convertSpanToTransaction(): TransactionEvent | undefined {\n    // We can only convert finished spans\n    if (!isFullFinishedSpan(spanToJSON(this))) {\n      return undefined;\n    }\n\n    if (!this._name) {\n      DEBUG_BUILD && logger.warn('Transaction has no name, falling back to `<unlabeled transaction>`.');\n      this._name = '<unlabeled transaction>';\n    }\n\n    const { scope: capturedSpanScope, isolationScope: capturedSpanIsolationScope } = getCapturedScopesOnSpan(this);\n\n    const normalizedRequest = capturedSpanScope?.getScopeData().sdkProcessingMetadata?.normalizedRequest;\n\n    if (this._sampled !== true) {\n      return undefined;\n    }\n\n    // The transaction span itself as well as any potential standalone spans should be filtered out\n    const finishedSpans = getSpanDescendants(this).filter(span => span !== this && !isStandaloneSpan(span));\n\n    const spans = finishedSpans.map(span => spanToJSON(span)).filter(isFullFinishedSpan);\n\n    const source = this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource | undefined;\n\n    // remove internal root span attributes we don't need to send.\n    /* eslint-disable @typescript-eslint/no-dynamic-delete */\n    delete this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n    spans.forEach(span => {\n      delete span.data[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n    });\n    // eslint-enabled-next-line @typescript-eslint/no-dynamic-delete\n\n    const transaction: TransactionEvent = {\n      contexts: {\n        trace: spanToTransactionTraceContext(this),\n      },\n      spans:\n        // spans.sort() mutates the array, but `spans` is already a copy so we can safely do this here\n        // we do not use spans anymore after this point\n        spans.length > MAX_SPAN_COUNT\n          ? spans.sort((a, b) => a.start_timestamp - b.start_timestamp).slice(0, MAX_SPAN_COUNT)\n          : spans,\n      start_timestamp: this._startTime,\n      timestamp: this._endTime,\n      transaction: this._name,\n      type: 'transaction',\n      sdkProcessingMetadata: {\n        capturedSpanScope,\n        capturedSpanIsolationScope,\n        dynamicSamplingContext: getDynamicSamplingContextFromSpan(this),\n      },\n      request: normalizedRequest,\n      ...(source && {\n        transaction_info: {\n          source,\n        },\n      }),\n    };\n\n    const measurements = timedEventsToMeasurements(this._events);\n    const hasMeasurements = measurements && Object.keys(measurements).length;\n\n    if (hasMeasurements) {\n      DEBUG_BUILD &&\n        logger.log(\n          '[Measurements] Adding measurements to transaction event',\n          JSON.stringify(measurements, undefined, 2),\n        );\n      transaction.measurements = measurements;\n    }\n\n    return transaction;\n  }\n}\n\nfunction isSpanTimeInput(value: undefined | SpanAttributes | SpanTimeInput): value is SpanTimeInput {\n  return (value && typeof value === 'number') || value instanceof Date || Array.isArray(value);\n}\n\n// We want to filter out any incomplete SpanJSON objects\nfunction isFullFinishedSpan(input: Partial<SpanJSON>): input is SpanJSON {\n  return !!input.start_timestamp && !!input.timestamp && !!input.span_id && !!input.trace_id;\n}\n\n/** `SentrySpan`s can be sent as a standalone span rather than belonging to a transaction */\nfunction isStandaloneSpan(span: Span): boolean {\n  return span instanceof SentrySpan && span.isStandaloneSpan();\n}\n\n/**\n * Sends a `SpanEnvelope`.\n *\n * Note: If the envelope's spans are dropped, e.g. via `beforeSendSpan`,\n * the envelope will not be sent either.\n */\nfunction sendSpanEnvelope(envelope: SpanEnvelope): void {\n  const client = getClient();\n  if (!client) {\n    return;\n  }\n\n  const spanItems = envelope[1];\n  if (!spanItems || spanItems.length === 0) {\n    client.recordDroppedEvent('before_send', 'span');\n    return;\n  }\n\n  // sendEnvelope should not throw\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  client.sendEnvelope(envelope);\n}\n"], "names": ["generateTraceId", "generateSpanId", "timestampInSeconds", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "TRACE_FLAG_SAMPLED", "TRACE_FLAG_NONE", "spanTimeInputToSeconds", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "logSpanEnd", "getStatusMessage", "SEMANTIC_ATTRIBUTE_PROFILE_ID", "SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME", "timedEventsToMeasurements", "getRootSpan", "convertSpanLinksForEnvelope", "DEBUG_BUILD", "logger", "time", "getClient", "createSpanEnvelope", "getCapturedScopesOnSpan", "getCurrentScope", "spanToJSON", "getSpanDescendants", "SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME", "spanToTransactionTraceContext", "getDynamicSamplingContextFromSpan"], "mappings": ";;;;;;;;;;;;;;;AA8CA,MAAM,cAAA,GAAiB,IAAI;;AAE3B;AACA;AACA;AACO,MAAM,YAA2B;;AAQxC;;AAEA;;AAEA;;AAEA;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,WAAW,GAAwB,EAAE,EAAE;AAC5D,IAAI,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAQ,IAAGA,kCAAe,EAAE;AAC5D,IAAI,IAAI,CAAC,OAAA,GAAU,WAAW,CAAC,MAAO,IAAGC,iCAAc,EAAE;AACzD,IAAI,IAAI,CAAC,UAAA,GAAa,WAAW,CAAC,cAAe,IAAGC,uBAAkB,EAAE;AACxE,IAAI,IAAI,CAAC,MAAA,GAAS,WAAW,CAAC,KAAK;;AAEnC,IAAI,IAAI,CAAC,WAAY,GAAE,EAAE;AACzB,IAAI,IAAI,CAAC,aAAa,CAAC;AACvB,MAAM,CAACC,mDAAgC,GAAG,QAAQ;AAClD,MAAM,CAACC,+CAA4B,GAAG,WAAW,CAAC,EAAE;AACpD,MAAM,GAAG,WAAW,CAAC,UAAU;AAC/B,KAAK,CAAC;;AAEN,IAAI,IAAI,CAAC,KAAA,GAAQ,WAAW,CAAC,IAAI;;AAEjC,IAAI,IAAI,WAAW,CAAC,YAAY,EAAE;AAClC,MAAM,IAAI,CAAC,aAAA,GAAgB,WAAW,CAAC,YAAY;AACnD;AACA;AACA,IAAI,IAAI,SAAU,IAAG,WAAW,EAAE;AAClC,MAAM,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAO;AACzC;AACA,IAAI,IAAI,WAAW,CAAC,YAAY,EAAE;AAClC,MAAM,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,YAAY;AAC9C;;AAEA,IAAI,IAAI,CAAC,OAAQ,GAAE,EAAE;;AAErB,IAAI,IAAI,CAAC,iBAAA,GAAoB,WAAW,CAAC,YAAY;;AAErD;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,IAAI,CAAC,YAAY,EAAE;AACzB;AACA;;AAEA;AACA,GAAS,OAAO,CAAC,IAAI,EAAkB;AACvC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5B,WAAW;AACX,MAAM,IAAI,CAAC,MAAA,GAAS,CAAC,IAAI,CAAC;AAC1B;AACA,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,QAAQ,CAAC,KAAK,EAAoB;AAC3C,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAChC,WAAW;AACX,MAAM,IAAI,CAAC,MAAO,GAAE,KAAK;AACzB;AACA,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,eAAe,CAAC,UAAU,EAAW,KAAK,EAA6B;AAChF;AACA;;AAEA;AACA,GAAS,WAAW,GAAoB;AACxC,IAAI,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAQ,EAAA,GAAI,IAAI;AAC1E,IAAI,OAAO;AACX,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,UAAU,EAAE,OAAA,GAAUC,4BAAA,GAAqBC,yBAAe;AAChE,KAAK;AACL;;AAEA;AACA,GAAS,YAAY,CAAC,GAAG,EAAU,KAAK,EAAwC;AAChF,IAAI,IAAI,KAAM,KAAI,SAAS,EAAE;AAC7B;AACA,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;AAClC,WAAW;AACX,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAA,GAAI,KAAK;AACnC;;AAEA,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,aAAa,CAAC,UAAU,EAAwB;AACzD,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,GAAI,IAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACnF,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,eAAe,CAAC,SAAS,EAAuB;AACzD,IAAI,IAAI,CAAC,UAAA,GAAaC,gCAAsB,CAAC,SAAS,CAAC;AACvD;;AAEA;AACA;AACA;AACA,GAAS,SAAS,CAAC,KAAK,EAAoB;AAC5C,IAAI,IAAI,CAAC,OAAQ,GAAE,KAAK;AACxB,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,UAAU,CAAC,IAAI,EAAgB;AACxC,IAAI,IAAI,CAAC,KAAM,GAAE,IAAI;AACrB,IAAI,IAAI,CAAC,YAAY,CAACC,mDAAgC,EAAE,QAAQ,CAAC;AACjE,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,GAAG,CAAC,YAAY,EAAwB;AACjD;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM;AACN;;AAEA,IAAI,IAAI,CAAC,QAAA,GAAWD,gCAAsB,CAAC,YAAY,CAAC;AACxD,IAAIE,mBAAU,CAAC,IAAI,CAAC;;AAEpB,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,GAAa;AACjC,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,IAAI,CAAC,WAAW;AAC5B,MAAM,WAAW,EAAE,IAAI,CAAC,KAAK;AAC7B,MAAM,EAAE,EAAE,IAAI,CAAC,WAAW,CAACL,+CAA4B,CAAC;AACxD,MAAM,cAAc,EAAE,IAAI,CAAC,aAAa;AACxC,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,eAAe,EAAE,IAAI,CAAC,UAAU;AACtC,MAAM,MAAM,EAAEM,0BAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5C,MAAM,SAAS,EAAE,IAAI,CAAC,QAAQ;AAC9B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,MAAM,EAAE,IAAI,CAAC,WAAW,CAACP,mDAAgC,CAAE;AACjE,MAAM,UAAU,EAAE,IAAI,CAAC,WAAW,CAACQ,gDAA6B,CAAE;AAClE,MAAM,cAAc,EAAE,IAAI,CAAC,WAAW,CAACC,oDAAiC,CAAE;AAC1E,MAAM,YAAY,EAAEC,qCAAyB,CAAC,IAAI,CAAC,OAAO,CAAC;AAC3D,MAAM,UAAU,EAAE,CAAC,IAAI,CAAC,iBAAkB,IAAGC,qBAAW,CAAC,IAAI,CAAE,KAAI,IAAI,KAAK,SAAS;AACrF,MAAM,UAAU,EAAE,IAAI,CAAC,iBAAA,GAAoBA,qBAAW,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,MAAA,GAAS,SAAS;AAC7F,MAAM,KAAK,EAAEC,qCAA2B,CAAC,IAAI,CAAC,MAAM,CAAC;AACrD,KAAK;AACL;;AAEA;AACA,GAAS,WAAW,GAAY;AAChC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAA,IAAY,CAAC,CAAC,IAAI,CAAC,QAAQ;AAC5C;;AAEA;AACA;AACA;AACA,GAAS,QAAQ;AACjB,IAAI,IAAI;AACR,IAAI,qBAAqB;AACzB,IAAI,SAAS;AACb,IAAU;AACV,IAAIC,sBAAA,IAAeC,aAAM,CAAC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC;;AAEzE,IAAI,MAAMC,MAAA,GAAO,eAAe,CAAC,qBAAqB,CAAA,GAAI,qBAAA,GAAwB,SAAA,IAAahB,uBAAkB,EAAE;AACnH,IAAI,MAAM,UAAA,GAAa,eAAe,CAAC,qBAAqB,CAAA,GAAI,EAAG,GAAE,qBAAsB,IAAG,EAAE;;AAEhG,IAAI,MAAM,KAAK,GAAe;AAC9B,MAAM,IAAI;AACV,MAAM,IAAI,EAAEK,gCAAsB,CAACW,MAAI,CAAC;AACxC,MAAM,UAAU;AAChB,KAAK;;AAEL,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE5B,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,GAAY;AACrC,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB;AACnC;;AAEA;AACA,GAAU,YAAY,GAAS;AAC/B,IAAI,MAAM,MAAA,GAASC,uBAAS,EAAE;AAC9B,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;AAClC;;AAEA;AACA;AACA;AACA,IAAI,MAAM,aAAA,GAAgB,IAAI,CAAC,iBAAA,IAAqB,IAAA,KAASL,qBAAW,CAAC,IAAI,CAAC;;AAE9E,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM;AACN;;AAEA;AACA,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAChC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AACzB,QAAQ,gBAAgB,CAACM,2BAAkB,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;AAC5D,aAAa;AACb,QAAQJ,sBAAY;AACpB,UAAUC,aAAM,CAAC,GAAG,CAAC,sFAAsF,CAAC;AAC5G,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,MAAM,CAAC,kBAAkB,CAAC,aAAa,EAAE,MAAM,CAAC;AAC1D;AACA;AACA,MAAM;AACN;;AAEA,IAAI,MAAM,gBAAiB,GAAE,IAAI,CAAC,yBAAyB,EAAE;AAC7D,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,MAAM,KAAA,GAAQI,6BAAuB,CAAC,IAAI,CAAC,CAAC,KAAM,IAAGC,6BAAe,EAAE;AAC5E,MAAM,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC;AAC1C;AACA;;AAEA;AACA;AACA;AACA,GAAU,yBAAyB,GAAiC;AACpE;AACA,IAAI,IAAI,CAAC,kBAAkB,CAACC,oBAAU,CAAC,IAAI,CAAC,CAAC,EAAE;AAC/C,MAAM,OAAO,SAAS;AACtB;;AAEA,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAMP,0BAAeC,aAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC;AACvG,MAAM,IAAI,CAAC,KAAM,GAAE,yBAAyB;AAC5C;;AAEA,IAAI,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,cAAc,EAAE,0BAAA,EAA6B,GAAEI,6BAAuB,CAAC,IAAI,CAAC;;AAElH,IAAI,MAAM,iBAAkB,GAAE,iBAAiB,EAAE,YAAY,EAAE,CAAC,qBAAqB,EAAE,iBAAiB;;AAExG,IAAI,IAAI,IAAI,CAAC,QAAS,KAAI,IAAI,EAAE;AAChC,MAAM,OAAO,SAAS;AACtB;;AAEA;AACA,IAAI,MAAM,gBAAgBG,4BAAkB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,IAAA,KAAS,IAAK,IAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;;AAE3G,IAAI,MAAM,KAAM,GAAE,aAAa,CAAC,GAAG,CAAC,IAAK,IAAGD,oBAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC;;AAExF,IAAI,MAAM,SAAS,IAAI,CAAC,WAAW,CAACf,mDAAgC,CAAE;;AAEtE;AACA;AACA,IAAI,OAAO,IAAI,CAAC,WAAW,CAACiB,6DAA0C,CAAC;AACvE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ;AAC1B,MAAM,OAAO,IAAI,CAAC,IAAI,CAACA,6DAA0C,CAAC;AAClE,KAAK,CAAC;AACN;;AAEA,IAAI,MAAM,WAAW,GAAqB;AAC1C,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAEC,uCAA6B,CAAC,IAAI,CAAC;AAClD,OAAO;AACP,MAAM,KAAK;AACX;AACA;AACA,QAAQ,KAAK,CAAC,MAAA,GAAS;AACvB,YAAY,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,eAAgB,GAAE,CAAC,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc;AAC/F,YAAY,KAAK;AACjB,MAAM,eAAe,EAAE,IAAI,CAAC,UAAU;AACtC,MAAM,SAAS,EAAE,IAAI,CAAC,QAAQ;AAC9B,MAAM,WAAW,EAAE,IAAI,CAAC,KAAK;AAC7B,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,qBAAqB,EAAE;AAC7B,QAAQ,iBAAiB;AACzB,QAAQ,0BAA0B;AAClC,QAAQ,sBAAsB,EAAEC,wDAAiC,CAAC,IAAI,CAAC;AACvE,OAAO;AACP,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,IAAI,MAAA,IAAU;AACpB,QAAQ,gBAAgB,EAAE;AAC1B,UAAU,MAAM;AAChB,SAAS;AACT,OAAO,CAAC;AACR,KAAK;;AAEL,IAAI,MAAM,eAAed,qCAAyB,CAAC,IAAI,CAAC,OAAO,CAAC;AAChE,IAAI,MAAM,eAAA,GAAkB,YAAA,IAAgB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM;;AAE5E,IAAI,IAAI,eAAe,EAAE;AACzB,MAAMG,sBAAY;AAClB,QAAQC,aAAM,CAAC,GAAG;AAClB,UAAU,yDAAyD;AACnE,UAAU,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,SAAS,EAAE,CAAC,CAAC;AACpD,SAAS;AACT,MAAM,WAAW,CAAC,YAAa,GAAE,YAAY;AAC7C;;AAEA,IAAI,OAAO,WAAW;AACtB;AACA;;AAEA,SAAS,eAAe,CAAC,KAAK,EAAsE;AACpG,EAAE,OAAO,CAAC,KAAA,IAAS,OAAO,KAAA,KAAU,QAAQ,KAAK,KAAM,YAAW,QAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9F;;AAEA;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAwC;AACzE,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,eAAgB,IAAG,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,OAAA,IAAW,CAAC,CAAC,KAAK,CAAC,QAAQ;AAC5F;;AAEA;AACA,SAAS,gBAAgB,CAAC,IAAI,EAAiB;AAC/C,EAAE,OAAO,gBAAgB,UAAA,IAAc,IAAI,CAAC,gBAAgB,EAAE;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAsB;AACxD,EAAE,MAAM,MAAA,GAASE,uBAAS,EAAE;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI;AACJ;;AAEA,EAAE,MAAM,SAAU,GAAE,QAAQ,CAAC,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,SAAU,IAAG,SAAS,CAAC,MAAA,KAAW,CAAC,EAAE;AAC5C,IAAI,MAAM,CAAC,kBAAkB,CAAC,aAAa,EAAE,MAAM,CAAC;AACpD,IAAI;AACJ;;AAEA;AACA;AACA,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC/B;;;;"}