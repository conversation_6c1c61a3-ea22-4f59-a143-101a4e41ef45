{"logs": [{"outputFile": "com.example.budget_tracker.app-mergeDebugResources-49:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "58,61,62,63", "startColumns": "4,4,4,4", "startOffsets": "6299,6599,6700,6811", "endColumns": "114,100,110,100", "endOffsets": "6409,6695,6806,6907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,261,386,525,668,802,937,1081,1177,1320,1468", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "163,256,381,520,663,797,932,1076,1172,1315,1463,1584"}, "to": {"startLines": "56,59,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6117,6414,6912,7037,7176,7319,7453,7588,7732,7828,7971,8119", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "6225,6502,7032,7171,7314,7448,7583,7727,7823,7966,8114,8235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,8472", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,8549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\006877986797b3b6be5cf579d190afc8\\transformed\\jetified-credentials-1.5.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,122", "endOffsets": "165,288"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2801,2916", "endColumns": "114,122", "endOffsets": "2911,3034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "31,32,33,34,35,36,37,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3039,3138,3240,3338,3435,3543,3654,8554", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3133,3235,3333,3430,3538,3649,3771,8650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,266,350,498,667,751", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "169,261,345,493,662,746,825"}, "to": {"startLines": "57,60,74,75,78,79,80", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6230,6507,8240,8324,8655,8824,8908", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "6294,6594,8319,8467,8819,8903,8982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4837", "endColumns": "128", "endOffsets": "4961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e1f6d2e0b1aa38467964f5b59b4f29f9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3776,3883,4056,4193,4300,4461,4595,4721,4966,5136,5244,5419,5557,5719,5903,5968,6035", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "3878,4051,4188,4295,4456,4590,4716,4832,5131,5239,5414,5552,5714,5898,5963,6030,6112"}}]}]}