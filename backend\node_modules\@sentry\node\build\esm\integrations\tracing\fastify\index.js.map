{"version": 3, "file": "index.js", "sources": ["../../../../../src/integrations/tracing/fastify/index.ts"], "sourcesContent": ["import * as diagnosticsChannel from 'node:diagnostics_channel';\nimport type { Instrumentation, InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport type { IntegrationFn, Span } from '@sentry/core';\nimport {\n  captureException,\n  defineIntegration,\n  getClient,\n  getIsolationScope,\n  logger,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  spanToJSON,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from '../../../debug-build';\nimport { generateInstrumentOnce } from '../../../otel/instrument';\nimport { FastifyOtelInstrumentation } from './fastify-otel/index';\nimport type { FastifyInstance, FastifyReply, FastifyRequest } from './types';\nimport { FastifyInstrumentationV3 } from './v3/instrumentation';\n\ninterface FastifyHandlerOptions {\n  /**\n   * Callback method deciding whether error should be captured and sent to Sentry\n   *\n   * @param error Captured Fastify error\n   * @param request Fastify request (or any object containing at least method, routeOptions.url, and routerPath)\n   * @param reply Fastify reply (or any object containing at least statusCode)\n   *\n   * @example\n   *\n   * ```javascript\n   * setupFastifyErrorHandler(app, {\n   *   shouldHandleError(_error, _request, reply) {\n   *     return reply.statusCode >= 400;\n   *   },\n   * });\n   * ```\n   *\n   * If using TypeScript, you can cast the request and reply to get full type safety.\n   *\n   * ```typescript\n   * import type { FastifyRequest, FastifyReply } from 'fastify';\n   *\n   * setupFastifyErrorHandler(app, {\n   *   shouldHandleError(error, minimalRequest, minimalReply) {\n   *     const request = minimalRequest as FastifyRequest;\n   *     const reply = minimalReply as FastifyReply;\n   *     return reply.statusCode >= 500;\n   *   },\n   * });\n   * ```\n   */\n  shouldHandleError: (error: Error, request: FastifyRequest, reply: FastifyReply) => boolean;\n}\n\nconst INTEGRATION_NAME = 'Fastify';\nconst INTEGRATION_NAME_V3 = 'Fastify-V3';\n\nexport const instrumentFastifyV3 = generateInstrumentOnce(INTEGRATION_NAME_V3, () => new FastifyInstrumentationV3());\n\nfunction handleFastifyError(\n  this: {\n    diagnosticsChannelExists?: boolean;\n  },\n  error: Error,\n  request: FastifyRequest & { opentelemetry?: () => { span?: Span } },\n  reply: FastifyReply,\n  shouldHandleError: (error: Error, request: FastifyRequest, reply: FastifyReply) => boolean,\n  handlerOrigin: 'diagnostics-channel' | 'onError-hook',\n): void {\n  // Diagnostics channel runs before the onError hook, so we can use it to check if the handler was already registered\n  if (handlerOrigin === 'diagnostics-channel') {\n    this.diagnosticsChannelExists = true;\n  }\n\n  if (this.diagnosticsChannelExists && handlerOrigin === 'onError-hook') {\n    DEBUG_BUILD &&\n      logger.warn(\n        'Fastify error handler was already registered via diagnostics channel.',\n        'You can safely remove `setupFastifyErrorHandler` call.',\n      );\n\n    // If the diagnostics channel already exists, we don't need to handle the error again\n    return;\n  }\n\n  if (shouldHandleError(error, request, reply)) {\n    captureException(error);\n  }\n}\n\nexport const instrumentFastify = generateInstrumentOnce(INTEGRATION_NAME, () => {\n  const fastifyOtelInstrumentationInstance = new FastifyOtelInstrumentation();\n  const plugin = fastifyOtelInstrumentationInstance.plugin();\n  const options = fastifyOtelInstrumentationInstance.getConfig();\n  const shouldHandleError = (options as FastifyHandlerOptions)?.shouldHandleError || defaultShouldHandleError;\n\n  // This message handler works for Fastify versions 3, 4 and 5\n  diagnosticsChannel.subscribe('fastify.initialization', message => {\n    const fastifyInstance = (message as { fastify?: FastifyInstance }).fastify;\n\n    fastifyInstance?.register(plugin).after(err => {\n      if (err) {\n        DEBUG_BUILD && logger.error('Failed to setup Fastify instrumentation', err);\n      } else {\n        instrumentClient();\n\n        if (fastifyInstance) {\n          instrumentOnRequest(fastifyInstance);\n        }\n      }\n    });\n  });\n\n  // This diagnostics channel only works on Fastify version 5\n  // For versions 3 and 4, we use `setupFastifyErrorHandler` instead\n  diagnosticsChannel.subscribe('tracing:fastify.request.handler:error', message => {\n    const { error, request, reply } = message as {\n      error: Error;\n      request: FastifyRequest & { opentelemetry?: () => { span?: Span } };\n      reply: FastifyReply;\n    };\n\n    handleFastifyError.call(handleFastifyError, error, request, reply, shouldHandleError, 'diagnostics-channel');\n  });\n\n  // Returning this as unknown not to deal with the internal types of the FastifyOtelInstrumentation\n  return fastifyOtelInstrumentationInstance as Instrumentation<InstrumentationConfig & FastifyHandlerOptions>;\n});\n\nconst _fastifyIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentFastifyV3();\n      instrumentFastify();\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Fastify](https://fastify.dev/).\n *\n * If you also want to capture errors, you need to call `setupFastifyErrorHandler(app)` after you set up your Fastify server.\n *\n * For more information, see the [fastify documentation](https://docs.sentry.io/platforms/javascript/guides/fastify/).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.fastifyIntegration()],\n * })\n * ```\n */\nexport const fastifyIntegration = defineIntegration(_fastifyIntegration);\n\n/**\n * Default function to determine if an error should be sent to Sentry\n *\n * 3xx and 4xx errors are not sent by default.\n */\nfunction defaultShouldHandleError(_error: Error, _request: FastifyRequest, reply: FastifyReply): boolean {\n  const statusCode = reply.statusCode;\n  // 3xx and 4xx errors are not sent by default.\n  return statusCode >= 500 || statusCode <= 299;\n}\n\n/**\n * Add an Fastify error handler to capture errors to Sentry.\n *\n * @param fastify The Fastify instance to which to add the error handler\n * @param options Configuration options for the handler\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const Fastify = require(\"fastify\");\n *\n * const app = Fastify();\n *\n * Sentry.setupFastifyErrorHandler(app);\n *\n * // Add your routes, etc.\n *\n * app.listen({ port: 3000 });\n * ```\n */\nexport function setupFastifyErrorHandler(fastify: FastifyInstance, options?: Partial<FastifyHandlerOptions>): void {\n  const shouldHandleError = options?.shouldHandleError || defaultShouldHandleError;\n  const plugin = Object.assign(\n    function (fastify: FastifyInstance, _options: unknown, done: () => void): void {\n      fastify.addHook('onError', async (request, reply, error) => {\n        handleFastifyError.call(handleFastifyError, error, request, reply, shouldHandleError, 'onError-hook');\n      });\n      done();\n    },\n    {\n      [Symbol.for('skip-override')]: true,\n      [Symbol.for('fastify.display-name')]: 'sentry-fastify-error-handler',\n    },\n  );\n\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  fastify.register(plugin);\n}\n\nfunction addFastifySpanAttributes(span: Span): void {\n  const spanJSON = spanToJSON(span);\n  const spanName = spanJSON.description;\n  const attributes = spanJSON.data;\n\n  const type = attributes['fastify.type'];\n\n  const isHook = type === 'hook';\n  const isHandler = type === spanName?.startsWith('handler -');\n  // In @fastify/otel `request-handler` is separated by dash, not underscore\n  const isRequestHandler = spanName === 'request' || type === 'request-handler';\n\n  // If this is already set, or we have no fastify span, no need to process again...\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] || (!isHandler && !isRequestHandler && !isHook)) {\n    return;\n  }\n\n  const opPrefix = isHook ? 'hook' : isHandler ? 'middleware' : isRequestHandler ? 'request-handler' : '<unknown>';\n\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.fastify',\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${opPrefix}.fastify`,\n  });\n\n  const attrName = attributes['fastify.name'] || attributes['plugin.name'] || attributes['hook.name'];\n  if (typeof attrName === 'string') {\n    // Try removing `fastify -> ` and `@fastify/otel -> ` prefixes\n    // This is a bit of a hack, and not always working for all spans\n    // But it's the best we can do without a proper API\n    const updatedName = attrName.replace(/^fastify -> /, '').replace(/^@fastify\\/otel -> /, '');\n\n    span.updateName(updatedName);\n  }\n}\n\nfunction instrumentClient(): void {\n  const client = getClient();\n  if (client) {\n    client.on('spanStart', (span: Span) => {\n      addFastifySpanAttributes(span);\n    });\n  }\n}\n\nfunction instrumentOnRequest(fastify: FastifyInstance): void {\n  fastify.addHook('onRequest', async (request: FastifyRequest & { opentelemetry?: () => { span?: Span } }, _reply) => {\n    if (request.opentelemetry) {\n      const { span } = request.opentelemetry();\n\n      if (span) {\n        addFastifySpanAttributes(span);\n      }\n    }\n\n    const routeName = request.routeOptions?.url;\n    const method = request.method || 'GET';\n\n    getIsolationScope().setTransactionName(`${method} ${routeName}`);\n  });\n}\n"], "names": [], "mappings": ";;;;;;;AAsDA,MAAM,gBAAA,GAAmB,SAAS;AAClC,MAAM,mBAAA,GAAsB,YAAY;;AAE3B,MAAA,mBAAA,GAAsB,sBAAsB,CAAC,mBAAmB,EAAE,MAAM,IAAI,wBAAwB,EAAE;;AAEnH,SAAS,kBAAkB;;AAI3B,EAAE,KAAK;AACP,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,iBAAiB;AACnB,EAAE,aAAa;AACf,EAAQ;AACR;AACA,EAAE,IAAI,aAAc,KAAI,qBAAqB,EAAE;AAC/C,IAAI,IAAI,CAAC,wBAAyB,GAAE,IAAI;AACxC;;AAEA,EAAE,IAAI,IAAI,CAAC,4BAA4B,aAAA,KAAkB,cAAc,EAAE;AACzE,IAAI,WAAY;AAChB,MAAM,MAAM,CAAC,IAAI;AACjB,QAAQ,uEAAuE;AAC/E,QAAQ,wDAAwD;AAChE,OAAO;;AAEP;AACA,IAAI;AACJ;;AAEA,EAAE,IAAI,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;AAChD,IAAI,gBAAgB,CAAC,KAAK,CAAC;AAC3B;AACA;;AAEO,MAAM,oBAAoB,sBAAsB,CAAC,gBAAgB,EAAE,MAAM;AAChF,EAAE,MAAM,kCAAmC,GAAE,IAAI,0BAA0B,EAAE;AAC7E,EAAE,MAAM,MAAO,GAAE,kCAAkC,CAAC,MAAM,EAAE;AAC5D,EAAE,MAAM,OAAQ,GAAE,kCAAkC,CAAC,SAAS,EAAE;AAChE,EAAE,MAAM,oBAAoB,CAAC,WAAmC,iBAAkB,IAAG,wBAAwB;;AAE7G;AACA,EAAE,kBAAkB,CAAC,SAAS,CAAC,wBAAwB,EAAE,WAAW;AACpE,IAAI,MAAM,eAAgB,GAAE,CAAC,OAAQ,GAAkC,OAAO;;AAE9E,IAAI,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAA,IAAO;AACnD,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,WAAA,IAAe,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,GAAG,CAAC;AACnF,aAAa;AACb,QAAQ,gBAAgB,EAAE;;AAE1B,QAAQ,IAAI,eAAe,EAAE;AAC7B,UAAU,mBAAmB,CAAC,eAAe,CAAC;AAC9C;AACA;AACA,KAAK,CAAC;AACN,GAAG,CAAC;;AAEJ;AACA;AACA,EAAE,kBAAkB,CAAC,SAAS,CAAC,uCAAuC,EAAE,WAAW;AACnF,IAAI,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAA,EAAQ,GAAE;;AAIlC;;AAEJ,IAAI,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,qBAAqB,CAAC;AAChH,GAAG,CAAC;;AAEJ;AACA,EAAE,OAAO,kCAAmC;AAC5C,CAAC;;AAED,MAAM,mBAAoB,IAAG,MAAM;AACnC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,SAAS,GAAG;AAChB,MAAM,mBAAmB,EAAE;AAC3B,MAAM,iBAAiB,EAAE;AACzB,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,kBAAmB,GAAE,iBAAiB,CAAC,mBAAmB;;AAEvE;AACA;AACA;AACA;AACA;AACA,SAAS,wBAAwB,CAAC,MAAM,EAAS,QAAQ,EAAkB,KAAK,EAAyB;AACzG,EAAE,MAAM,UAAA,GAAa,KAAK,CAAC,UAAU;AACrC;AACA,EAAE,OAAO,UAAW,IAAG,OAAO,UAAA,IAAc,GAAG;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC,OAAO,EAAmB,OAAO,EAAyC;AACnH,EAAE,MAAM,iBAAkB,GAAE,OAAO,EAAE,iBAAA,IAAqB,wBAAwB;AAClF,EAAE,MAAM,MAAA,GAAS,MAAM,CAAC,MAAM;AAC9B,IAAI,UAAU,OAAO,EAAmB,QAAQ,EAAW,IAAI,EAAoB;AACnF,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,EAAE,KAAK,KAAK;AAClE,QAAQ,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,cAAc,CAAC;AAC7G,OAAO,CAAC;AACR,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI;AACzC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,8BAA8B;AAC1E,KAAK;AACL,GAAG;;AAEH;AACA,EAAE,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1B;;AAEA,SAAS,wBAAwB,CAAC,IAAI,EAAc;AACpD,EAAE,MAAM,QAAS,GAAE,UAAU,CAAC,IAAI,CAAC;AACnC,EAAE,MAAM,QAAA,GAAW,QAAQ,CAAC,WAAW;AACvC,EAAE,MAAM,UAAA,GAAa,QAAQ,CAAC,IAAI;;AAElC,EAAE,MAAM,IAAK,GAAE,UAAU,CAAC,cAAc,CAAC;;AAEzC,EAAE,MAAM,MAAA,GAAS,IAAA,KAAS,MAAM;AAChC,EAAE,MAAM,SAAU,GAAE,IAAK,KAAI,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC;AAC9D;AACA,EAAE,MAAM,mBAAmB,QAAA,KAAa,SAAU,IAAG,IAAK,KAAI,iBAAiB;;AAE/E;AACA,EAAE,IAAI,UAAU,CAAC,4BAA4B,MAAM,CAAC,SAAA,IAAa,CAAC,gBAAA,IAAoB,CAAC,MAAM,CAAC,EAAE;AAChG,IAAI;AACJ;;AAEA,EAAE,MAAM,QAAA,GAAW,MAAA,GAAS,MAAO,GAAE,SAAU,GAAE,eAAe,gBAAA,GAAmB,iBAAA,GAAoB,WAAW;;AAElH,EAAE,IAAI,CAAC,aAAa,CAAC;AACrB,IAAI,CAAC,gCAAgC,GAAG,wBAAwB;AAChE,IAAI,CAAC,4BAA4B,GAAG,CAAC,EAAA,QAAA,CAAA,QAAA,CAAA;AACA,GAAA,CAAA;;AAEA,EAAA,MAAA,QAAA,GAAA,UAAA,CAAA,cAAA,CAAA,IAAA,UAAA,CAAA,aAAA,CAAA,IAAA,UAAA,CAAA,WAAA,CAAA;AACA,EAAA,IAAA,OAAA,QAAA,KAAA,QAAA,EAAA;AACA;AACA;AACA;AACA,IAAA,MAAA,WAAA,GAAA,QAAA,CAAA,OAAA,CAAA,cAAA,EAAA,EAAA,CAAA,CAAA,OAAA,CAAA,qBAAA,EAAA,EAAA,CAAA;;AAEA,IAAA,IAAA,CAAA,UAAA,CAAA,WAAA,CAAA;AACA;AACA;;AAEA,SAAA,gBAAA,GAAA;AACA,EAAA,MAAA,MAAA,GAAA,SAAA,EAAA;AACA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,CAAA,EAAA,CAAA,WAAA,EAAA,CAAA,IAAA,KAAA;AACA,MAAA,wBAAA,CAAA,IAAA,CAAA;AACA,KAAA,CAAA;AACA;AACA;;AAEA,SAAA,mBAAA,CAAA,OAAA,EAAA;AACA,EAAA,OAAA,CAAA,OAAA,CAAA,WAAA,EAAA,OAAA,OAAA,EAAA,MAAA,KAAA;AACA,IAAA,IAAA,OAAA,CAAA,aAAA,EAAA;AACA,MAAA,MAAA,EAAA,IAAA,EAAA,GAAA,OAAA,CAAA,aAAA,EAAA;;AAEA,MAAA,IAAA,IAAA,EAAA;AACA,QAAA,wBAAA,CAAA,IAAA,CAAA;AACA;AACA;;AAEA,IAAA,MAAA,SAAA,GAAA,OAAA,CAAA,YAAA,EAAA,GAAA;AACA,IAAA,MAAA,MAAA,GAAA,OAAA,CAAA,MAAA,IAAA,KAAA;;AAEA,IAAA,iBAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA,GAAA,CAAA;AACA;;;;"}