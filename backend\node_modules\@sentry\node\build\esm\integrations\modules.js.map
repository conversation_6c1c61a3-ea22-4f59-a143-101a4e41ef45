{"version": 3, "file": "modules.js", "sources": ["../../../src/integrations/modules.ts"], "sourcesContent": ["import { existsSync, readFileSync } from 'node:fs';\nimport { dirname, join } from 'node:path';\nimport type { IntegrationFn } from '@sentry/core';\nimport { isCjs } from '../utils/commonjs';\n\ntype ModuleInfo = Record<string, string>;\n\nlet moduleCache: ModuleInfo | undefined;\n\nconst INTEGRATION_NAME = 'Modules';\n\ndeclare const __SENTRY_SERVER_MODULES__: Record<string, string>;\n\n/**\n * `__SENTRY_SERVER_MODULES__` can be replaced at build time with the modules loaded by the server.\n * Right now, we leverage this in Next.js to circumvent the problem that we do not get access to these things at runtime.\n */\nconst SERVER_MODULES = typeof __SENTRY_SERVER_MODULES__ === 'undefined' ? {} : __SENTRY_SERVER_MODULES__;\n\nconst _modulesIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event) {\n      event.modules = {\n        ...event.modules,\n        ..._getModules(),\n      };\n\n      return event;\n    },\n    getModules: _getModules,\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Add node modules / packages to the event.\n * For this, multiple sources are used:\n * - They can be injected at build time into the __SENTRY_SERVER_MODULES__ variable (e.g. in Next.js)\n * - They are extracted from the dependencies & devDependencies in the package.json file\n * - They are extracted from the require.cache (CJS only)\n */\nexport const modulesIntegration = _modulesIntegration;\n\nfunction getRequireCachePaths(): string[] {\n  try {\n    return require.cache ? Object.keys(require.cache as Record<string, unknown>) : [];\n  } catch (e) {\n    return [];\n  }\n}\n\n/** Extract information about package.json modules */\nfunction collectModules(): ModuleInfo {\n  return {\n    ...SERVER_MODULES,\n    ...getModulesFromPackageJson(),\n    ...(isCjs() ? collectRequireModules() : {}),\n  };\n}\n\n/** Extract information about package.json modules from require.cache */\nfunction collectRequireModules(): ModuleInfo {\n  const mainPaths = require.main?.paths || [];\n  const paths = getRequireCachePaths();\n\n  // We start with the modules from package.json (if possible)\n  // These may be overwritten by more specific versions from the require.cache\n  const infos: ModuleInfo = {};\n  const seen = new Set<string>();\n\n  paths.forEach(path => {\n    let dir = path;\n\n    /** Traverse directories upward in the search of package.json file */\n    const updir = (): void | (() => void) => {\n      const orig = dir;\n      dir = dirname(orig);\n\n      if (!dir || orig === dir || seen.has(orig)) {\n        return undefined;\n      }\n      if (mainPaths.indexOf(dir) < 0) {\n        return updir();\n      }\n\n      const pkgfile = join(orig, 'package.json');\n      seen.add(orig);\n\n      if (!existsSync(pkgfile)) {\n        return updir();\n      }\n\n      try {\n        const info = JSON.parse(readFileSync(pkgfile, 'utf8')) as {\n          name: string;\n          version: string;\n        };\n        infos[info.name] = info.version;\n      } catch (_oO) {\n        // no-empty\n      }\n    };\n\n    updir();\n  });\n\n  return infos;\n}\n\n/** Fetches the list of modules and the versions loaded by the entry file for your node.js app. */\nfunction _getModules(): ModuleInfo {\n  if (!moduleCache) {\n    moduleCache = collectModules();\n  }\n  return moduleCache;\n}\n\ninterface PackageJson {\n  dependencies?: Record<string, string>;\n  devDependencies?: Record<string, string>;\n}\n\nfunction getPackageJson(): PackageJson {\n  try {\n    const filePath = join(process.cwd(), 'package.json');\n    const packageJson = JSON.parse(readFileSync(filePath, 'utf8')) as PackageJson;\n\n    return packageJson;\n  } catch (e) {\n    return {};\n  }\n}\n\nfunction getModulesFromPackageJson(): ModuleInfo {\n  const packageJson = getPackageJson();\n\n  return {\n    ...packageJson.dependencies,\n    ...packageJson.devDependencies,\n  };\n}\n"], "names": [], "mappings": ";;;;AAOA,IAAI,WAAW;;AAEf,MAAM,gBAAA,GAAmB,SAAS;;AAIlC;AACA;AACA;AACA;AACA,MAAM,cAAA,GAAiB,OAAO,yBAA0B,KAAI,cAAc,EAAG,GAAE,yBAAyB;;AAExG,MAAM,mBAAoB,IAAG,MAAM;AACnC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,MAAM,KAAK,CAAC,OAAA,GAAU;AACtB,QAAQ,GAAG,KAAK,CAAC,OAAO;AACxB,QAAQ,GAAG,WAAW,EAAE;AACxB,OAAO;;AAEP,MAAM,OAAO,KAAK;AAClB,KAAK;AACL,IAAI,UAAU,EAAE,WAAW;AAC3B,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,kBAAmB,GAAE;;AAElC,SAAS,oBAAoB,GAAa;AAC1C,EAAE,IAAI;AACN,IAAI,OAAO,OAAO,CAAC,KAAA,GAAQ,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAA,EAAkC,GAAE,EAAE;AACrF,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,EAAE;AACb;AACA;;AAEA;AACA,SAAS,cAAc,GAAe;AACtC,EAAE,OAAO;AACT,IAAI,GAAG,cAAc;AACrB,IAAI,GAAG,yBAAyB,EAAE;AAClC,IAAI,IAAI,KAAK,EAAG,GAAE,qBAAqB,EAAG,GAAE,EAAE,CAAC;AAC/C,GAAG;AACH;;AAEA;AACA,SAAS,qBAAqB,GAAe;AAC7C,EAAE,MAAM,SAAU,GAAE,OAAO,CAAC,IAAI,EAAE,KAAA,IAAS,EAAE;AAC7C,EAAE,MAAM,KAAA,GAAQ,oBAAoB,EAAE;;AAEtC;AACA;AACA,EAAE,MAAM,KAAK,GAAe,EAAE;AAC9B,EAAE,MAAM,IAAK,GAAE,IAAI,GAAG,EAAU;;AAEhC,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ;AACxB,IAAI,IAAI,GAAI,GAAE,IAAI;;AAElB;AACA,IAAI,MAAM,KAAA,GAAQ,MAA2B;AAC7C,MAAM,MAAM,IAAK,GAAE,GAAG;AACtB,MAAM,GAAI,GAAE,OAAO,CAAC,IAAI,CAAC;;AAEzB,MAAM,IAAI,CAAC,GAAA,IAAO,IAAK,KAAI,GAAI,IAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAClD,QAAQ,OAAO,SAAS;AACxB;AACA,MAAM,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAA,GAAI,CAAC,EAAE;AACtC,QAAQ,OAAO,KAAK,EAAE;AACtB;;AAEA,MAAM,MAAM,UAAU,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC;AAChD,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;;AAEpB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAChC,QAAQ,OAAO,KAAK,EAAE;AACtB;;AAEA,MAAM,IAAI;AACV,QAAQ,MAAM,IAAA,GAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;;AAGrD;AACR,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE,GAAE,IAAI,CAAC,OAAO;AACvC,OAAQ,CAAA,OAAO,GAAG,EAAE;AACpB;AACA;AACA,KAAK;;AAEL,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;;AAEJ,EAAE,OAAO,KAAK;AACd;;AAEA;AACA,SAAS,WAAW,GAAe;AACnC,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,WAAY,GAAE,cAAc,EAAE;AAClC;AACA,EAAE,OAAO,WAAW;AACpB;;AAOA,SAAS,cAAc,GAAgB;AACvC,EAAE,IAAI;AACN,IAAI,MAAM,QAAA,GAAW,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC;AACxD,IAAI,MAAM,WAAA,GAAc,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAE;;AAEnE,IAAI,OAAO,WAAW;AACtB,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,EAAE;AACb;AACA;;AAEA,SAAS,yBAAyB,GAAe;AACjD,EAAE,MAAM,WAAA,GAAc,cAAc,EAAE;;AAEtC,EAAE,OAAO;AACT,IAAI,GAAG,WAAW,CAAC,YAAY;AAC/B,IAAI,GAAG,WAAW,CAAC,eAAe;AAClC,GAAG;AACH;;;;"}