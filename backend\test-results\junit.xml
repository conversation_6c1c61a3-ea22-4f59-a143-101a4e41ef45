<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="31" failures="19" errors="0" time="18.21">
  <testsuite name="src\tests\auth.integration.test.js" errors="0" failures="5" skipped="0" timestamp="2025-07-08T08:56:11" time="14.268" tests="5">
    <testcase classname="Auth Integration Tests &gt; POST /api/auth/register should register a new user" name="Auth Integration Tests &gt; POST /api/auth/register should register a new user" time="4.749">
      <failure>Error: expect(received).toEqual(expected) // deep equality

Expected: 201
Received: 500
    at Object.toEqual (E:\BUDGET TRACKER\backend\src\tests\auth.integration.test.js:33:30)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
      <failure>MongoNetworkError: 180B0000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\ws\deps\openssl\openssl\ssl\record\rec_layer_s3.c:1605:SSL alert number 80

    at TLSSocket.&lt;anonymous&gt; (E:\BUDGET TRACKER\backend\node_modules\mongodb\src\cmap\connect.ts:395:16)
    at Object.onceWrapper (node:events:639:26)
    at TLSSocket.emit (node:events:524:28)
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)</failure>
    </testcase>
    <testcase classname="Auth Integration Tests &gt; POST /api/auth/login should login with valid credentials" name="Auth Integration Tests &gt; POST /api/auth/login should login with valid credentials" time="3.665">
      <failure>Error: expect(received).toEqual(expected) // deep equality

Expected: 200
Received: 401
    at Object.toEqual (E:\BUDGET TRACKER\backend\src\tests\auth.integration.test.js:56:30)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Auth Integration Tests &gt; Protected Routes should access protected route with valid token" name="Auth Integration Tests &gt; Protected Routes should access protected route with valid token" time="1.074">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;token&apos;)
    at Object.token (E:\BUDGET TRACKER\backend\src\tests\auth.integration.test.js:78:34)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Auth Integration Tests &gt; Protected Routes should reject access without token" name="Auth Integration Tests &gt; Protected Routes should reject access without token" time="0.712">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;token&apos;)
    at Object.token (E:\BUDGET TRACKER\backend\src\tests\auth.integration.test.js:78:34)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Auth Integration Tests &gt; Protected Routes should reject access with invalid token" name="Auth Integration Tests &gt; Protected Routes should reject access with invalid token" time="1.19">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;token&apos;)
    at Object.token (E:\BUDGET TRACKER\backend\src\tests\auth.integration.test.js:78:34)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
  </testsuite>
  <testsuite name="src\tests\minimal-mongoose.test.js" errors="0" failures="0" skipped="0" timestamp="2025-07-08T08:56:25" time="1.199" tests="2">
    <testcase classname="Minimal Mongoose Test should simply pass" name="Minimal Mongoose Test should simply pass" time="0.003">
    </testcase>
    <testcase classname="Minimal Mongoose Test should have active connection" name="Minimal Mongoose Test should have active connection" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="src\tests\auth.controller.test.js" errors="0" failures="12" skipped="0" timestamp="2025-07-08T08:56:27" time="0.811" tests="18">
    <testcase classname="Auth Controller &gt; registerUser should register a new user" name="Auth Controller &gt; registerUser should register a new user" time="0.017">
    </testcase>
    <testcase classname="Auth Controller &gt; registerUser should reject registration with missing fields" name="Auth Controller &gt; registerUser should reject registration with missing fields" time="0.01">
    </testcase>
    <testcase classname="Auth Controller &gt; registerUser should reject duplicate email registration" name="Auth Controller &gt; registerUser should reject duplicate email registration" time="0.024">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 400
Received: 201

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:201:26)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; registerUser edge cases should reject extremely long names" name="Auth Controller &gt; registerUser edge cases should reject extremely long names" time="0.037">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 400
Received: 201

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:223:26)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; registerUser edge cases should reject invalid email formats" name="Auth Controller &gt; registerUser edge cases should reject invalid email formats" time="0.02">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 400
Received: 201

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:242:26)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; Input Validation should reject empty name" name="Auth Controller &gt; Input Validation should reject empty name" time="0.007">
    </testcase>
    <testcase classname="Auth Controller &gt; Input Validation should reject invalid email formats" name="Auth Controller &gt; Input Validation should reject invalid email formats" time="0.013">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 400
Received: 201

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:267:28)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; Input Validation should reject short passwords" name="Auth Controller &gt; Input Validation should reject short passwords" time="0.012">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 400
Received: 201

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:275:26)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; loginUser should login with valid credentials" name="Auth Controller &gt; loginUser should login with valid credentials" time="0.021">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 200
Received: 401

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:304:26)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; loginUser should reject login with invalid password" name="Auth Controller &gt; loginUser should reject login with invalid password" time="0.022">
    </testcase>
    <testcase classname="Auth Controller &gt; loginUser should reject login with non-existent email" name="Auth Controller &gt; loginUser should reject login with non-existent email" time="0.021">
    </testcase>
    <testcase classname="Auth Controller &gt; loginUser error scenarios should handle database errors during login" name="Auth Controller &gt; loginUser error scenarios should handle database errors during login" time="0.014">
    </testcase>
    <testcase classname="Auth Controller &gt; refreshToken should refresh tokens with valid refresh token" name="Auth Controller &gt; refreshToken should refresh tokens with valid refresh token" time="0.016">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;refreshToken&apos;)
    at Object.refreshToken (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:406:63)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; refreshToken should reject refresh with invalid token" name="Auth Controller &gt; refreshToken should reject refresh with invalid token" time="0.016">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;refreshToken&apos;)
    at Object.refreshToken (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:406:63)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; refreshToken should reject refresh with missing token" name="Auth Controller &gt; refreshToken should reject refresh with missing token" time="0.018">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;refreshToken&apos;)
    at Object.refreshToken (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:406:63)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; refreshToken should rotate refresh tokens" name="Auth Controller &gt; refreshToken should rotate refresh tokens" time="0.016">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;refreshToken&apos;)
    at Object.refreshToken (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:406:63)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; refreshToken edge cases should handle expired refresh tokens" name="Auth Controller &gt; refreshToken edge cases should handle expired refresh tokens" time="0.019">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 403
Received: 500

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:518:26)</failure>
    </testcase>
    <testcase classname="Auth Controller &gt; refreshToken edge cases should handle missing user during refresh" name="Auth Controller &gt; refreshToken edge cases should handle missing user during refresh" time="0.017">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: 403
Received: 500

Number of calls: 1
    at Object.toHaveBeenCalledWith (E:\BUDGET TRACKER\backend\src\tests\auth.controller.test.js:537:26)</failure>
    </testcase>
  </testsuite>
  <testsuite name="src\tests\auth.controller.debug.test.js" errors="0" failures="2" skipped="0" timestamp="2025-07-08T08:56:27" time="0.214" tests="2">
    <testcase classname="Auth Controller Debug should simply pass" name="Auth Controller Debug should simply pass" time="0.001">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.createClient (E:\BUDGET TRACKER\backend\node_modules\mongoose\lib\drivers\node-mongodb-native\connection.js:237:11)
    at NativeConnection.openUri (E:\BUDGET TRACKER\backend\node_modules\mongoose\lib\connection.js:1071:34)
    at Mongoose.connect (E:\BUDGET TRACKER\backend\node_modules\mongoose\lib\mongoose.js:450:15)
    at Object.connect (E:\BUDGET TRACKER\backend\src\tests\auth.controller.debug.test.js:10:22)
    at Promise.finally.completed (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:1499:10)
    at _callCircusHook (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:978:40)
    at runNextTicks (node:internal/process/task_queues:65:5)
    at processImmediate (node:internal/timers:459:9)
    at _runTestsForDescribeBlock (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:775:7)
    at _runTestsForDescribeBlock (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:829:11)
    at run (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:1920:21)
    at jestAdapter (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\runner.js:101:19)
    at runTestInternal (E:\BUDGET TRACKER\backend\node_modules\jest-runner\build\index.js:272:16)
    at runTest (E:\BUDGET TRACKER\backend\node_modules\jest-runner\build\index.js:340:7)</failure>
    </testcase>
    <testcase classname="Auth Controller Debug should connect to database" name="Auth Controller Debug should connect to database" time="0">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.createClient (E:\BUDGET TRACKER\backend\node_modules\mongoose\lib\drivers\node-mongodb-native\connection.js:237:11)
    at NativeConnection.openUri (E:\BUDGET TRACKER\backend\node_modules\mongoose\lib\connection.js:1071:34)
    at Mongoose.connect (E:\BUDGET TRACKER\backend\node_modules\mongoose\lib\mongoose.js:450:15)
    at Object.connect (E:\BUDGET TRACKER\backend\src\tests\auth.controller.debug.test.js:10:22)
    at Promise.finally.completed (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:1559:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:1499:10)
    at _callCircusHook (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:978:40)
    at runNextTicks (node:internal/process/task_queues:65:5)
    at processImmediate (node:internal/timers:459:9)
    at _runTestsForDescribeBlock (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:775:7)
    at _runTestsForDescribeBlock (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:829:11)
    at run (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:757:3)
    at runAndTransformResultsToJestFormat (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\jestAdapterInit.js:1920:21)
    at jestAdapter (E:\BUDGET TRACKER\backend\node_modules\jest-circus\build\runner.js:101:19)
    at runTestInternal (E:\BUDGET TRACKER\backend\node_modules\jest-runner\build\index.js:272:16)
    at runTest (E:\BUDGET TRACKER\backend\node_modules\jest-runner\build\index.js:340:7)</failure>
    </testcase>
  </testsuite>
  <testsuite name="src\tests\async.test.js" errors="0" failures="0" skipped="0" timestamp="2025-07-08T08:56:28" time="0.384" tests="1">
    <testcase classname="Async Test should handle async" name="Async Test should handle async" time="0.107">
    </testcase>
  </testsuite>
  <testsuite name="src\tests\isolated.test.js" errors="0" failures="0" skipped="0" timestamp="2025-07-08T08:56:28" time="0.197" tests="1">
    <testcase classname="Isolated Test should pass" name="Isolated Test should pass" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="src\tests\simple.test.js" errors="0" failures="0" skipped="0" timestamp="2025-07-08T08:56:28" time="0.214" tests="1">
    <testcase classname=" Simple test" name=" Simple test" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="test\simple.test.js" errors="0" failures="0" skipped="0" timestamp="2025-07-08T08:56:28" time="0.166" tests="1">
    <testcase classname="Simple Test should pass a simple test" name="Simple Test should pass a simple test" time="0.003">
    </testcase>
  </testsuite>
</testsuites>