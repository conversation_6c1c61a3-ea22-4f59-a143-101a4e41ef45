{"logs": [{"outputFile": "com.example.budget_tracker.app-mergeDebugResources-49:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "31,32,33,34,35,36,37,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2965,3059,3161,3258,3357,3465,3571,8293", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3054,3156,3253,3352,3460,3566,3686,8389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4700", "endColumns": "129", "endOffsets": "4825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "57,60,74,75,78,79,80", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6011,6279,7993,8071,8394,8563,8642", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "6076,6361,8066,8208,8558,8637,8713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\006877986797b3b6be5cf579d190afc8\\transformed\\jetified-credentials-1.5.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,116", "endOffsets": "161,278"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2737,2848", "endColumns": "110,116", "endOffsets": "2843,2960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,8213", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,8288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,249,371,515,650,786,913,1054,1154,1295,1439", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "156,244,366,510,645,781,908,1049,1149,1290,1434,1561"}, "to": {"startLines": "56,59,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5905,6191,6676,6798,6942,7077,7213,7340,7481,7581,7722,7866", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "6006,6274,6793,6937,7072,7208,7335,7476,7576,7717,7861,7988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "58,61,62,63", "startColumns": "4,4,4,4", "startOffsets": "6081,6366,6467,6579", "endColumns": "109,100,111,96", "endOffsets": "6186,6462,6574,6671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e1f6d2e0b1aa38467964f5b59b4f29f9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3691,3797,3956,4082,4191,4347,4477,4597,4830,4984,5091,5252,5380,5522,5698,5765,5827", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "3792,3951,4077,4186,4342,4472,4592,4695,4979,5086,5247,5375,5517,5693,5760,5822,5900"}}]}]}