{"version": 3, "file": "object.js", "sources": ["../../../src/utils/object.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { WrappedFunction } from '../types-hoist/wrappedfunction';\nimport { htmlTreeAsString } from './browser';\nimport { isElement, isError, isEvent, isInstanceOf, isPrimitive } from './is';\nimport { logger } from './logger';\nimport { truncate } from './string';\n\n/**\n * Replace a method in an object with a wrapped version of itself.\n *\n * If the method on the passed object is not a function, the wrapper will not be applied.\n *\n * @param source An object that contains a method to be wrapped.\n * @param name The name of the method to be wrapped.\n * @param replacementFactory A higher-order function that takes the original version of the given method and returns a\n * wrapped version. Note: The function returned by `replacementFactory` needs to be a non-arrow function, in order to\n * preserve the correct value of `this`, and the original method must be called using `origMethod.call(this, <other\n * args>)` or `origMethod.apply(this, [<other args>])` (rather than being called directly), again to preserve `this`.\n * @returns void\n */\nexport function fill(source: { [key: string]: any }, name: string, replacementFactory: (...args: any[]) => any): void {\n  if (!(name in source)) {\n    return;\n  }\n\n  // explicitly casting to unknown because we don't know the type of the method initially at all\n  const original = source[name] as unknown;\n\n  if (typeof original !== 'function') {\n    return;\n  }\n\n  const wrapped = replacementFactory(original) as WrappedFunction;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    markFunctionWrapped(wrapped, original);\n  }\n\n  try {\n    source[name] = wrapped;\n  } catch {\n    DEBUG_BUILD && logger.log(`Failed to replace method \"${name}\" in object`, source);\n  }\n}\n\n/**\n * Defines a non-enumerable property on the given object.\n *\n * @param obj The object on which to set the property\n * @param name The name of the property to be set\n * @param value The value to which to set the property\n */\nexport function addNonEnumerableProperty(obj: object, name: string, value: unknown): void {\n  try {\n    Object.defineProperty(obj, name, {\n      // enumerable: false, // the default, so we can save on bundle size by not explicitly setting it\n      value: value,\n      writable: true,\n      configurable: true,\n    });\n  } catch (o_O) {\n    DEBUG_BUILD && logger.log(`Failed to add non-enumerable property \"${name}\" to object`, obj);\n  }\n}\n\n/**\n * Remembers the original function on the wrapped function and\n * patches up the prototype.\n *\n * @param wrapped the wrapper function\n * @param original the original function that gets wrapped\n */\nexport function markFunctionWrapped(wrapped: WrappedFunction, original: WrappedFunction): void {\n  try {\n    const proto = original.prototype || {};\n    wrapped.prototype = original.prototype = proto;\n    addNonEnumerableProperty(wrapped, '__sentry_original__', original);\n  } catch (o_O) {} // eslint-disable-line no-empty\n}\n\n/**\n * This extracts the original function if available.  See\n * `markFunctionWrapped` for more information.\n *\n * @param func the function to unwrap\n * @returns the unwrapped version of the function if available.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function getOriginalFunction<T extends Function>(func: WrappedFunction<T>): T | undefined {\n  return func.__sentry_original__;\n}\n\n/**\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\n * non-enumerable properties attached.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n * @returns An Event or Error turned into an object - or the value argument itself, when value is neither an Event nor\n *  an Error.\n */\nexport function convertToPlainObject<V>(value: V):\n  | {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    }\n  | {\n      [ownProps: string]: unknown;\n      message: string;\n      name: string;\n      stack?: string;\n    }\n  | V {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value),\n    };\n  } else if (isEvent(value)) {\n    const newObj: {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    } = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value),\n    };\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      newObj.detail = value.detail;\n    }\n\n    return newObj;\n  } else {\n    return value;\n  }\n}\n\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target: unknown): string {\n  try {\n    return isElement(target) ? htmlTreeAsString(target) : Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj: unknown): { [key: string]: unknown } {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps: { [key: string]: unknown } = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = (obj as Record<string, unknown>)[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\nexport function extractExceptionKeysForMessage(exception: Record<string, unknown>, maxLength: number = 40): string {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n\n  const firstKey = keys[0];\n\n  if (!firstKey) {\n    return '[object has no keys]';\n  }\n\n  if (firstKey.length >= maxLength) {\n    return truncate(firstKey, maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return a new object having removed all fields whose value was `undefined`.\n * Works recursively on objects and arrays.\n *\n * Attention: This function keeps circular references in the returned object.\n *\n * @deprecated This function is no longer used by the SDK and will be removed in a future major version.\n */\nexport function dropUndefinedKeys<T>(inputValue: T): T {\n  // This map keeps track of what already visited nodes map to.\n  // Our Set - based memoBuilder doesn't work here because we want to the output object to have the same circular\n  // references as the input object.\n  const memoizationMap = new Map<unknown, unknown>();\n\n  // This function just proxies `_dropUndefinedKeys` to keep the `memoBuilder` out of this function's API\n  return _dropUndefinedKeys(inputValue, memoizationMap);\n}\n\nfunction _dropUndefinedKeys<T>(inputValue: T, memoizationMap: Map<unknown, unknown>): T {\n  // Early return for primitive values\n  if (inputValue === null || typeof inputValue !== 'object') {\n    return inputValue;\n  }\n\n  // Check memo map first for all object types\n  const memoVal = memoizationMap.get(inputValue);\n  if (memoVal !== undefined) {\n    return memoVal as T;\n  }\n\n  // handle arrays\n  if (Array.isArray(inputValue)) {\n    const returnValue: unknown[] = [];\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    inputValue.forEach(value => {\n      returnValue.push(_dropUndefinedKeys(value, memoizationMap));\n    });\n\n    return returnValue as unknown as T;\n  }\n\n  if (isPojo(inputValue)) {\n    const returnValue: { [key: string]: unknown } = {};\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    const keys = Object.keys(inputValue);\n\n    keys.forEach(key => {\n      const val = inputValue[key];\n      if (val !== undefined) {\n        returnValue[key] = _dropUndefinedKeys(val, memoizationMap);\n      }\n    });\n\n    return returnValue as T;\n  }\n\n  // For other object types, return as is\n  return inputValue;\n}\n\nfunction isPojo(input: unknown): input is Record<string, unknown> {\n  // Plain objects have Object as constructor or no constructor\n  const constructor = (input as object).constructor;\n  return constructor === Object || constructor === undefined;\n}\n\n/**\n * Ensure that something is an object.\n *\n * Turns `undefined` and `null` into `String`s and all other primitives into instances of their respective wrapper\n * classes (String, Boolean, Number, etc.). Acts as the identity function on non-primitives.\n *\n * @param wat The subject of the objectification\n * @returns A version of `wat` which can safely be used with `Object` class methods\n */\nexport function objectify(wat: unknown): typeof Object {\n  let objectified;\n  switch (true) {\n    // this will catch both undefined and null\n    case wat == undefined:\n      objectified = new String(wat);\n      break;\n\n    // Though symbols and bigints do have wrapper classes (`Symbol` and `BigInt`, respectively), for whatever reason\n    // those classes don't have constructors which can be used with the `new` keyword. We therefore need to cast each as\n    // an object in order to wrap it.\n    case typeof wat === 'symbol' || typeof wat === 'bigint':\n      objectified = Object(wat);\n      break;\n\n    // this will catch the remaining primitives: `String`, `Number`, and `Boolean`\n    case isPrimitive(wat):\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      objectified = new (wat as any).constructor(wat);\n      break;\n\n    // by process of elimination, at this point we know that `wat` must already be an object\n    default:\n      objectified = wat;\n      break;\n  }\n  return objectified;\n}\n"], "names": ["DEBUG_BUILD", "logger", "isError", "isEvent", "isInstanceOf", "isElement", "htmlTreeAsString", "truncate", "isPrimitive"], "mappings": ";;;;;;;;AAAA;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,MAAM,EAA0B,IAAI,EAAU,kBAAkB,EAAiC;AACtH,EAAE,IAAI,EAAE,QAAQ,MAAM,CAAC,EAAE;AACzB,IAAI;AACJ;;AAEA;AACA,EAAE,MAAM,QAAS,GAAE,MAAM,CAAC,IAAI,CAAE;;AAEhC,EAAE,IAAI,OAAO,QAAS,KAAI,UAAU,EAAE;AACtC,IAAI;AACJ;;AAEA,EAAE,MAAM,OAAQ,GAAE,kBAAkB,CAAC,QAAQ,CAAE;;AAE/C;AACA;AACA,EAAE,IAAI,OAAO,OAAQ,KAAI,UAAU,EAAE;AACrC,IAAI,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC1C;;AAEA,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAA,GAAI,OAAO;AAC1B,IAAI,MAAM;AACV,IAAIA,sBAAY,IAAGC,aAAM,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC;AACrF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC,GAAG,EAAU,IAAI,EAAU,KAAK,EAAiB;AAC1F,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE;AACrC;AACA,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,YAAY,EAAE,IAAI;AACxB,KAAK,CAAC;AACN,GAAI,CAAA,OAAO,GAAG,EAAE;AAChB,IAAID,sBAAY,IAAGC,aAAM,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;AAC/F;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,OAAO,EAAmB,QAAQ,EAAyB;AAC/F,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,QAAQ,CAAC,SAAU,IAAG,EAAE;AAC1C,IAAI,OAAO,CAAC,SAAU,GAAE,QAAQ,CAAC,SAAA,GAAY,KAAK;AAClD,IAAI,wBAAwB,CAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,CAAC;AACtE,GAAI,CAAA,OAAO,GAAG,EAAE,EAAC;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAqB,IAAI,EAAqC;AACjG,EAAE,OAAO,IAAI,CAAC,mBAAmB;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB,CAAI,KAAK;;AAc3C,CAAI;AACN,EAAE,IAAIC,UAAO,CAAC,KAAK,CAAC,EAAE;AACtB,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,KAAK,CAAC,OAAO;AAC5B,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI;AACtB,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;AACxB,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAChC,KAAK;AACL,GAAE,MAAO,IAAIC,UAAO,CAAC,KAAK,CAAC,EAAE;AAC7B,IAAI,MAAM;;AAMN,GAAI;AACR,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI;AACtB,MAAM,MAAM,EAAE,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC;AAChD,MAAM,aAAa,EAAE,oBAAoB,CAAC,KAAK,CAAC,aAAa,CAAC;AAC9D,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAChC,KAAK;;AAEL,IAAI,IAAI,OAAO,WAAA,KAAgB,WAAA,IAAeC,eAAY,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;AAChF,MAAM,MAAM,CAAC,MAAA,GAAS,KAAK,CAAC,MAAM;AAClC;;AAEA,IAAI,OAAO,MAAM;AACjB,SAAS;AACT,IAAI,OAAO,KAAK;AAChB;AACA;;AAEA;AACA,SAAS,oBAAoB,CAAC,MAAM,EAAmB;AACvD,EAAE,IAAI;AACN,IAAI,OAAOC,YAAS,CAAC,MAAM,IAAIC,wBAAgB,CAAC,MAAM,CAAA,GAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AAChG,GAAI,CAAA,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,WAAW;AACtB;AACA;;AAEA;AACA,SAAS,gBAAgB,CAAC,GAAG,EAAuC;AACpE,EAAE,IAAI,OAAO,GAAI,KAAI,YAAY,GAAA,KAAQ,IAAI,EAAE;AAC/C,IAAI,MAAM,cAAc,GAA+B,EAAE;AACzD,IAAI,KAAK,MAAM,QAAS,IAAG,GAAG,EAAE;AAChC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;AAC/D,QAAQ,cAAc,CAAC,QAAQ,CAAE,GAAE,CAAC,GAAI,GAA4B,QAAQ,CAAC;AAC7E;AACA;AACA,IAAI,OAAO,cAAc;AACzB,SAAS;AACT,IAAI,OAAO,EAAE;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,8BAA8B,CAAC,SAAS,EAA2B,SAAS,GAAW,EAAE,EAAU;AACnH,EAAE,MAAM,IAAK,GAAE,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;AAC3D,EAAE,IAAI,CAAC,IAAI,EAAE;;AAEb,EAAE,MAAM,QAAS,GAAE,IAAI,CAAC,CAAC,CAAC;;AAE1B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,OAAO,sBAAsB;AACjC;;AAEA,EAAE,IAAI,QAAQ,CAAC,MAAO,IAAG,SAAS,EAAE;AACpC,IAAI,OAAOC,eAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC;AACxC;;AAEA,EAAE,KAAK,IAAI,YAAA,GAAe,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE,YAAY,EAAE,EAAE;AACzE,IAAI,MAAM,UAAA,GAAa,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAC7D,IAAI,IAAI,UAAU,CAAC,MAAO,GAAE,SAAS,EAAE;AACvC,MAAM;AACN;AACA,IAAI,IAAI,YAAA,KAAiB,IAAI,CAAC,MAAM,EAAE;AACtC,MAAM,OAAO,UAAU;AACvB;AACA,IAAI,OAAOA,eAAQ,CAAC,UAAU,EAAE,SAAS,CAAC;AAC1C;;AAEA,EAAE,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAI,UAAU,EAAQ;AACvD;AACA;AACA;AACA,EAAE,MAAM,cAAe,GAAE,IAAI,GAAG,EAAoB;;AAEpD;AACA,EAAE,OAAO,kBAAkB,CAAC,UAAU,EAAE,cAAc,CAAC;AACvD;;AAEA,SAAS,kBAAkB,CAAI,UAAU,EAAK,cAAc,EAA4B;AACxF;AACA,EAAE,IAAI,UAAA,KAAe,IAAA,IAAQ,OAAO,UAAA,KAAe,QAAQ,EAAE;AAC7D,IAAI,OAAO,UAAU;AACrB;;AAEA;AACA,EAAE,MAAM,UAAU,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC;AAChD,EAAE,IAAI,OAAQ,KAAI,SAAS,EAAE;AAC7B,IAAI,OAAO,OAAQ;AACnB;;AAEA;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AACjC,IAAI,MAAM,WAAW,GAAc,EAAE;AACrC;AACA,IAAI,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;;AAE/C,IAAI,UAAU,CAAC,OAAO,CAAC,SAAS;AAChC,MAAM,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;AACjE,KAAK,CAAC;;AAEN,IAAI,OAAO,WAAY;AACvB;;AAEA,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE;AAC1B,IAAI,MAAM,WAAW,GAA+B,EAAE;AACtD;AACA,IAAI,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;;AAE/C,IAAI,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;;AAExC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;AACxB,MAAM,MAAM,GAAI,GAAE,UAAU,CAAC,GAAG,CAAC;AACjC,MAAM,IAAI,GAAI,KAAI,SAAS,EAAE;AAC7B,QAAQ,WAAW,CAAC,GAAG,CAAE,GAAE,kBAAkB,CAAC,GAAG,EAAE,cAAc,CAAC;AAClE;AACA,KAAK,CAAC;;AAEN,IAAI,OAAO,WAAY;AACvB;;AAEA;AACA,EAAE,OAAO,UAAU;AACnB;;AAEA,SAAS,MAAM,CAAC,KAAK,EAA6C;AAClE;AACA,EAAE,MAAM,WAAY,GAAE,CAAC,KAAM,GAAW,WAAW;AACnD,EAAE,OAAO,WAAY,KAAI,UAAU,WAAA,KAAgB,SAAS;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,GAAG,EAA0B;AACvD,EAAE,IAAI,WAAW;AACjB,EAAE,QAAQ,IAAI;AACd;AACA,IAAI,KAAK,GAAI,IAAG,SAAS;AACzB,MAAM,cAAc,IAAI,MAAM,CAAC,GAAG,CAAC;AACnC,MAAM;;AAEN;AACA;AACA;AACA,IAAI,KAAK,OAAO,GAAI,KAAI,YAAY,OAAO,GAAI,KAAI,QAAQ;AAC3D,MAAM,WAAY,GAAE,MAAM,CAAC,GAAG,CAAC;AAC/B,MAAM;;AAEN;AACA,IAAI,KAAKC,cAAW,CAAC,GAAG,CAAC;AACzB;AACA,MAAM,WAAA,GAAc,IAAI,CAAC,GAAA,GAAY,WAAW,CAAC,GAAG,CAAC;AACrD,MAAM;;AAEN;AACA,IAAI;AACJ,MAAM,WAAA,GAAc,GAAG;AACvB,MAAM;AACN;AACA,EAAE,OAAO,WAAW;AACpB;;;;;;;;;;;"}