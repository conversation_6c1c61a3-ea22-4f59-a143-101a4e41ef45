{"version": 3, "file": "api.js", "sources": ["../../src/api.ts"], "sourcesContent": ["import type { ReportDialogOptions } from './report-dialog';\nimport type { DsnCom<PERSON>, DsnLike } from './types-hoist/dsn';\nimport type { SdkInfo } from './types-hoist/sdkinfo';\nimport { dsnToString, makeDsn } from './utils/dsn';\n\nconst SENTRY_API_VERSION = '7';\n\n/** Returns the prefix to construct Sentry ingestion API endpoints. */\nfunction getBaseApiEndpoint(dsn: DsnComponents): string {\n  const protocol = dsn.protocol ? `${dsn.protocol}:` : '';\n  const port = dsn.port ? `:${dsn.port}` : '';\n  return `${protocol}//${dsn.host}${port}${dsn.path ? `/${dsn.path}` : ''}/api/`;\n}\n\n/** Returns the ingest API endpoint for target. */\nfunction _getIngestEndpoint(dsn: DsnComponents): string {\n  return `${getBaseApiEndpoint(dsn)}${dsn.projectId}/envelope/`;\n}\n\n/** Returns a URL-encoded string with auth config suitable for a query string. */\nfunction _encodedAuth(dsn: DsnComponents, sdkInfo: SdkInfo | undefined): string {\n  const params: Record<string, string> = {\n    sentry_version: SENTRY_API_VERSION,\n  };\n\n  if (dsn.publicKey) {\n    // We send only the minimum set of required information. See\n    // https://github.com/getsentry/sentry-javascript/issues/2572.\n    params.sentry_key = dsn.publicKey;\n  }\n\n  if (sdkInfo) {\n    params.sentry_client = `${sdkInfo.name}/${sdkInfo.version}`;\n  }\n\n  return new URLSearchParams(params).toString();\n}\n\n/**\n * Returns the envelope endpoint URL with auth in the query string.\n *\n * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n */\nexport function getEnvelopeEndpointWithUrlEncodedAuth(dsn: DsnComponents, tunnel?: string, sdkInfo?: SdkInfo): string {\n  return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, sdkInfo)}`;\n}\n\n/** Returns the url to the report dialog endpoint. */\nexport function getReportDialogEndpoint(dsnLike: DsnLike, dialogOptions: ReportDialogOptions): string {\n  const dsn = makeDsn(dsnLike);\n  if (!dsn) {\n    return '';\n  }\n\n  const endpoint = `${getBaseApiEndpoint(dsn)}embed/error-page/`;\n\n  let encodedOptions = `dsn=${dsnToString(dsn)}`;\n  for (const key in dialogOptions) {\n    if (key === 'dsn') {\n      continue;\n    }\n\n    if (key === 'onClose') {\n      continue;\n    }\n\n    if (key === 'user') {\n      const user = dialogOptions.user;\n      if (!user) {\n        continue;\n      }\n      if (user.name) {\n        encodedOptions += `&name=${encodeURIComponent(user.name)}`;\n      }\n      if (user.email) {\n        encodedOptions += `&email=${encodeURIComponent(user.email)}`;\n      }\n    } else {\n      encodedOptions += `&${encodeURIComponent(key)}=${encodeURIComponent(dialogOptions[key] as string)}`;\n    }\n  }\n\n  return `${endpoint}?${encodedOptions}`;\n}\n"], "names": ["dsn", "makeDsn", "dsnToString"], "mappings": ";;;;AAKA,MAAM,kBAAA,GAAqB,GAAG;;AAE9B;AACA,SAAS,kBAAkB,CAAC,GAAG,EAAyB;AACxD,EAAE,MAAM,WAAW,GAAG,CAAC,QAAS,GAAE,CAAC,EAAA,GAAA,CAAA,QAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAA,GAAA,CAAA,IAAA,GAAA,CAAA,CAAA,EAAA,GAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA;AACA,EAAA,OAAA,CAAA,EAAA,QAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,EAAA,IAAA,CAAA,EAAA,GAAA,CAAA,IAAA,GAAA,CAAA,CAAA,EAAA,GAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AACA;;AAEA;AACA,SAAA,kBAAA,CAAA,GAAA,EAAA;AACA,EAAA,OAAA,CAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,EAAA,GAAA,CAAA,SAAA,CAAA,UAAA,CAAA;AACA;;AAEA;AACA,SAAA,YAAA,CAAA,GAAA,EAAA,OAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAA;AACA,IAAA,cAAA,EAAA,kBAAA;AACA,GAAA;;AAEA,EAAA,IAAA,GAAA,CAAA,SAAA,EAAA;AACA;AACA;AACA,IAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,SAAA;AACA;;AAEA,EAAA,IAAA,OAAA,EAAA;AACA,IAAA,MAAA,CAAA,aAAA,GAAA,CAAA,EAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACA;;AAEA,EAAA,OAAA,IAAA,eAAA,CAAA,MAAA,CAAA,CAAA,QAAA,EAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAA,qCAAA,CAAA,GAAA,EAAA,MAAA,EAAA,OAAA,EAAA;AACA,EAAA,OAAA,MAAA,GAAA,MAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,YAAA,CAAA,GAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA;;AAEA;AACA,SAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA;AACA,EAAA,MAAAA,KAAA,GAAAC,WAAA,CAAA,OAAA,CAAA;AACA,EAAA,IAAA,CAAAD,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA;;AAEA,EAAA,MAAA,QAAA,GAAA,CAAA,EAAA,kBAAA,CAAAA,KAAA,CAAA,CAAA,iBAAA,CAAA;;AAEA,EAAA,IAAA,cAAA,GAAA,CAAA,IAAA,EAAAE,eAAA,CAAAF,KAAA,CAAA,CAAA,CAAA;AACA,EAAA,KAAA,MAAA,GAAA,IAAA,aAAA,EAAA;AACA,IAAA,IAAA,GAAA,KAAA,KAAA,EAAA;AACA,MAAA;AACA;;AAEA,IAAA,IAAA,GAAA,KAAA,SAAA,EAAA;AACA,MAAA;AACA;;AAEA,IAAA,IAAA,GAAA,KAAA,MAAA,EAAA;AACA,MAAA,MAAA,IAAA,GAAA,aAAA,CAAA,IAAA;AACA,MAAA,IAAA,CAAA,IAAA,EAAA;AACA,QAAA;AACA;AACA,MAAA,IAAA,IAAA,CAAA,IAAA,EAAA;AACA,QAAA,cAAA,IAAA,CAAA,MAAA,EAAA,kBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;AACA,MAAA,IAAA,IAAA,CAAA,KAAA,EAAA;AACA,QAAA,cAAA,IAAA,CAAA,OAAA,EAAA,kBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;AACA,KAAA,MAAA;AACA,MAAA,cAAA,IAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,EAAA,cAAA,CAAA,CAAA;AACA;;;;;"}