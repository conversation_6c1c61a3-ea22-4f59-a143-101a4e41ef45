{"version": 3, "file": "baggage.js", "sources": ["../../../src/utils/baggage.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport { isString } from './is';\nimport { logger } from './logger';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX = 'sentry-';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX_REGEX = /^sentry-/;\n\n/**\n * Max length of a serialized baggage string\n *\n * https://www.w3.org/TR/baggage/#limits\n */\nexport const MAX_BAGGAGE_STRING_LENGTH = 8192;\n\n/**\n * Takes a baggage header and turns it into Dynamic Sampling Context, by extracting all the \"sentry-\" prefixed values\n * from it.\n *\n * @param baggageHeader A very bread definition of a baggage header as it might appear in various frameworks.\n * @returns The Dynamic Sampling Context that was found on `baggageHeader`, if there was any, `undefined` otherwise.\n */\nexport function baggageHeaderToDynamicSamplingContext(\n  // Very liberal definition of what any incoming header might look like\n  baggageHeader: string | string[] | number | null | undefined | boolean,\n): Partial<DynamicSamplingContext> | undefined {\n  const baggageObject = parseBaggageHeader(baggageHeader);\n\n  if (!baggageObject) {\n    return undefined;\n  }\n\n  // Read all \"sentry-\" prefixed values out of the baggage object and put it onto a dynamic sampling context object.\n  const dynamicSamplingContext = Object.entries(baggageObject).reduce<Record<string, string>>((acc, [key, value]) => {\n    if (key.match(SENTRY_BAGGAGE_KEY_PREFIX_REGEX)) {\n      const nonPrefixedKey = key.slice(SENTRY_BAGGAGE_KEY_PREFIX.length);\n      acc[nonPrefixedKey] = value;\n    }\n    return acc;\n  }, {});\n\n  // Only return a dynamic sampling context object if there are keys in it.\n  // A keyless object means there were no sentry values on the header, which means that there is no DSC.\n  if (Object.keys(dynamicSamplingContext).length > 0) {\n    return dynamicSamplingContext as Partial<DynamicSamplingContext>;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Turns a Dynamic Sampling Object into a baggage header by prefixing all the keys on the object with \"sentry-\".\n *\n * @param dynamicSamplingContext The Dynamic Sampling Context to turn into a header. For convenience and compatibility\n * with the `getDynamicSamplingContext` method on the Transaction class ,this argument can also be `undefined`. If it is\n * `undefined` the function will return `undefined`.\n * @returns a baggage header, created from `dynamicSamplingContext`, or `undefined` either if `dynamicSamplingContext`\n * was `undefined`, or if `dynamicSamplingContext` didn't contain any values.\n */\nexport function dynamicSamplingContextToSentryBaggageHeader(\n  // this also takes undefined for convenience and bundle size in other places\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>,\n): string | undefined {\n  if (!dynamicSamplingContext) {\n    return undefined;\n  }\n\n  // Prefix all DSC keys with \"sentry-\" and put them into a new object\n  const sentryPrefixedDSC = Object.entries(dynamicSamplingContext).reduce<Record<string, string>>(\n    (acc, [dscKey, dscValue]) => {\n      if (dscValue) {\n        acc[`${SENTRY_BAGGAGE_KEY_PREFIX}${dscKey}`] = dscValue;\n      }\n      return acc;\n    },\n    {},\n  );\n\n  return objectToBaggageHeader(sentryPrefixedDSC);\n}\n\n/**\n * Take a baggage header and parse it into an object.\n */\nexport function parseBaggageHeader(\n  baggageHeader: string | string[] | number | null | undefined | boolean,\n): Record<string, string> | undefined {\n  if (!baggageHeader || (!isString(baggageHeader) && !Array.isArray(baggageHeader))) {\n    return undefined;\n  }\n\n  if (Array.isArray(baggageHeader)) {\n    // Combine all baggage headers into one object containing the baggage values so we can later read the Sentry-DSC-values from it\n    return baggageHeader.reduce<Record<string, string>>((acc, curr) => {\n      const currBaggageObject = baggageHeaderToObject(curr);\n      Object.entries(currBaggageObject).forEach(([key, value]) => {\n        acc[key] = value;\n      });\n      return acc;\n    }, {});\n  }\n\n  return baggageHeaderToObject(baggageHeader);\n}\n\n/**\n * Will parse a baggage header, which is a simple key-value map, into a flat object.\n *\n * @param baggageHeader The baggage header to parse.\n * @returns a flat object containing all the key-value pairs from `baggageHeader`.\n */\nfunction baggageHeaderToObject(baggageHeader: string): Record<string, string> {\n  return baggageHeader\n    .split(',')\n    .map(baggageEntry =>\n      baggageEntry.split('=').map(keyOrValue => {\n        try {\n          return decodeURIComponent(keyOrValue.trim());\n        } catch {\n          // We ignore errors here, e.g. if the value cannot be URL decoded.\n          // This will then be skipped in the next step\n          return;\n        }\n      }),\n    )\n    .reduce<Record<string, string>>((acc, [key, value]) => {\n      if (key && value) {\n        acc[key] = value;\n      }\n      return acc;\n    }, {});\n}\n\n/**\n * Turns a flat object (key-value pairs) into a baggage header, which is also just key-value pairs.\n *\n * @param object The object to turn into a baggage header.\n * @returns a baggage header string, or `undefined` if the object didn't have any values, since an empty baggage header\n * is not spec compliant.\n */\nexport function objectToBaggageHeader(object: Record<string, string>): string | undefined {\n  if (Object.keys(object).length === 0) {\n    // An empty baggage header is not spec compliant: We return undefined.\n    return undefined;\n  }\n\n  return Object.entries(object).reduce((baggageHeader, [objectKey, objectValue], currentIndex) => {\n    const baggageEntry = `${encodeURIComponent(objectKey)}=${encodeURIComponent(objectValue)}`;\n    const newBaggageHeader = currentIndex === 0 ? baggageEntry : `${baggageHeader},${baggageEntry}`;\n    if (newBaggageHeader.length > MAX_BAGGAGE_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Not adding key: ${objectKey} with val: ${objectValue} to baggage header due to exceeding baggage size limits.`,\n        );\n      return baggageHeader;\n    } else {\n      return newBaggageHeader;\n    }\n  }, '');\n}\n"], "names": [], "mappings": ";;;;AAKO,MAAM,yBAA0B,GAAE;;AAElC,MAAM,+BAAgC,GAAE;;AAE/C;AACA;AACA;AACA;AACA;AACO,MAAM,yBAA0B,GAAE;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qCAAqC;AACrD;AACA,EAAE,aAAa;AACf,EAA+C;AAC/C,EAAE,MAAM,aAAc,GAAE,kBAAkB,CAAC,aAAa,CAAC;;AAEzD,EAAE,IAAI,CAAC,aAAa,EAAE;AACtB,IAAI,OAAO,SAAS;AACpB;;AAEA;AACA,EAAE,MAAM,yBAAyB,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAAyB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;AACrH,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE;AACpD,MAAM,MAAM,cAAe,GAAE,GAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC;AACxE,MAAM,GAAG,CAAC,cAAc,CAAA,GAAI,KAAK;AACjC;AACA,IAAI,OAAO,GAAG;AACd,GAAG,EAAE,EAAE,CAAC;;AAER;AACA;AACA,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,MAAA,GAAS,CAAC,EAAE;AACtD,IAAI,OAAO,sBAAuB;AAClC,SAAS;AACT,IAAI,OAAO,SAAS;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2CAA2C;AAC3D;AACA,EAAE,sBAAsB;AACxB,EAAsB;AACtB,EAAE,IAAI,CAAC,sBAAsB,EAAE;AAC/B,IAAI,OAAO,SAAS;AACpB;;AAEA;AACA,EAAE,MAAM,iBAAkB,GAAE,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,MAAM;AACzE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK;AACjC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,GAAG,CAAC,CAAC,EAAA,yBAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,GAAA,QAAA;AACA;AACA,MAAA,OAAA,GAAA;AACA,KAAA;AACA,IAAA,EAAA;AACA,GAAA;;AAEA,EAAA,OAAA,qBAAA,CAAA,iBAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,SAAA,kBAAA;AACA,EAAA,aAAA;AACA,EAAA;AACA,EAAA,IAAA,CAAA,aAAA,KAAA,CAAA,QAAA,CAAA,aAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,aAAA,CAAA,CAAA,EAAA;AACA,IAAA,OAAA,SAAA;AACA;;AAEA,EAAA,IAAA,KAAA,CAAA,OAAA,CAAA,aAAA,CAAA,EAAA;AACA;AACA,IAAA,OAAA,aAAA,CAAA,MAAA,CAAA,CAAA,GAAA,EAAA,IAAA,KAAA;AACA,MAAA,MAAA,iBAAA,GAAA,qBAAA,CAAA,IAAA,CAAA;AACA,MAAA,MAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,EAAA,KAAA,CAAA,KAAA;AACA,QAAA,GAAA,CAAA,GAAA,CAAA,GAAA,KAAA;AACA,OAAA,CAAA;AACA,MAAA,OAAA,GAAA;AACA,KAAA,EAAA,EAAA,CAAA;AACA;;AAEA,EAAA,OAAA,qBAAA,CAAA,aAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,aAAA,EAAA;AACA,EAAA,OAAA;AACA,KAAA,KAAA,CAAA,GAAA;AACA,KAAA,GAAA,CAAA,YAAA;AACA,MAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AACA,QAAA,IAAA;AACA,UAAA,OAAA,kBAAA,CAAA,UAAA,CAAA,IAAA,EAAA,CAAA;AACA,SAAA,CAAA,MAAA;AACA;AACA;AACA,UAAA;AACA;AACA,OAAA,CAAA;AACA;AACA,KAAA,MAAA,CAAA,CAAA,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,CAAA,KAAA;AACA,MAAA,IAAA,GAAA,IAAA,KAAA,EAAA;AACA,QAAA,GAAA,CAAA,GAAA,CAAA,GAAA,KAAA;AACA;AACA,MAAA,OAAA,GAAA;AACA,KAAA,EAAA,EAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,MAAA,EAAA;AACA,EAAA,IAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA;AACA,IAAA,OAAA,SAAA;AACA;;AAEA,EAAA,OAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,CAAA,aAAA,EAAA,CAAA,SAAA,EAAA,WAAA,CAAA,EAAA,YAAA,KAAA;AACA,IAAA,MAAA,YAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,gBAAA,GAAA,YAAA,KAAA,CAAA,GAAA,YAAA,GAAA,CAAA,EAAA,aAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA;AACA,IAAA,IAAA,gBAAA,CAAA,MAAA,GAAA,yBAAA,EAAA;AACA,MAAA,WAAA;AACA,QAAA,MAAA,CAAA,IAAA;AACA,UAAA,CAAA,gBAAA,EAAA,SAAA,CAAA,WAAA,EAAA,WAAA,CAAA,wDAAA,CAAA;AACA,SAAA;AACA,MAAA,OAAA,aAAA;AACA,KAAA,MAAA;AACA,MAAA,OAAA,gBAAA;AACA;AACA,GAAA,EAAA,EAAA,CAAA;AACA;;;;"}