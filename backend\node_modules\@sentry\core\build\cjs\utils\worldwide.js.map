{"version": 3, "file": "worldwide.js", "sources": ["../../../src/utils/worldwide.ts"], "sourcesContent": ["/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n *\n * Note: This file was originally called `global.ts`, but was changed to unblock users which might be doing\n * string replaces with bundlers like Vite for `global` (would break imports that rely on importing from utils/src/global).\n *\n * Why worldwide?\n *\n * Why not?\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Carrier } from '../carrier';\nimport type { Client } from '../client';\nimport type { SerializedLog } from '../types-hoist/log';\nimport type { Span } from '../types-hoist/span';\nimport type { SdkSource } from './env';\n\n/** Internal global with common properties and Sentry extensions  */\nexport type InternalGlobal = {\n  navigator?: { userAgent?: string; maxTouchPoints?: number };\n  console: Console;\n  PerformanceObserver?: any;\n  Sentry?: any;\n  onerror?: {\n    (event: object | string, source?: string, lineno?: number, colno?: number, error?: Error): any;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  onunhandledrejection?: {\n    (event: unknown): boolean;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  SENTRY_SDK_SOURCE?: SdkSource;\n  /**\n   * A map of Sentry clients to their log buffers.\n   *\n   * This is used to store logs that are sent to Sentry.\n   */\n  _sentryClientToLogBufferMap?: WeakMap<Client, Array<SerializedLog>>;\n  /**\n   * Debug IDs are indirectly injected by Sentry CLI or bundler plugins to directly reference a particular source map\n   * for resolving of a source file. The injected code will place an entry into the record for each loaded bundle/JS\n   * file.\n   */\n  _sentryDebugIds?: Record<string, string>;\n  /**\n   * Raw module metadata that is injected by bundler plugins.\n   *\n   * Keys are `error.stack` strings, values are the metadata.\n   */\n  _sentryModuleMetadata?: Record<string, any>;\n  _sentryEsmLoaderHookRegistered?: boolean;\n  /**\n   * A map of spans to evaluated feature flags. Populated by feature flag integrations.\n   */\n  _spanToFlagBufferMap?: WeakMap<Span, Set<string>>;\n} & Carrier;\n\n/** Get's the global object for the current JavaScript runtime */\nexport const GLOBAL_OBJ = globalThis as unknown as InternalGlobal;\n"], "names": [], "mappings": ";;AAoBA;;AA6CA;AACO,MAAM,UAAW,GAAE,UAAW;;;;"}