{"version": 3, "file": "integration.js", "sources": ["../../src/integration.ts"], "sourcesContent": ["import type { Client } from './client';\nimport { getClient } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { Integration, IntegrationFn } from './types-hoist/integration';\nimport type { Options } from './types-hoist/options';\nimport { logger } from './utils/logger';\n\nexport const installedIntegrations: string[] = [];\n\n/** Map of integrations assigned to a client */\nexport type IntegrationIndex = {\n  [key: string]: Integration;\n};\n\ntype IntegrationWithDefaultInstance = Integration & { isDefaultInstance?: true };\n\n/**\n * Remove duplicates from the given array, preferring the last instance of any duplicate. Not guaranteed to\n * preserve the order of integrations in the array.\n *\n * @private\n */\nfunction filterDuplicates(integrations: Integration[]): Integration[] {\n  const integrationsByName: { [key: string]: Integration } = {};\n\n  integrations.forEach((currentInstance: IntegrationWithDefaultInstance) => {\n    const { name } = currentInstance;\n\n    const existingInstance: IntegrationWithDefaultInstance | undefined = integrationsByName[name];\n\n    // We want integrations later in the array to overwrite earlier ones of the same type, except that we never want a\n    // default instance to overwrite an existing user instance\n    if (existingInstance && !existingInstance.isDefaultInstance && currentInstance.isDefaultInstance) {\n      return;\n    }\n\n    integrationsByName[name] = currentInstance;\n  });\n\n  return Object.values(integrationsByName);\n}\n\n/** Gets integrations to install */\nexport function getIntegrationsToSetup(options: Pick<Options, 'defaultIntegrations' | 'integrations'>): Integration[] {\n  const defaultIntegrations = options.defaultIntegrations || [];\n  const userIntegrations = options.integrations;\n\n  // We flag default instances, so that later we can tell them apart from any user-created instances of the same class\n  defaultIntegrations.forEach((integration: IntegrationWithDefaultInstance) => {\n    integration.isDefaultInstance = true;\n  });\n\n  let integrations: Integration[];\n\n  if (Array.isArray(userIntegrations)) {\n    integrations = [...defaultIntegrations, ...userIntegrations];\n  } else if (typeof userIntegrations === 'function') {\n    const resolvedUserIntegrations = userIntegrations(defaultIntegrations);\n    integrations = Array.isArray(resolvedUserIntegrations) ? resolvedUserIntegrations : [resolvedUserIntegrations];\n  } else {\n    integrations = defaultIntegrations;\n  }\n\n  return filterDuplicates(integrations);\n}\n\n/**\n * Given a list of integration instances this installs them all. When `withDefaults` is set to `true` then all default\n * integrations are added unless they were already provided before.\n * @param integrations array of integration instances\n * @param withDefault should enable default integrations\n */\nexport function setupIntegrations(client: Client, integrations: Integration[]): IntegrationIndex {\n  const integrationIndex: IntegrationIndex = {};\n\n  integrations.forEach((integration: Integration | undefined) => {\n    // guard against empty provided integrations\n    if (integration) {\n      setupIntegration(client, integration, integrationIndex);\n    }\n  });\n\n  return integrationIndex;\n}\n\n/**\n * Execute the `afterAllSetup` hooks of the given integrations.\n */\nexport function afterSetupIntegrations(client: Client, integrations: Integration[]): void {\n  for (const integration of integrations) {\n    // guard against empty provided integrations\n    if (integration?.afterAllSetup) {\n      integration.afterAllSetup(client);\n    }\n  }\n}\n\n/** Setup a single integration.  */\nexport function setupIntegration(client: Client, integration: Integration, integrationIndex: IntegrationIndex): void {\n  if (integrationIndex[integration.name]) {\n    DEBUG_BUILD && logger.log(`Integration skipped because it was already installed: ${integration.name}`);\n    return;\n  }\n  integrationIndex[integration.name] = integration;\n\n  // `setupOnce` is only called the first time\n  if (installedIntegrations.indexOf(integration.name) === -1 && typeof integration.setupOnce === 'function') {\n    integration.setupOnce();\n    installedIntegrations.push(integration.name);\n  }\n\n  // `setup` is run for each client\n  if (integration.setup && typeof integration.setup === 'function') {\n    integration.setup(client);\n  }\n\n  if (typeof integration.preprocessEvent === 'function') {\n    const callback = integration.preprocessEvent.bind(integration) as typeof integration.preprocessEvent;\n    client.on('preprocessEvent', (event, hint) => callback(event, hint, client));\n  }\n\n  if (typeof integration.processEvent === 'function') {\n    const callback = integration.processEvent.bind(integration) as typeof integration.processEvent;\n\n    const processor = Object.assign((event: Event, hint: EventHint) => callback(event, hint, client), {\n      id: integration.name,\n    });\n\n    client.addEventProcessor(processor);\n  }\n\n  DEBUG_BUILD && logger.log(`Integration installed: ${integration.name}`);\n}\n\n/** Add an integration to the current scope's client. */\nexport function addIntegration(integration: Integration): void {\n  const client = getClient();\n\n  if (!client) {\n    DEBUG_BUILD && logger.warn(`Cannot add integration \"${integration.name}\" because no SDK Client is available.`);\n    return;\n  }\n\n  client.addIntegration(integration);\n}\n\n/**\n * Define an integration function that can be used to create an integration instance.\n * Note that this by design hides the implementation details of the integration, as they are considered internal.\n */\nexport function defineIntegration<Fn extends IntegrationFn>(fn: Fn): (...args: Parameters<Fn>) => Integration {\n  return fn;\n}\n"], "names": ["DEBUG_BUILD", "logger", "getClient"], "mappings": ";;;;;;AAQa,MAAA,qBAAqB,GAAa;;AAE/C;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,YAAY,EAAgC;AACtE,EAAE,MAAM,kBAAkB,GAAmC,EAAE;;AAE/D,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,eAAe,KAAqC;AAC5E,IAAI,MAAM,EAAE,IAAK,EAAA,GAAI,eAAe;;AAEpC,IAAI,MAAM,gBAAgB,GAA+C,kBAAkB,CAAC,IAAI,CAAC;;AAEjG;AACA;AACA,IAAI,IAAI,gBAAiB,IAAG,CAAC,gBAAgB,CAAC,iBAAA,IAAqB,eAAe,CAAC,iBAAiB,EAAE;AACtG,MAAM;AACN;;AAEA,IAAI,kBAAkB,CAAC,IAAI,CAAA,GAAI,eAAe;AAC9C,GAAG,CAAC;;AAEJ,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;AAC1C;;AAEA;AACO,SAAS,sBAAsB,CAAC,OAAO,EAAwE;AACtH,EAAE,MAAM,sBAAsB,OAAO,CAAC,mBAAoB,IAAG,EAAE;AAC/D,EAAE,MAAM,gBAAA,GAAmB,OAAO,CAAC,YAAY;;AAE/C;AACA,EAAE,mBAAmB,CAAC,OAAO,CAAC,CAAC,WAAW,KAAqC;AAC/E,IAAI,WAAW,CAAC,iBAAkB,GAAE,IAAI;AACxC,GAAG,CAAC;;AAEJ,EAAE,IAAI,YAAY;;AAElB,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;AACvC,IAAI,YAAA,GAAe,CAAC,GAAG,mBAAmB,EAAE,GAAG,gBAAgB,CAAC;AAChE,GAAE,MAAO,IAAI,OAAO,gBAAiB,KAAI,UAAU,EAAE;AACrD,IAAI,MAAM,wBAAyB,GAAE,gBAAgB,CAAC,mBAAmB,CAAC;AAC1E,IAAI,YAAa,GAAE,KAAK,CAAC,OAAO,CAAC,wBAAwB,CAAA,GAAI,wBAAA,GAA2B,CAAC,wBAAwB,CAAC;AAClH,SAAS;AACT,IAAI,YAAA,GAAe,mBAAmB;AACtC;;AAEA,EAAE,OAAO,gBAAgB,CAAC,YAAY,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,MAAM,EAAU,YAAY,EAAmC;AACjG,EAAE,MAAM,gBAAgB,GAAqB,EAAE;;AAE/C,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,KAA8B;AACjE;AACA,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,gBAAgB,CAAC,MAAM,EAAE,WAAW,EAAE,gBAAgB,CAAC;AAC7D;AACA,GAAG,CAAC;;AAEJ,EAAE,OAAO,gBAAgB;AACzB;;AAEA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,MAAM,EAAU,YAAY,EAAuB;AAC1F,EAAE,KAAK,MAAM,WAAY,IAAG,YAAY,EAAE;AAC1C;AACA,IAAI,IAAI,WAAW,EAAE,aAAa,EAAE;AACpC,MAAM,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC;AACvC;AACA;AACA;;AAEA;AACO,SAAS,gBAAgB,CAAC,MAAM,EAAU,WAAW,EAAe,gBAAgB,EAA0B;AACrH,EAAE,IAAI,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AAC1C,IAAIA,sBAAY,IAAGC,aAAM,CAAC,GAAG,CAAC,CAAC,sDAAsD,EAAE,WAAW,CAAC,IAAI,CAAC,CAAA,CAAA;AACA,IAAA;AACA;AACA,EAAA,gBAAA,CAAA,WAAA,CAAA,IAAA,CAAA,GAAA,WAAA;;AAEA;AACA,EAAA,IAAA,qBAAA,CAAA,OAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,EAAA,IAAA,OAAA,WAAA,CAAA,SAAA,KAAA,UAAA,EAAA;AACA,IAAA,WAAA,CAAA,SAAA,EAAA;AACA,IAAA,qBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AACA;;AAEA;AACA,EAAA,IAAA,WAAA,CAAA,KAAA,IAAA,OAAA,WAAA,CAAA,KAAA,KAAA,UAAA,EAAA;AACA,IAAA,WAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACA;;AAEA,EAAA,IAAA,OAAA,WAAA,CAAA,eAAA,KAAA,UAAA,EAAA;AACA,IAAA,MAAA,QAAA,GAAA,WAAA,CAAA,eAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,IAAA,MAAA,CAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,KAAA,EAAA,IAAA,KAAA,QAAA,CAAA,KAAA,EAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AACA;;AAEA,EAAA,IAAA,OAAA,WAAA,CAAA,YAAA,KAAA,UAAA,EAAA;AACA,IAAA,MAAA,QAAA,GAAA,WAAA,CAAA,YAAA,CAAA,IAAA,CAAA,WAAA,CAAA;;AAEA,IAAA,MAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,CAAA,KAAA,EAAA,IAAA,KAAA,QAAA,CAAA,KAAA,EAAA,IAAA,EAAA,MAAA,CAAA,EAAA;AACA,MAAA,EAAA,EAAA,WAAA,CAAA,IAAA;AACA,KAAA,CAAA;;AAEA,IAAA,MAAA,CAAA,iBAAA,CAAA,SAAA,CAAA;AACA;;AAEA,EAAAD,sBAAA,IAAAC,aAAA,CAAA,GAAA,CAAA,CAAA,uBAAA,EAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;;AAEA;AACA,SAAA,cAAA,CAAA,WAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAAC,uBAAA,EAAA;;AAEA,EAAA,IAAA,CAAA,MAAA,EAAA;AACA,IAAAF,sBAAA,IAAAC,aAAA,CAAA,IAAA,CAAA,CAAA,wBAAA,EAAA,WAAA,CAAA,IAAA,CAAA,qCAAA,CAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,MAAA,CAAA,cAAA,CAAA,WAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,iBAAA,CAAA,EAAA,EAAA;AACA,EAAA,OAAA,EAAA;AACA;;;;;;;;;;"}