# Budget Tracker Error Codes

## Authentication Errors (4xx)
- `MISSING_FIELDS`: Required fields not provided
- `USER_EXISTS`: Email already registered
- `INVALID_CREDENTIALS`: Wrong email/password
- `INVALID_TOKEN`: Invalid/expired JWT
- `ACCESS_DENIED`: Insufficient permissions

## Server Errors (5xx)
- `USER_CREATION_FAILED`: Database error creating user
- `TOKEN_GENERATION_FAILED`: Error generating JWT
- `DATABASE_ERROR`: MongoDB operation failed
- `SERVER_ERROR`: Generic server error

## Validation Errors (4xx)
- `INVALID_EMAIL`: Malformed email address
- `WEAK_PASSWORD`: Password doesn't meet requirements
- `INVALID_DATE`: Invalid transaction date
