com.example.budget_tracker.app-jetified-credentials-1.5.0-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\006877986797b3b6be5cf579d190afc8\transformed\jetified-credentials-1.5.0\res
com.example.budget_tracker.app-browser-1.8.0-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\res
com.example.budget_tracker.app-jetified-lifecycle-process-2.7.0-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\res
com.example.budget_tracker.app-jetified-play-services-basement-18.4.0-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\res
com.example.budget_tracker.app-jetified-appcompat-resources-1.2.0-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\res
com.example.budget_tracker.app-jetified-window-1.2.0-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\res
com.example.budget_tracker.app-fragment-1.7.1-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\res
com.example.budget_tracker.app-core-runtime-2.2.0-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\res
com.example.budget_tracker.app-lifecycle-livedata-core-2.7.0-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\res
com.example.budget_tracker.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.budget_tracker.app-jetified-tracing-1.2.0-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\res
com.example.budget_tracker.app-coordinatorlayout-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\res
com.example.budget_tracker.app-jetified-firebase-common-21.0.0-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\res
com.example.budget_tracker.app-jetified-lifecycle-viewmodel-ktx-2.7.0-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.budget_tracker.app-jetified-datastore-core-release-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\res
com.example.budget_tracker.app-jetified-activity-ktx-1.9.3-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\32f4a2813ac7c771e056d42a720055af\transformed\jetified-activity-ktx-1.9.3\res
com.example.budget_tracker.app-jetified-play-services-auth-21.3.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\res
com.example.budget_tracker.app-jetified-savedstate-ktx-1.2.1-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.budget_tracker.app-jetified-core-1.0.0-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\res
com.example.budget_tracker.app-jetified-datastore-release-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\res
com.example.budget_tracker.app-core-1.15.0-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\res
com.example.budget_tracker.app-jetified-window-java-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\res
com.example.budget_tracker.app-jetified-core-common-2.0.3-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\5dd3740d4798cd744da95fbad85bd5d6\transformed\jetified-core-common-2.0.3\res
com.example.budget_tracker.app-jetified-annotation-experimental-1.4.1-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\res
com.example.budget_tracker.app-jetified-datastore-preferences-release-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\res
com.example.budget_tracker.app-recyclerview-1.0.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\res
com.example.budget_tracker.app-jetified-savedstate-1.2.1-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\res
com.example.budget_tracker.app-lifecycle-viewmodel-2.7.0-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\res
com.example.budget_tracker.app-jetified-startup-runtime-1.1.1-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\res
com.example.budget_tracker.app-jetified-activity-1.9.3-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\93eeca70efd8419049cd49df8af72af1\transformed\jetified-activity-1.9.3\res
com.example.budget_tracker.app-jetified-fragment-ktx-1.7.1-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\res
com.example.budget_tracker.app-appcompat-1.2.0-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\res
com.example.budget_tracker.app-jetified-profileinstaller-1.3.1-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\res
com.example.budget_tracker.app-lifecycle-runtime-2.7.0-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\res
com.example.budget_tracker.app-preference-1.2.1-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\res
com.example.budget_tracker.app-jetified-security-crypto-1.1.0-alpha06-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\b87823aa9ee377cf4f75094088b20314\transformed\jetified-security-crypto-1.1.0-alpha06\res
com.example.budget_tracker.app-jetified-credentials-play-services-auth-1.5.0-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\res
com.example.budget_tracker.app-jetified-lifecycle-runtime-ktx-2.7.0-37 C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.budget_tracker.app-jetified-core-ktx-1.15.0-38 C:\Users\<USER>\.gradle\caches\8.12\transforms\cc77f214097347dd090a046ace804f34\transformed\jetified-core-ktx-1.15.0\res
com.example.budget_tracker.app-biometric-1.1.0-39 C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\res
com.example.budget_tracker.app-jetified-lifecycle-livedata-core-ktx-2.7.0-40 C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.budget_tracker.app-jetified-play-services-base-18.5.0-41 C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\res
com.example.budget_tracker.app-slidingpanelayout-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\res
com.example.budget_tracker.app-media-1.1.0-43 C:\Users\<USER>\.gradle\caches\8.12\transforms\f84db7003533a22de0405c5251ecb704\transformed\media-1.1.0\res
com.example.budget_tracker.app-transition-1.4.1-44 C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\res
com.example.budget_tracker.app-debug-45 E:\BUDGET TRACKER\budget_tracker\android\app\src\debug\res
com.example.budget_tracker.app-main-46 E:\BUDGET TRACKER\budget_tracker\android\app\src\main\res
com.example.budget_tracker.app-pngs-47 E:\BUDGET TRACKER\budget_tracker\build\app\generated\res\pngs\debug
com.example.budget_tracker.app-resValues-48 E:\BUDGET TRACKER\budget_tracker\build\app\generated\res\resValues\debug
com.example.budget_tracker.app-packageDebugResources-49 E:\BUDGET TRACKER\budget_tracker\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.budget_tracker.app-packageDebugResources-50 E:\BUDGET TRACKER\budget_tracker\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.budget_tracker.app-debug-51 E:\BUDGET TRACKER\budget_tracker\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.budget_tracker.app-debug-52 E:\BUDGET TRACKER\budget_tracker\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-53 E:\BUDGET TRACKER\budget_tracker\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-54 E:\BUDGET TRACKER\budget_tracker\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-55 E:\BUDGET TRACKER\budget_tracker\build\flutter_local_notifications\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-56 E:\BUDGET TRACKER\budget_tracker\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-57 E:\BUDGET TRACKER\budget_tracker\build\flutter_secure_storage\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-58 E:\BUDGET TRACKER\budget_tracker\build\google_sign_in_android\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-59 E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-60 E:\BUDGET TRACKER\budget_tracker\build\integration_test\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-61 E:\BUDGET TRACKER\budget_tracker\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-62 E:\BUDGET TRACKER\budget_tracker\build\printing\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-63 E:\BUDGET TRACKER\budget_tracker\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-64 E:\BUDGET TRACKER\budget_tracker\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.example.budget_tracker.app-debug-65 E:\BUDGET TRACKER\budget_tracker\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
