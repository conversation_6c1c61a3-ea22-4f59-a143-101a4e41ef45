{"version": 3, "file": "checkin.js", "sources": ["../../src/checkin.ts"], "sourcesContent": ["import type { SerializedCheckIn } from './types-hoist/checkin';\nimport type { DsnComponents } from './types-hoist/dsn';\nimport type { CheckInEnvelope, CheckInItem, DynamicSamplingContext } from './types-hoist/envelope';\nimport type { SdkMetadata } from './types-hoist/sdkmetadata';\nimport { dsnToString } from './utils/dsn';\nimport { createEnvelope } from './utils/envelope';\n\n/**\n * Create envelope from check in item.\n */\nexport function createCheckInEnvelope(\n  checkIn: SerializedCheckIn,\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n  dsn?: DsnComponents,\n): CheckInEnvelope {\n  const headers: CheckInEnvelope[0] = {\n    sent_at: new Date().toISOString(),\n  };\n\n  if (metadata?.sdk) {\n    headers.sdk = {\n      name: metadata.sdk.name,\n      version: metadata.sdk.version,\n    };\n  }\n\n  if (!!tunnel && !!dsn) {\n    headers.dsn = dsnToString(dsn);\n  }\n\n  if (dynamicSamplingContext) {\n    headers.trace = dynamicSamplingContext as DynamicSamplingContext;\n  }\n\n  const item = createCheckInEnvelopeItem(checkIn);\n  return createEnvelope<CheckInEnvelope>(headers, [item]);\n}\n\nfunction createCheckInEnvelopeItem(checkIn: SerializedCheckIn): CheckInItem {\n  const checkInHeaders: CheckInItem[0] = {\n    type: 'check_in',\n  };\n  return [checkInHeaders, checkIn];\n}\n"], "names": [], "mappings": ";;;AAOA;AACA;AACA;AACO,SAAS,qBAAqB;AACrC,EAAE,OAAO;AACT,EAAE,sBAAsB;AACxB,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,GAAG;AACL,EAAmB;AACnB,EAAE,MAAM,OAAO,GAAuB;AACtC,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,GAAG;;AAEH,EAAE,IAAI,QAAQ,EAAE,GAAG,EAAE;AACrB,IAAI,OAAO,CAAC,GAAA,GAAM;AAClB,MAAM,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI;AAC7B,MAAM,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO;AACnC,KAAK;AACL;;AAEA,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE;AACzB,IAAI,OAAO,CAAC,GAAA,GAAM,WAAW,CAAC,GAAG,CAAC;AAClC;;AAEA,EAAE,IAAI,sBAAsB,EAAE;AAC9B,IAAI,OAAO,CAAC,KAAM,GAAE,sBAAuB;AAC3C;;AAEA,EAAE,MAAM,IAAK,GAAE,yBAAyB,CAAC,OAAO,CAAC;AACjD,EAAE,OAAO,cAAc,CAAkB,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;AACzD;;AAEA,SAAS,yBAAyB,CAAC,OAAO,EAAkC;AAC5E,EAAE,MAAM,cAAc,GAAmB;AACzC,IAAI,IAAI,EAAE,UAAU;AACpB,GAAG;AACH,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC;AAClC;;;;"}