{"version": 3, "file": "breadcrumb-log-level.js", "sources": ["../../../src/utils/breadcrumb-log-level.ts"], "sourcesContent": ["import type { SeverityLevel } from '../types-hoist/severity';\n\n/**\n * Determine a breadcrumb's log level (only `warning` or `error`) based on an HTTP status code.\n */\nexport function getBreadcrumbLogLevelFromHttpStatusCode(statusCode: number | undefined): SeverityLevel | undefined {\n  // NOTE: undefined defaults to 'info' in Sentry\n  if (statusCode === undefined) {\n    return undefined;\n  } else if (statusCode >= 400 && statusCode < 500) {\n    return 'warning';\n  } else if (statusCode >= 500) {\n    return 'error';\n  } else {\n    return undefined;\n  }\n}\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACO,SAAS,uCAAuC,CAAC,UAAU,EAAiD;AACnH;AACA,EAAE,IAAI,UAAW,KAAI,SAAS,EAAE;AAChC,IAAI,OAAO,SAAS;AACpB,GAAE,MAAO,IAAI,UAAA,IAAc,GAAA,IAAO,UAAA,GAAa,GAAG,EAAE;AACpD,IAAI,OAAO,SAAS;AACpB,SAAS,IAAI,UAAW,IAAG,GAAG,EAAE;AAChC,IAAI,OAAO,OAAO;AAClB,SAAS;AACT,IAAI,OAAO,SAAS;AACpB;AACA;;;;"}