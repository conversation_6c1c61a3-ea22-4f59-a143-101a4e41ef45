{"name": "budget-tracker-backend", "version": "1.0.0", "description": "Backend API for Budget Tracker mobile application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --verbose --detectOpenHandles --forceExit --runInBand --logHeapUsage --passWithNoTests", "test:watch": "jest --watch"}, "keywords": ["budget", "tracker", "finance", "mongodb", "express", "nodejs"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@sentry/node": "^9.34.0", "@sentry/tracing": "^7.120.3", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "redis": "^5.5.6", "uuid": "^11.1.0", "validator": "^13.15.15", "winston": "^3.17.0"}, "devDependencies": {"@types/jest": "^30.0.0", "jest": "^30.0.4", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}