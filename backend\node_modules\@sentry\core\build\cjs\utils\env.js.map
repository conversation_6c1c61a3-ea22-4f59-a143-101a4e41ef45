{"version": 3, "file": "env.js", "sources": ["../../../src/utils/env.ts"], "sourcesContent": ["/*\n * This module exists for optimizations in the build process through rollup and terser.  We define some global\n * constants, which can be overridden during build. By guarding certain pieces of code with functions that return these\n * constants, we can control whether or not they appear in the final bundle. (Any code guarded by a false condition will\n * never run, and will hence be dropped during treeshaking.) The two primary uses for this are stripping out calls to\n * `logger` and preventing node-related code from appearing in browser bundles.\n *\n * Attention:\n * This file should not be used to define constants/flags that are intended to be used for tree-shaking conducted by\n * users. These flags should live in their respective packages, as we identified user tooling (specifically webpack)\n * having issues tree-shaking these constants across package boundaries.\n * An example for this is the __SENTRY_DEBUG__ constant. It is declared in each package individually because we want\n * users to be able to shake away expressions that it guards.\n */\n\ndeclare const __SENTRY_BROWSER_BUNDLE__: boolean | undefined;\n\nexport type SdkSource = 'npm' | 'cdn' | 'loader';\n\n/**\n * Figures out if we're building a browser bundle.\n *\n * @returns true if this is a browser bundle build.\n */\nexport function isBrowserBundle(): boolean {\n  return typeof __SENTRY_BROWSER_BUNDLE__ !== 'undefined' && !!__SENTRY_BROWSER_BUNDLE__;\n}\n\n/**\n * Get source of SDK.\n */\nexport function getSDKSource(): SdkSource {\n  // This comment is used to identify this line in the CDN bundle build step and replace this with \"return 'cdn';\"\n  /* __SENTRY_SDK_SOURCE__ */ return 'npm';\n}\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,GAAY;AAC3C,EAAE,OAAO,OAAO,yBAA0B,KAAI,eAAe,CAAC,CAAC,yBAAyB;AACxF;;AAEA;AACA;AACA;AACO,SAAS,YAAY,GAAc;AAC1C;AACA,8BAA8B,OAAO,KAAK;AAC1C;;;;;"}