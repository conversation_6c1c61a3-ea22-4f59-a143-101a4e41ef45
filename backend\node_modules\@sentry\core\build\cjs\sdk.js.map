{"version": 3, "file": "sdk.js", "sources": ["../../src/sdk.ts"], "sourcesContent": ["import type { Client } from './client';\nimport { getCurrentScope } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { ClientOptions } from './types-hoist/options';\nimport { consoleSandbox, logger } from './utils/logger';\n\n/** A class object that can instantiate Client objects. */\nexport type ClientClass<F extends Client, O extends ClientOptions> = new (options: O) => F;\n\n/**\n * Internal function to create a new SDK client instance. The client is\n * installed and then bound to the current scope.\n *\n * @param clientClass The client class to instantiate.\n * @param options Options to pass to the client.\n */\nexport function initAndBind<F extends Client, O extends ClientOptions>(\n  clientClass: ClientClass<F, O>,\n  options: O,\n): Client {\n  if (options.debug === true) {\n    if (DEBUG_BUILD) {\n      logger.enable();\n    } else {\n      // use `console.warn` rather than `logger.warn` since by non-debug bundles have all `logger.x` statements stripped\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.warn('[<PERSON><PERSON>] Cannot initialize SDK with `debug` option using a non-debug bundle.');\n      });\n    }\n  }\n  const scope = getCurrentScope();\n  scope.update(options.initialScope);\n\n  const client = new clientClass(options);\n  setCurrentClient(client);\n  client.init();\n  return client;\n}\n\n/**\n * Make the given client the current client.\n */\nexport function setCurrentClient(client: Client): void {\n  getCurrentScope().setClient(client);\n}\n"], "names": ["DEBUG_BUILD", "logger", "consoleSandbox", "getCurrentScope"], "mappings": ";;;;;;AAMA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW;AAC3B,EAAE,WAAW;AACb,EAAE,OAAO;AACT,EAAU;AACV,EAAE,IAAI,OAAO,CAAC,KAAM,KAAI,IAAI,EAAE;AAC9B,IAAI,IAAIA,sBAAW,EAAE;AACrB,MAAMC,aAAM,CAAC,MAAM,EAAE;AACrB,WAAW;AACX;AACA,MAAMC,qBAAc,CAAC,MAAM;AAC3B;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC;AACpG,OAAO,CAAC;AACR;AACA;AACA,EAAE,MAAM,KAAA,GAAQC,6BAAe,EAAE;AACjC,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;;AAEpC,EAAE,MAAM,MAAO,GAAE,IAAI,WAAW,CAAC,OAAO,CAAC;AACzC,EAAE,gBAAgB,CAAC,MAAM,CAAC;AAC1B,EAAE,MAAM,CAAC,IAAI,EAAE;AACf,EAAE,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,MAAM,EAAgB;AACvD,EAAEA,6BAAe,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;AACrC;;;;;"}