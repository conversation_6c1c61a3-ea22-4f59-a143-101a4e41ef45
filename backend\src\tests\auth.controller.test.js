const fs = require('fs');
const mongoose = require('mongoose');
const { registerUser, loginUser, refreshToken } = require('../controllers/auth.controller');
const User = require('../models/user.model');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const logFile = './test-debug.log';

function log(message) {
  fs.appendFileSync(logFile, `${new Date().toISOString()} - AUTH TEST: ${message}\n`);
}

// Mock mongoose models
jest.mock('../models/user.model');

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  genSalt: jest.fn().mockResolvedValue('mockSalt'),
  hash: jest.fn().mockResolvedValue('hashedPassword'),
  compare: jest.fn().mockResolvedValue(true)
}));

// Mock jwt
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockImplementation((payload, secret) => {
    if (secret === process.env.JWT_SECRET) {
      return 'mockAccessToken';
    } else if (secret === process.env.REFRESH_TOKEN_SECRET) {
      return 'mockRefreshToken';
    }
    throw new Error('Invalid secret');
  }),
  verify: jest.fn().mockImplementation((token, secret) => {
    if (token === 'mockRefreshToken' && secret === process.env.REFRESH_TOKEN_SECRET) {
      return { userId: 'mockUserId' };
    }
    throw new Error('Invalid token');
  })
}));

// Mock request and response objects
const mockRequest = (body) => ({
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockImplementation((status) => {
    console.log(`Response status: ${status}`);
    return res;
  });
  res.json = jest.fn().mockImplementation((body) => {
    console.log('Response body:', JSON.stringify(body, null, 2));
    return res;
  });
  return res;
};

log('Starting auth controller test suite');

describe('Auth Controller', () => {
  beforeAll(async () => {
    // Database connection is handled by global setup
    // Just ensure we're in test mode
    log('Test environment ready');
    log(`Connection state: ${mongoose.connection.readyState}`);
  });

  afterAll(async () => {
    try {
      log('Disconnecting from MongoDB');
      await mongoose.disconnect();
      log('Disconnected successfully');
    } catch (err) {
      log(`Disconnection error: ${err}`);
      throw err;
    }
  });

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup mock user data - default to no user found for registration tests
    User.findOne.mockImplementation(({ email }) => {
      // For registration: return promise directly (no chaining)
      const mockUser = null;

      // Create a mock that supports both direct resolution and chaining
      const mockQuery = {
        select: jest.fn().mockResolvedValue(mockUser)
      };

      // Make the mock query thenable (promise-like) for direct await
      mockQuery.then = (resolve, reject) => {
        return Promise.resolve(mockUser).then(resolve, reject);
      };

      return mockQuery;
    });

    User.create.mockResolvedValue({
      _id: 'mockUserId',
      name: 'Test User',
      email: '<EMAIL>',
      password: 'hashedPassword',
      refreshToken: '',
      save: jest.fn().mockResolvedValue(true)
    });
  });

  describe('registerUser', () => {
    it('should register a new user', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing registerUser with valid data');

      // Mock successful user creation
      User.create.mockResolvedValueOnce({
        _id: 'mockUserId',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedPassword',
        refreshToken: '',
        save: jest.fn().mockResolvedValue(true)
      });

      // Mock token generation
      jwt.sign.mockImplementation((payload, secret) => {
        if (secret === process.env.JWT_SECRET) return 'mockAccessToken';
        if (secret === process.env.REFRESH_TOKEN_SECRET) return 'mockRefreshToken';
        throw new Error('Invalid secret');
      });

      const req = mockRequest({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!'
      });
      const res = mockResponse();

      await registerUser(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            _id: 'mockUserId',
            name: 'Test User',
            email: '<EMAIL>',
            accessToken: 'mockAccessToken',
            refreshToken: 'mockRefreshToken'
          })
        })
      );
      log('registerUser test with valid data passed');
    });

    it('should reject registration with missing fields', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing registerUser with missing fields');
      const req = mockRequest({
        name: 'Test User',
        // Missing email and password
      });
      const res = mockResponse();

      await registerUser(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Please provide name, email, and password'
        })
      );
      log('registerUser test with missing fields passed');
    });

    it('should reject duplicate email registration', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing registerUser with duplicate email');
      // First registration
      await registerUser(
        mockRequest({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'Password123!'
        }),
        mockResponse()
      );

      // Second registration with same email
      const req = mockRequest({
        name: 'Another User',
        email: '<EMAIL>',
        password: 'password456'
      });
      const res = mockResponse();

      await registerUser(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'User already exists'
        })
      );
      log('registerUser test with duplicate email passed');
    });
  });

  describe('registerUser edge cases', () => {
    it('should reject extremely long names', async () => {
      const req = mockRequest({
        name: 'A'.repeat(256), // Exceeds reasonable length
        email: '<EMAIL>',
        password: 'Password123!'
      });
      const res = mockResponse();

      await registerUser(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('validation failed')
        })
      );
    });

    it('should reject invalid email formats', async () => {
      const req = mockRequest({
        name: 'Test User',
        email: 'not-an-email',
        password: 'Password123!'
      });
      const res = mockResponse();

      await registerUser(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: expect.stringContaining('valid email')
        })
      );
    });
  });

  describe('Input Validation', () => {
    it('should reject empty name', async () => {
      const req = mockRequest({ email: '<EMAIL>', password: 'password' });
      const res = mockResponse();
      await registerUser(req, res);
      expect(res.status).toHaveBeenCalledWith(400);
    });

    it('should reject invalid email formats', async () => {
      const invalidEmails = ['plainstring', 'missing@dot', '@missingusername.com'];
      
      for (const email of invalidEmails) {
        const req = mockRequest({ name: 'Test', email, password: 'password' });
        const res = mockResponse();
        await registerUser(req, res);
        expect(res.status).toHaveBeenCalledWith(400);
      }
    });

    it('should reject short passwords', async () => {
      const req = mockRequest({ name: 'Test', email: '<EMAIL>', password: '123' });
      const res = mockResponse();
      await registerUser(req, res);
      expect(res.status).toHaveBeenCalledWith(400);
    });
  });

  describe('loginUser', () => {
    beforeEach(async () => {
      log('Registering test user for loginUser tests');
      // Register a test user
      await registerUser(
        mockRequest({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'Password123!'
        }),
        mockResponse()
      );
    });

    it('should login with valid credentials', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing loginUser with valid credentials');
      const req = mockRequest({
        email: '<EMAIL>',
        password: 'Password123!'
      });
      const res = mockResponse();

      await loginUser(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            _id: expect.any(String),
            name: 'Test User',
            email: '<EMAIL>',
            accessToken: expect.any(String),
            refreshToken: expect.any(String)
          })
        })
      );
      log('loginUser test with valid credentials passed');
    });

    it('should reject login with invalid password', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing loginUser with invalid password');
      const req = mockRequest({
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      const res = mockResponse();

      await loginUser(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Invalid credentials'
        })
      );
      log('loginUser test with invalid password passed');
    });

    it('should reject login with non-existent email', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing loginUser with non-existent email');
      const req = mockRequest({
        email: '<EMAIL>',
        password: 'Password123!'
      });
      const res = mockResponse();

      await loginUser(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Invalid credentials'
        })
      );
      log('loginUser test with non-existent email passed');
    });
  });

  describe('loginUser error scenarios', () => {
    it('should handle database errors during login', async () => {
      User.findOne.mockRejectedValue(new Error('Database error'));
      
      const req = mockRequest({
        email: '<EMAIL>',
        password: 'Password123!'
      });
      const res = mockResponse();

      await loginUser(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Internal server error'
        })
      );
    });
  });

  describe('refreshToken', () => {
    let refreshTokenValue;
    
    beforeEach(async () => {
      log('Registering and logging in test user for refreshToken tests');
      // Register and login a test user to get tokens
      const registerReq = mockRequest({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!'
      });
      const registerRes = mockResponse();
      await registerUser(registerReq, registerRes);

      const loginReq = mockRequest({
        email: '<EMAIL>',
        password: 'Password123!'
      });
      const loginRes = mockResponse();
      await loginUser(loginReq, loginRes);
      
      refreshTokenValue = loginRes.json.mock.calls[0][0].data.refreshToken;
    });
    
    it('should refresh tokens with valid refresh token', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing refreshToken with valid token');
      const req = mockRequest({
        refreshToken: refreshTokenValue
      });
      const res = mockResponse();
      
      await refreshToken(req, res);
      
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            _id: expect.any(String),
            name: 'Test User',
            email: '<EMAIL>',
            accessToken: expect.any(String),
            refreshToken: expect.any(String)
          })
        })
      );
      log('refreshToken test with valid token passed');
    });
    
    it('should reject refresh with invalid token', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing refreshToken with invalid token');
      const req = mockRequest({
        refreshToken: 'invalid.token.here'
      });
      const res = mockResponse();
      
      await refreshToken(req, res);
      
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Invalid refresh token'
        })
      );
      log('refreshToken test with invalid token passed');
    });
    
    it('should reject refresh with missing token', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing refreshToken with missing token');
      const req = mockRequest({});
      const res = mockResponse();
      
      await refreshToken(req, res);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Refresh token required'
        })
      );
      log('refreshToken test with missing token passed');
    });
    
    it('should rotate refresh tokens', async () => {
      console.log('Starting test:', expect.getState().currentTestName);
      log('Testing refreshToken rotation');
      const req1 = mockRequest({
        refreshToken: refreshTokenValue
      });
      const res1 = mockResponse();
      await refreshToken(req1, res1);
      
      const newRefreshToken = res1.json.mock.calls[0][0].data.refreshToken;
      
      // Try to reuse old refresh token
      const req2 = mockRequest({
        refreshToken: refreshTokenValue
      });
      const res2 = mockResponse();
      await refreshToken(req2, res2);
      
      expect(res2.status).toHaveBeenCalledWith(403);
      
      // Verify new refresh token works
      const req3 = mockRequest({
        refreshToken: newRefreshToken
      });
      const res3 = mockResponse();
      await refreshToken(req3, res3);
      
      expect(res3.status).toHaveBeenCalledWith(200);
      log('refreshToken rotation test passed');
    });
  });

  describe('refreshToken edge cases', () => {
    it('should handle expired refresh tokens', async () => {
      jwt.verify.mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired', new Date());
      });
      
      const req = mockRequest({
        refreshToken: 'expired.token.here'
      });
      const res = mockResponse();

      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Invalid refresh token'
        })
      );
    });

    it('should handle missing user during refresh', async () => {
      User.findOne.mockResolvedValue(null);
      
      const req = mockRequest({
        refreshToken: 'valid.but.no.user'
      });
      const res = mockResponse();

      await refreshToken(req, res);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Invalid refresh token'
        })
      );
    });
  });
});
