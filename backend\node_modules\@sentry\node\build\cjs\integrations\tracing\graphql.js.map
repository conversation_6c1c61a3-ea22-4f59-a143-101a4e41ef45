{"version": 3, "file": "graphql.js", "sources": ["../../../../src/integrations/tracing/graphql.ts"], "sourcesContent": ["import type { AttributeValue } from '@opentelemetry/api';\nimport { SpanStatusCode } from '@opentelemetry/api';\nimport { GraphQLInstrumentation } from '@opentelemetry/instrumentation-graphql';\nimport type { IntegrationFn } from '@sentry/core';\nimport { defineIntegration, getRootSpan, spanToJSON } from '@sentry/core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION } from '@sentry/opentelemetry';\nimport { generateInstrumentOnce } from '../../otel/instrument';\nimport { addOriginToSpan } from '../../utils/addOriginToSpan';\n\ninterface GraphqlOptions {\n  /**\n   * Do not create spans for resolvers.\n   *\n   * Defaults to true.\n   */\n  ignoreResolveSpans?: boolean;\n\n  /**\n   * Don't create spans for the execution of the default resolver on object properties.\n   *\n   * When a resolver function is not defined on the schema for a field, graphql will\n   * use the default resolver which just looks for a property with that name on the object.\n   * If the property is not a function, it's not very interesting to trace.\n   * This option can reduce noise and number of spans created.\n   *\n   * Defaults to true.\n   */\n  ignoreTrivialResolveSpans?: boolean;\n\n  /**\n   * If this is enabled, a http.server root span containing this span will automatically be renamed to include the operation name.\n   * Set this to `false` if you do not want this behavior, and want to keep the default http.server span name.\n   *\n   * Defaults to true.\n   */\n  useOperationNameForRootSpan?: boolean;\n}\n\nconst INTEGRATION_NAME = 'Graphql';\n\nexport const instrumentGraphql = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  GraphQLInstrumentation,\n  (_options: GraphqlOptions) => {\n    const options = getOptionsWithDefaults(_options);\n\n    return {\n      ...options,\n      responseHook(span, result) {\n        addOriginToSpan(span, 'auto.graphql.otel.graphql');\n\n        // We want to ensure spans are marked as errored if there are errors in the result\n        // We only do that if the span is not already marked with a status\n        const resultWithMaybeError = result as { errors?: { message: string }[] };\n        if (resultWithMaybeError.errors?.length && !spanToJSON(span).status) {\n          span.setStatus({ code: SpanStatusCode.ERROR });\n        }\n\n        const attributes = spanToJSON(span).data;\n\n        // If operation.name is not set, we fall back to use operation.type only\n        const operationType = attributes['graphql.operation.type'];\n        const operationName = attributes['graphql.operation.name'];\n\n        if (options.useOperationNameForRootSpan && operationType) {\n          const rootSpan = getRootSpan(span);\n          const rootSpanAttributes = spanToJSON(rootSpan).data;\n\n          const existingOperations = rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION] || [];\n\n          const newOperation = operationName ? `${operationType} ${operationName}` : `${operationType}`;\n\n          // We keep track of each operation on the root span\n          // This can either be a string, or an array of strings (if there are multiple operations)\n          if (Array.isArray(existingOperations)) {\n            (existingOperations as string[]).push(newOperation);\n            rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, existingOperations);\n          } else if (typeof existingOperations === 'string') {\n            rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, [existingOperations, newOperation]);\n          } else {\n            rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, newOperation);\n          }\n\n          if (!spanToJSON(rootSpan).data['original-description']) {\n            rootSpan.setAttribute('original-description', spanToJSON(rootSpan).description);\n          }\n          // Important for e.g. @sentry/aws-serverless because this would otherwise overwrite the name again\n          rootSpan.updateName(\n            `${spanToJSON(rootSpan).data['original-description']} (${getGraphqlOperationNamesFromAttribute(\n              existingOperations,\n            )})`,\n          );\n        }\n      },\n    };\n  },\n);\n\nconst _graphqlIntegration = ((options: GraphqlOptions = {}) => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      // We set defaults here, too, because otherwise we'd update the instrumentation config\n      // to the config without defaults, as `generateInstrumentOnce` automatically calls `setConfig(options)`\n      // when being called the second time\n      instrumentGraphql(getOptionsWithDefaults(options));\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [graphql](https://www.npmjs.com/package/graphql) library.\n *\n * For more information, see the [`graphqlIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/graphql/).\n *\n * @param {GraphqlOptions} options Configuration options for the GraphQL integration.\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.graphqlIntegration()],\n * });\n */\nexport const graphqlIntegration = defineIntegration(_graphqlIntegration);\n\nfunction getOptionsWithDefaults(options?: GraphqlOptions): GraphqlOptions {\n  return {\n    ignoreResolveSpans: true,\n    ignoreTrivialResolveSpans: true,\n    useOperationNameForRootSpan: true,\n    ...options,\n  };\n}\n\n// copy from packages/opentelemetry/utils\nfunction getGraphqlOperationNamesFromAttribute(attr: AttributeValue): string {\n  if (Array.isArray(attr)) {\n    const sorted = attr.slice().sort();\n\n    // Up to 5 items, we just add all of them\n    if (sorted.length <= 5) {\n      return sorted.join(', ');\n    } else {\n      // Else, we add the first 5 and the diff of other operations\n      return `${sorted.slice(0, 5).join(', ')}, +${sorted.length - 5}`;\n    }\n  }\n\n  return `${attr}`;\n}\n"], "names": ["generateInstrumentOnce", "GraphQLInstrumentation", "addOriginToSpan", "spanToJSON", "SpanStatusCode", "getRootSpan", "SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION", "defineIntegration"], "mappings": ";;;;;;;;;AAsCA,MAAM,gBAAA,GAAmB,SAAS;;AAE3B,MAAM,iBAAkB,GAAEA,iCAAsB;AACvD,EAAE,gBAAgB;AAClB,EAAEC,6CAAsB;AACxB,EAAE,CAAC,QAAQ,KAAqB;AAChC,IAAI,MAAM,OAAQ,GAAE,sBAAsB,CAAC,QAAQ,CAAC;;AAEpD,IAAI,OAAO;AACX,MAAM,GAAG,OAAO;AAChB,MAAM,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE;AACjC,QAAQC,+BAAe,CAAC,IAAI,EAAE,2BAA2B,CAAC;;AAE1D;AACA;AACA,QAAQ,MAAM,oBAAqB,GAAE,MAAO;AAC5C,QAAQ,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAA,IAAU,CAACC,eAAU,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;AAC7E,UAAU,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEC,kBAAc,CAAC,KAAM,EAAC,CAAC;AACxD;;AAEA,QAAQ,MAAM,aAAaD,eAAU,CAAC,IAAI,CAAC,CAAC,IAAI;;AAEhD;AACA,QAAQ,MAAM,aAAc,GAAE,UAAU,CAAC,wBAAwB,CAAC;AAClE,QAAQ,MAAM,aAAc,GAAE,UAAU,CAAC,wBAAwB,CAAC;;AAElE,QAAQ,IAAI,OAAO,CAAC,2BAA4B,IAAG,aAAa,EAAE;AAClE,UAAU,MAAM,QAAS,GAAEE,gBAAW,CAAC,IAAI,CAAC;AAC5C,UAAU,MAAM,qBAAqBF,eAAU,CAAC,QAAQ,CAAC,CAAC,IAAI;;AAE9D,UAAU,MAAM,qBAAqB,kBAAkB,CAACG,yDAA2C,CAAA,IAAK,EAAE;;AAE1G,UAAU,MAAM,YAAa,GAAE,aAAc,GAAE,CAAC,EAAA,aAAA,CAAA,CAAA,EAAA,aAAA,CAAA,CAAA,GAAA,CAAA,EAAA,aAAA,CAAA,CAAA;;AAEA;AACA;AACA,UAAA,IAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,EAAA;AACA,YAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AACA,YAAA,QAAA,CAAA,YAAA,CAAAA,yDAAA,EAAA,kBAAA,CAAA;AACA,WAAA,MAAA,IAAA,OAAA,kBAAA,KAAA,QAAA,EAAA;AACA,YAAA,QAAA,CAAA,YAAA,CAAAA,yDAAA,EAAA,CAAA,kBAAA,EAAA,YAAA,CAAA,CAAA;AACA,WAAA,MAAA;AACA,YAAA,QAAA,CAAA,YAAA,CAAAA,yDAAA,EAAA,YAAA,CAAA;AACA;;AAEA,UAAA,IAAA,CAAAH,eAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,sBAAA,CAAA,EAAA;AACA,YAAA,QAAA,CAAA,YAAA,CAAA,sBAAA,EAAAA,eAAA,CAAA,QAAA,CAAA,CAAA,WAAA,CAAA;AACA;AACA;AACA,UAAA,QAAA,CAAA,UAAA;AACA,YAAA,CAAA,EAAAA,eAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,sBAAA,CAAA,CAAA,EAAA,EAAA,qCAAA;AACA,cAAA,kBAAA;AACA,aAAA,CAAA,CAAA,CAAA;AACA,WAAA;AACA;AACA,OAAA;AACA,KAAA;AACA,GAAA;AACA;;AAEA,MAAA,mBAAA,IAAA,CAAA,OAAA,GAAA,EAAA,KAAA;AACA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,gBAAA;AACA,IAAA,SAAA,GAAA;AACA;AACA;AACA;AACA,MAAA,iBAAA,CAAA,sBAAA,CAAA,OAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,kBAAA,GAAAI,sBAAA,CAAA,mBAAA;;AAEA,SAAA,sBAAA,CAAA,OAAA,EAAA;AACA,EAAA,OAAA;AACA,IAAA,kBAAA,EAAA,IAAA;AACA,IAAA,yBAAA,EAAA,IAAA;AACA,IAAA,2BAAA,EAAA,IAAA;AACA,IAAA,GAAA,OAAA;AACA,GAAA;AACA;;AAEA;AACA,SAAA,qCAAA,CAAA,IAAA,EAAA;AACA,EAAA,IAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AACA,IAAA,MAAA,MAAA,GAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,EAAA;;AAEA;AACA,IAAA,IAAA,MAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,MAAA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,KAAA,MAAA;AACA;AACA,MAAA,OAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;;;;;"}