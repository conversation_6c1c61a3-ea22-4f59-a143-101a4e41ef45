# Budget Tracker Deployment Guide

## Prerequisites
- Node.js 18+
- MongoDB Atlas cluster
- Redis server (for rate limiting)
- Sentry DSN

## Deployment Steps
1. Set environment variables (copy .env.example to .env)
2. Install dependencies: `npm install`
3. Run database migrations: `npm run migrate`
4. Start the server: `npm start`

## Health Checks
- `GET /api/health` - System status
- `GET /api/ready` - Service readiness

## Monitoring Setup
1. Configure Sentry DSN
2. Set up alerts for:
   - HTTP 5xx errors
   - Database connection failures
   - High response times
