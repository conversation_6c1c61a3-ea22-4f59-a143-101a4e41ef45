# Test environment variables
NODE_ENV=test
JWT_SECRET=test-secret-key-for-testing-that-is-at-least-32-characters-long
REFRESH_TOKEN_SECRET=test-refresh-secret-key-for-testing-that-is-at-least-32-characters-long
PORT=3001

# In-memory MongoDB will be used for testing
# No need for MONGODB_URI as we're using mongodb-memory-server

# Logging
LOG_LEVEL=error

# CORS
FRONTEND_URL=http://localhost:3000

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
