const express = require('express');
const { 
  createTransaction, 
  getTransactions, 
  getTransaction,
  updateTransaction,
  deleteTransaction,
  getTransactionStats
} = require('../controllers/transaction.controller');
const { protect } = require('../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .post(protect, createTransaction)
  .get(protect, getTransactions);

router.route('/stats')
  .get(protect, getTransactionStats);
  
router.route('/:id')
  .get(protect, getTransaction)
  .put(protect, updateTransaction)
  .delete(protect, deleteTransaction);

module.exports = router;
