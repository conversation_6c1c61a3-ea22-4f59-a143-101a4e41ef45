{"version": 3, "file": "anr.js", "sources": ["../../../src/utils/anr.ts"], "sourcesContent": ["import type { StackFrame } from '../types-hoist/stackframe';\nimport { filenameIsInApp } from './node-stack-trace';\nimport { UNKNOWN_FUNCTION } from './stacktrace';\n\ntype WatchdogReturn = {\n  /** Resets the watchdog timer */\n  poll: () => void;\n  /** Enables or disables the watchdog timer */\n  enabled: (state: boolean) => void;\n};\n\ntype CreateTimerImpl = () => { getTimeMs: () => number; reset: () => void };\n\n/**\n * A node.js watchdog timer\n * @param pollInterval The interval that we expect to get polled at\n * @param anrThreshold The threshold for when we consider ANR\n * @param callback The callback to call for ANR\n * @returns An object with `poll` and `enabled` functions {@link WatchdogReturn}\n */\nexport function watchdogTimer(\n  createTimer: CreateTimerImpl,\n  pollInterval: number,\n  anrThreshold: number,\n  callback: () => void,\n): WatchdogReturn {\n  const timer = createTimer();\n  let triggered = false;\n  let enabled = true;\n\n  setInterval(() => {\n    const diffMs = timer.getTimeMs();\n\n    if (triggered === false && diffMs > pollInterval + anrThreshold) {\n      triggered = true;\n      if (enabled) {\n        callback();\n      }\n    }\n\n    if (diffMs < pollInterval + anrThreshold) {\n      triggered = false;\n    }\n  }, 20);\n\n  return {\n    poll: () => {\n      timer.reset();\n    },\n    enabled: (state: boolean) => {\n      enabled = state;\n    },\n  };\n}\n\n// types copied from inspector.d.ts\ninterface Location {\n  scriptId: string;\n  lineNumber: number;\n  columnNumber?: number;\n}\n\ninterface CallFrame {\n  functionName: string;\n  location: Location;\n  url: string;\n}\n\n/**\n * Converts Debugger.CallFrame to Sentry StackFrame\n */\nexport function callFrameToStackFrame(\n  frame: CallFrame,\n  url: string | undefined,\n  getModuleFromFilename: (filename: string | undefined) => string | undefined,\n): StackFrame {\n  const filename = url ? url.replace(/^file:\\/\\//, '') : undefined;\n\n  // CallFrame row/col are 0 based, whereas StackFrame are 1 based\n  const colno = frame.location.columnNumber ? frame.location.columnNumber + 1 : undefined;\n  const lineno = frame.location.lineNumber ? frame.location.lineNumber + 1 : undefined;\n\n  return {\n    filename,\n    module: getModuleFromFilename(filename),\n    function: frame.functionName || UNKNOWN_FUNCTION,\n    colno,\n    lineno,\n    in_app: filename ? filenameIsInApp(filename) : undefined,\n  };\n}\n"], "names": ["UNKNOWN_FUNCTION", "filenameIsInApp"], "mappings": ";;;;;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa;AAC7B,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,QAAQ;AACV,EAAkB;AAClB,EAAE,MAAM,KAAA,GAAQ,WAAW,EAAE;AAC7B,EAAE,IAAI,SAAU,GAAE,KAAK;AACvB,EAAE,IAAI,OAAQ,GAAE,IAAI;;AAEpB,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,MAAM,MAAO,GAAE,KAAK,CAAC,SAAS,EAAE;;AAEpC,IAAI,IAAI,SAAA,KAAc,KAAA,IAAS,MAAA,GAAS,YAAA,GAAe,YAAY,EAAE;AACrE,MAAM,SAAA,GAAY,IAAI;AACtB,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,QAAQ,EAAE;AAClB;AACA;;AAEA,IAAI,IAAI,MAAA,GAAS,YAAa,GAAE,YAAY,EAAE;AAC9C,MAAM,SAAA,GAAY,KAAK;AACvB;AACA,GAAG,EAAE,EAAE,CAAC;;AAER,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,MAAM,KAAK,CAAC,KAAK,EAAE;AACnB,KAAK;AACL,IAAI,OAAO,EAAE,CAAC,KAAK,KAAc;AACjC,MAAM,OAAA,GAAU,KAAK;AACrB,KAAK;AACL,GAAG;AACH;;AAEA;;AAaA;AACA;AACA;AACO,SAAS,qBAAqB;AACrC,EAAE,KAAK;AACP,EAAE,GAAG;AACL,EAAE,qBAAqB;AACvB,EAAc;AACd,EAAE,MAAM,QAAA,GAAW,GAAA,GAAM,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAA,GAAI,SAAS;;AAElE;AACA,EAAE,MAAM,KAAM,GAAE,KAAK,CAAC,QAAQ,CAAC,YAAA,GAAe,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAA,GAAI,SAAS;AACzF,EAAE,MAAM,MAAO,GAAE,KAAK,CAAC,QAAQ,CAAC,UAAA,GAAa,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAA,GAAI,SAAS;;AAEtF,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,MAAM,EAAE,qBAAqB,CAAC,QAAQ,CAAC;AAC3C,IAAI,QAAQ,EAAE,KAAK,CAAC,YAAA,IAAgBA,2BAAgB;AACpD,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,MAAM,EAAE,QAAS,GAAEC,8BAAe,CAAC,QAAQ,CAAE,GAAE,SAAS;AAC5D,GAAG;AACH;;;;;"}