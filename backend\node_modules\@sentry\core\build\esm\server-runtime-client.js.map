{"version": 3, "file": "server-runtime-client.js", "sources": ["../../src/server-runtime-client.ts"], "sourcesContent": ["import { createCheckInEnvelope } from './checkin';\nimport { _getTraceInfoFromScope, Client } from './client';\nimport { getIsolationScope } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport { _INTERNAL_flushLogsBuffer } from './logs/exports';\nimport type { Scope } from './scope';\nimport { registerSpanErrorInstrumentation } from './tracing';\nimport type { CheckIn, MonitorConfig, SerializedCheckIn } from './types-hoist/checkin';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { Log } from './types-hoist/log';\nimport type { Primitive } from './types-hoist/misc';\nimport type { ClientOptions } from './types-hoist/options';\nimport type { ParameterizedString } from './types-hoist/parameterize';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { BaseTransportOptions } from './types-hoist/transport';\nimport { eventFromMessage, eventFromUnknownInput } from './utils/eventbuilder';\nimport { isPrimitive } from './utils/is';\nimport { logger } from './utils/logger';\nimport { uuid4 } from './utils/misc';\nimport { resolvedSyncPromise } from './utils/syncpromise';\n\n// TODO: Make this configurable\nconst DEFAULT_LOG_FLUSH_INTERVAL = 5000;\n\nexport interface ServerRuntimeClientOptions extends ClientOptions<BaseTransportOptions> {\n  platform?: string;\n  runtime?: { name: string; version?: string };\n  serverName?: string;\n}\n\n/**\n * The Sentry Server Runtime Client SDK.\n */\nexport class ServerRuntimeClient<\n  O extends ClientOptions & ServerRuntimeClientOptions = ServerRuntimeClientOptions,\n> extends Client<O> {\n  private _logFlushIdleTimeout: ReturnType<typeof setTimeout> | undefined;\n  private _logWeight: number;\n\n  /**\n   * Creates a new Edge SDK instance.\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: O) {\n    // Server clients always support tracing\n    registerSpanErrorInstrumentation();\n\n    super(options);\n\n    this._logWeight = 0;\n\n    if (this._options._experiments?.enableLogs) {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      const client = this;\n\n      client.on('flushLogs', () => {\n        client._logWeight = 0;\n        clearTimeout(client._logFlushIdleTimeout);\n      });\n\n      client.on('afterCaptureLog', log => {\n        client._logWeight += estimateLogSizeInBytes(log);\n\n        // We flush the logs buffer if it exceeds 0.8 MB\n        // The log weight is a rough estimate, so we flush way before\n        // the payload gets too big.\n        if (client._logWeight >= 800_000) {\n          _INTERNAL_flushLogsBuffer(client);\n        } else {\n          // start an idle timeout to flush the logs buffer if no logs are captured for a while\n          client._logFlushIdleTimeout = setTimeout(() => {\n            _INTERNAL_flushLogsBuffer(client);\n          }, DEFAULT_LOG_FLUSH_INTERVAL);\n        }\n      });\n\n      client.on('flush', () => {\n        _INTERNAL_flushLogsBuffer(client);\n      });\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromException(exception: unknown, hint?: EventHint): PromiseLike<Event> {\n    const event = eventFromUnknownInput(this, this._options.stackParser, exception, hint);\n    event.level = 'error';\n\n    return resolvedSyncPromise(event);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(\n    message: ParameterizedString,\n    level: SeverityLevel = 'info',\n    hint?: EventHint,\n  ): PromiseLike<Event> {\n    return resolvedSyncPromise(\n      eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace),\n    );\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureException(exception: unknown, hint?: EventHint, scope?: Scope): string {\n    setCurrentRequestSessionErroredOrCrashed(hint);\n    return super.captureException(exception, hint, scope);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string {\n    // If the event is of type Exception, then a request session should be captured\n    const isException = !event.type && event.exception?.values && event.exception.values.length > 0;\n    if (isException) {\n      setCurrentRequestSessionErroredOrCrashed(hint);\n    }\n\n    return super.captureEvent(event, hint, scope);\n  }\n\n  /**\n   * Create a cron monitor check in and send it to Sentry.\n   *\n   * @param checkIn An object that describes a check in.\n   * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n   * to create a monitor automatically when sending a check in.\n   */\n  public captureCheckIn(checkIn: CheckIn, monitorConfig?: MonitorConfig, scope?: Scope): string {\n    const id = 'checkInId' in checkIn && checkIn.checkInId ? checkIn.checkInId : uuid4();\n    if (!this._isEnabled()) {\n      DEBUG_BUILD && logger.warn('SDK not enabled, will not capture check-in.');\n      return id;\n    }\n\n    const options = this.getOptions();\n    const { release, environment, tunnel } = options;\n\n    const serializedCheckIn: SerializedCheckIn = {\n      check_in_id: id,\n      monitor_slug: checkIn.monitorSlug,\n      status: checkIn.status,\n      release,\n      environment,\n    };\n\n    if ('duration' in checkIn) {\n      serializedCheckIn.duration = checkIn.duration;\n    }\n\n    if (monitorConfig) {\n      serializedCheckIn.monitor_config = {\n        schedule: monitorConfig.schedule,\n        checkin_margin: monitorConfig.checkinMargin,\n        max_runtime: monitorConfig.maxRuntime,\n        timezone: monitorConfig.timezone,\n        failure_issue_threshold: monitorConfig.failureIssueThreshold,\n        recovery_threshold: monitorConfig.recoveryThreshold,\n      };\n    }\n\n    const [dynamicSamplingContext, traceContext] = _getTraceInfoFromScope(this, scope);\n    if (traceContext) {\n      serializedCheckIn.contexts = {\n        trace: traceContext,\n      };\n    }\n\n    const envelope = createCheckInEnvelope(\n      serializedCheckIn,\n      dynamicSamplingContext,\n      this.getSdkMetadata(),\n      tunnel,\n      this.getDsn(),\n    );\n\n    DEBUG_BUILD && logger.info('Sending checkin:', checkIn.monitorSlug, checkIn.status);\n\n    // sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.sendEnvelope(envelope);\n\n    return id;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _prepareEvent(\n    event: Event,\n    hint: EventHint,\n    currentScope: Scope,\n    isolationScope: Scope,\n  ): PromiseLike<Event | null> {\n    if (this._options.platform) {\n      event.platform = event.platform || this._options.platform;\n    }\n\n    if (this._options.runtime) {\n      event.contexts = {\n        ...event.contexts,\n        runtime: event.contexts?.runtime || this._options.runtime,\n      };\n    }\n\n    if (this._options.serverName) {\n      event.server_name = event.server_name || this._options.serverName;\n    }\n\n    return super._prepareEvent(event, hint, currentScope, isolationScope);\n  }\n}\n\nfunction setCurrentRequestSessionErroredOrCrashed(eventHint?: EventHint): void {\n  const requestSession = getIsolationScope().getScopeData().sdkProcessingMetadata.requestSession;\n  if (requestSession) {\n    // We mutate instead of doing `setSdkProcessingMetadata` because the http integration stores away a particular\n    // isolationScope. If that isolation scope is forked, setting the processing metadata here will not mutate the\n    // original isolation scope that the http integration stored away.\n    const isHandledException = eventHint?.mechanism?.handled ?? true;\n    // A request session can go from \"errored\" -> \"crashed\" but not \"crashed\" -> \"errored\".\n    // Crashed (unhandled exception) is worse than errored (handled exception).\n    if (isHandledException && requestSession.status !== 'crashed') {\n      requestSession.status = 'errored';\n    } else if (!isHandledException) {\n      requestSession.status = 'crashed';\n    }\n  }\n}\n\n/**\n * Estimate the size of a log in bytes.\n *\n * @param log - The log to estimate the size of.\n * @returns The estimated size of the log in bytes.\n */\nfunction estimateLogSizeInBytes(log: Log): number {\n  let weight = 0;\n\n  // Estimate byte size of 2 bytes per character. This is a rough estimate JS strings are stored as UTF-16.\n  if (log.message) {\n    weight += log.message.length * 2;\n  }\n\n  if (log.attributes) {\n    Object.values(log.attributes).forEach(value => {\n      if (Array.isArray(value)) {\n        weight += value.length * estimatePrimitiveSizeInBytes(value[0]);\n      } else if (isPrimitive(value)) {\n        weight += estimatePrimitiveSizeInBytes(value);\n      } else {\n        // For objects values, we estimate the size of the object as 100 bytes\n        weight += 100;\n      }\n    });\n  }\n\n  return weight;\n}\n\nfunction estimatePrimitiveSizeInBytes(value: Primitive): number {\n  if (typeof value === 'string') {\n    return value.length * 2;\n  } else if (typeof value === 'number') {\n    return 8;\n  } else if (typeof value === 'boolean') {\n    return 4;\n  }\n\n  return 0;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAqBA;AACA,MAAM,0BAAA,GAA6B,IAAI;;AAQvC;AACA;AACA;AACO,MAAM;;AAEb,SAAU,MAAM,CAAI;;AAIpB;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,EAAK;AACjC;AACA,IAAI,gCAAgC,EAAE;;AAEtC,IAAI,KAAK,CAAC,OAAO,CAAC;;AAElB,IAAI,IAAI,CAAC,UAAW,GAAE,CAAC;;AAEvB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE;AAChD;AACA,MAAM,MAAM,MAAO,GAAE,IAAI;;AAEzB,MAAM,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM;AACnC,QAAQ,MAAM,CAAC,UAAW,GAAE,CAAC;AAC7B,QAAQ,YAAY,CAAC,MAAM,CAAC,oBAAoB,CAAC;AACjD,OAAO,CAAC;;AAER,MAAM,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,OAAO;AAC1C,QAAQ,MAAM,CAAC,UAAA,IAAc,sBAAsB,CAAC,GAAG,CAAC;;AAExD;AACA;AACA;AACA,QAAQ,IAAI,MAAM,CAAC,UAAW,IAAG,MAAO,EAAE;AAC1C,UAAU,yBAAyB,CAAC,MAAM,CAAC;AAC3C,eAAe;AACf;AACA,UAAU,MAAM,CAAC,oBAAqB,GAAE,UAAU,CAAC,MAAM;AACzD,YAAY,yBAAyB,CAAC,MAAM,CAAC;AAC7C,WAAW,EAAE,0BAA0B,CAAC;AACxC;AACA,OAAO,CAAC;;AAER,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;AAC/B,QAAQ,yBAAyB,CAAC,MAAM,CAAC;AACzC,OAAO,CAAC;AACR;AACA;;AAEA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,SAAS,EAAW,IAAI,EAAkC;AACtF,IAAI,MAAM,KAAM,GAAE,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC;AACzF,IAAI,KAAK,CAAC,KAAM,GAAE,OAAO;;AAEzB,IAAI,OAAO,mBAAmB,CAAC,KAAK,CAAC;AACrC;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB;AACzB,IAAI,OAAO;AACX,IAAI,KAAK,GAAkB,MAAM;AACjC,IAAI,IAAI;AACR,IAAwB;AACxB,IAAI,OAAO,mBAAmB;AAC9B,MAAM,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AACvG,KAAK;AACL;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAc,KAAK,EAAkB;AACvF,IAAI,wCAAwC,CAAC,IAAI,CAAC;AAClD,IAAI,OAAO,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC;AACzD;;AAEA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAc,KAAK,EAAkB;AAC7E;AACA,IAAI,MAAM,cAAc,CAAC,KAAK,CAAC,IAAA,IAAQ,KAAK,CAAC,SAAS,EAAE,MAAO,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAO,GAAE,CAAC;AACnG,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,wCAAwC,CAAC,IAAI,CAAC;AACpD;;AAEA,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,OAAO,EAAW,aAAa,EAAkB,KAAK,EAAkB;AAChG,IAAI,MAAM,EAAG,GAAE,WAAY,IAAG,WAAW,OAAO,CAAC,SAAA,GAAY,OAAO,CAAC,YAAY,KAAK,EAAE;AACxF,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AAC5B,MAAM,eAAe,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC;AAC/E,MAAM,OAAO,EAAE;AACf;;AAEA,IAAI,MAAM,OAAQ,GAAE,IAAI,CAAC,UAAU,EAAE;AACrC,IAAI,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,MAAA,EAAS,GAAE,OAAO;;AAEpD,IAAI,MAAM,iBAAiB,GAAsB;AACjD,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,OAAO,CAAC,WAAW;AACvC,MAAM,MAAM,EAAE,OAAO,CAAC,MAAM;AAC5B,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,KAAK;;AAEL,IAAI,IAAI,UAAW,IAAG,OAAO,EAAE;AAC/B,MAAM,iBAAiB,CAAC,QAAA,GAAW,OAAO,CAAC,QAAQ;AACnD;;AAEA,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,iBAAiB,CAAC,cAAA,GAAiB;AACzC,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,QAAQ,cAAc,EAAE,aAAa,CAAC,aAAa;AACnD,QAAQ,WAAW,EAAE,aAAa,CAAC,UAAU;AAC7C,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,QAAQ,uBAAuB,EAAE,aAAa,CAAC,qBAAqB;AACpE,QAAQ,kBAAkB,EAAE,aAAa,CAAC,iBAAiB;AAC3D,OAAO;AACP;;AAEA,IAAI,MAAM,CAAC,sBAAsB,EAAE,YAAY,CAAA,GAAI,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC;AACtF,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,iBAAiB,CAAC,QAAA,GAAW;AACnC,QAAQ,KAAK,EAAE,YAAY;AAC3B,OAAO;AACP;;AAEA,IAAI,MAAM,QAAS,GAAE,qBAAqB;AAC1C,MAAM,iBAAiB;AACvB,MAAM,sBAAsB;AAC5B,MAAM,IAAI,CAAC,cAAc,EAAE;AAC3B,MAAM,MAAM;AACZ,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,KAAK;;AAEL,IAAI,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC;;AAEvF;AACA;AACA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;;AAE/B,IAAI,OAAO,EAAE;AACb;;AAEA;AACA;AACA;AACA,GAAY,aAAa;AACzB,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAA+B;AAC/B,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AAChC,MAAM,KAAK,CAAC,QAAS,GAAE,KAAK,CAAC,QAAS,IAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ;AAC/D;;AAEA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC/B,MAAM,KAAK,CAAC,QAAA,GAAW;AACvB,QAAQ,GAAG,KAAK,CAAC,QAAQ;AACzB,QAAQ,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,OAAQ,IAAG,IAAI,CAAC,QAAQ,CAAC,OAAO;AACjE,OAAO;AACP;;AAEA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAClC,MAAM,KAAK,CAAC,WAAY,GAAE,KAAK,CAAC,WAAY,IAAG,IAAI,CAAC,QAAQ,CAAC,UAAU;AACvE;;AAEA,IAAI,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,CAAC;AACzE;AACA;;AAEA,SAAS,wCAAwC,CAAC,SAAS,EAAoB;AAC/E,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE,CAAC,YAAY,EAAE,CAAC,qBAAqB,CAAC,cAAc;AAChG,EAAE,IAAI,cAAc,EAAE;AACtB;AACA;AACA;AACA,IAAI,MAAM,qBAAqB,SAAS,EAAE,SAAS,EAAE,OAAQ,IAAG,IAAI;AACpE;AACA;AACA,IAAI,IAAI,kBAAmB,IAAG,cAAc,CAAC,MAAA,KAAW,SAAS,EAAE;AACnE,MAAM,cAAc,CAAC,MAAO,GAAE,SAAS;AACvC,WAAW,IAAI,CAAC,kBAAkB,EAAE;AACpC,MAAM,cAAc,CAAC,MAAO,GAAE,SAAS;AACvC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,GAAG,EAAe;AAClD,EAAE,IAAI,MAAO,GAAE,CAAC;;AAEhB;AACA,EAAE,IAAI,GAAG,CAAC,OAAO,EAAE;AACnB,IAAI,MAAA,IAAU,GAAG,CAAC,OAAO,CAAC,MAAA,GAAS,CAAC;AACpC;;AAEA,EAAE,IAAI,GAAG,CAAC,UAAU,EAAE;AACtB,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,KAAA,IAAS;AACnD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAChC,QAAQ,MAAO,IAAG,KAAK,CAAC,MAAO,GAAE,4BAA4B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvE,OAAM,MAAO,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACrC,QAAQ,MAAO,IAAG,4BAA4B,CAAC,KAAK,CAAC;AACrD,aAAa;AACb;AACA,QAAQ,MAAA,IAAU,GAAG;AACrB;AACA,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO,MAAM;AACf;;AAEA,SAAS,4BAA4B,CAAC,KAAK,EAAqB;AAChE,EAAE,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;AACjC,IAAI,OAAO,KAAK,CAAC,MAAA,GAAS,CAAC;AAC3B,GAAE,MAAO,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;AACxC,IAAI,OAAO,CAAC;AACZ,GAAE,MAAO,IAAI,OAAO,KAAM,KAAI,SAAS,EAAE;AACzC,IAAI,OAAO,CAAC;AACZ;;AAEA,EAAE,OAAO,CAAC;AACV;;;;"}