{"version": 3, "file": "index.js", "sources": ["../../src/semanticAttributes.ts", "../../src/utils/getParentSpanId.ts", "../../src/utils/spanTypes.ts", "../../src/utils/getRequestSpanData.ts", "../../src/custom/client.ts", "../../src/utils/getSpanKind.ts", "../../src/constants.ts", "../../src/utils/contextData.ts", "../../src/utils/isSentryRequest.ts", "../../src/utils/getSamplingDecision.ts", "../../src/utils/parseSpanDescription.ts", "../../src/utils/enhanceDscWithOpenTelemetryRootSpanName.ts", "../../src/utils/getActiveSpan.ts", "../../src/debug-build.ts", "../../src/utils/makeTraceState.ts", "../../src/utils/setupCheck.ts", "../../src/propagator.ts", "../../src/trace.ts", "../../src/utils/suppressTracing.ts", "../../src/setupEventContextTrace.ts", "../../src/utils/getTraceData.ts", "../../src/asyncContextStrategy.ts", "../../src/contextManager.ts", "../../src/utils/groupSpansWithParents.ts", "../../src/utils/mapStatus.ts", "../../src/spanExporter.ts", "../../src/spanProcessor.ts", "../../src/sampler.ts"], "sourcesContent": ["/** If this attribute is true, it means that the parent is a remote span. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE = 'sentry.parentIsRemote';\n\n// These are not standardized yet, but used by the graphql instrumentation\nexport const SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION = 'sentry.graphql.operation';\n", "import type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\n\n/**\n * Get the parent span id from a span.\n * In OTel v1, the parent span id is accessed as `parentSpanId`\n * In OTel v2, the parent span id is accessed as `spanId` on the `parentSpanContext`\n */\nexport function getParentSpanId(span: ReadableSpan): string | undefined {\n  if ('parentSpanId' in span) {\n    return span.parentSpanId as string | undefined;\n  } else if ('parentSpanContext' in span) {\n    return (span.parentSpanContext as { spanId?: string } | undefined)?.spanId;\n  }\n\n  return undefined;\n}\n", "import type { SpanKind, SpanStatus } from '@opentelemetry/api';\nimport type { ReadableSpan, TimedEvent } from '@opentelemetry/sdk-trace-base';\nimport type { AbstractSpan } from '../types';\nimport { getParentSpanId } from './getParentSpanId';\n\n/**\n * Check if a given span has attributes.\n * This is necessary because the base `Span` type does not have attributes,\n * so in places where we are passed a generic span, we need to check if we want to access them.\n */\nexport function spanHasAttributes<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { attributes: ReadableSpan['attributes'] } {\n  const castSpan = span as ReadableSpan;\n  return !!castSpan.attributes && typeof castSpan.attributes === 'object';\n}\n\n/**\n * Check if a given span has a kind.\n * This is necessary because the base `Span` type does not have a kind,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasKind<SpanType extends AbstractSpan>(span: SpanType): span is SpanType & { kind: SpanKind } {\n  const castSpan = span as ReadableSpan;\n  return typeof castSpan.kind === 'number';\n}\n\n/**\n * Check if a given span has a status.\n * This is necessary because the base `Span` type does not have a status,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasStatus<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { status: SpanStatus } {\n  const castSpan = span as ReadableSpan;\n  return !!castSpan.status;\n}\n\n/**\n * Check if a given span has a name.\n * This is necessary because the base `Span` type does not have a name,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasName<SpanType extends AbstractSpan>(span: SpanType): span is SpanType & { name: string } {\n  const castSpan = span as ReadableSpan;\n  return !!castSpan.name;\n}\n\n/**\n * Check if a given span has a kind.\n * This is necessary because the base `Span` type does not have a kind,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasParentId<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { parentSpanId: string } {\n  const castSpan = span as ReadableSpan;\n  return !!getParentSpanId(castSpan);\n}\n\n/**\n * Check if a given span has events.\n * This is necessary because the base `Span` type does not have events,\n * so in places where we are passed a generic span, we need to check if we want to access it.\n */\nexport function spanHasEvents<SpanType extends AbstractSpan>(\n  span: SpanType,\n): span is SpanType & { events: TimedEvent[] } {\n  const castSpan = span as ReadableSpan;\n  return Array.isArray(castSpan.events);\n}\n", "import type { Span } from '@opentelemetry/api';\nimport type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_URL_FULL,\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_URL,\n} from '@opentelemetry/semantic-conventions';\nimport type { SanitizedRequestData } from '@sentry/core';\nimport { getSanitizedUrlString, parseUrl } from '@sentry/core';\nimport { spanHasAttributes } from './spanTypes';\n\n/**\n * Get sanitizied request data from an OTEL span.\n */\nexport function getRequestSpanData(span: Span | ReadableSpan): Partial<SanitizedRequestData> {\n  // The base `Span` type has no `attributes`, so we need to guard here against that\n  if (!spanHasAttributes(span)) {\n    return {};\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  const maybeUrlAttribute = (span.attributes[ATTR_URL_FULL] || span.attributes[SEMATTRS_HTTP_URL]) as\n    | string\n    | undefined;\n\n  const data: Partial<SanitizedRequestData> = {\n    url: maybeUrlAttribute,\n    // eslint-disable-next-line deprecation/deprecation\n    'http.method': (span.attributes[ATTR_HTTP_REQUEST_METHOD] || span.attributes[SEMATTRS_HTTP_METHOD]) as\n      | string\n      | undefined,\n  };\n\n  // Default to GET if URL is set but method is not\n  if (!data['http.method'] && data.url) {\n    data['http.method'] = 'GET';\n  }\n\n  try {\n    if (typeof maybeUrlAttribute === 'string') {\n      const url = parseUrl(maybeUrlAttribute);\n\n      data.url = getSanitizedUrlString(url);\n\n      if (url.search) {\n        data['http.query'] = url.search;\n      }\n      if (url.hash) {\n        data['http.fragment'] = url.hash;\n      }\n    }\n  } catch {\n    // ignore\n  }\n\n  return data;\n}\n", "import type { Tracer } from '@opentelemetry/api';\nimport { trace } from '@opentelemetry/api';\nimport type { BasicTracerProvider } from '@opentelemetry/sdk-trace-base';\nimport type { Client } from '@sentry/core';\nimport { SDK_VERSION } from '@sentry/core';\nimport type { OpenTelemetryClient as OpenTelemetryClientInterface } from '../types';\n\n// Typescript complains if we do not use `...args: any[]` for the mixin, with:\n// A mixin class must have a constructor with a single rest parameter of type 'any[]'.ts(2545)\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/**\n * Wrap an Client class with things we need for OpenTelemetry support.\n * Make sure that the Client class passed in is non-abstract!\n *\n * Usage:\n * const OpenTelemetryClient = getWrappedClientClass(NodeClient);\n * const client = new OpenTelemetryClient(options);\n */\nexport function wrapClientClass<\n  ClassConstructor extends new (...args: any[]) => Client,\n  WrappedClassConstructor extends new (...args: any[]) => Client & OpenTelemetryClientInterface,\n>(ClientClass: ClassConstructor): WrappedClassConstructor {\n  // @ts-expect-error We just assume that this is non-abstract, if you pass in an abstract class this would make it non-abstract\n  class OpenTelemetryClient extends ClientClass implements OpenTelemetryClientInterface {\n    public traceProvider: BasicTracerProvider | undefined;\n    private _tracer: Tracer | undefined;\n\n    public constructor(...args: any[]) {\n      super(...args);\n    }\n\n    /** Get the OTEL tracer. */\n    public get tracer(): Tracer {\n      if (this._tracer) {\n        return this._tracer;\n      }\n\n      const name = '@sentry/opentelemetry';\n      const version = SDK_VERSION;\n      const tracer = trace.getTracer(name, version);\n      this._tracer = tracer;\n\n      return tracer;\n    }\n\n    /**\n     * @inheritDoc\n     */\n    public async flush(timeout?: number): Promise<boolean> {\n      const provider = this.traceProvider;\n      await provider?.forceFlush();\n      return super.flush(timeout);\n    }\n  }\n\n  return OpenTelemetryClient as unknown as WrappedClassConstructor;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n", "import { SpanKind } from '@opentelemetry/api';\nimport type { AbstractSpan } from '../types';\nimport { spanHasKind } from './spanTypes';\n\n/**\n * Get the span kind from a span.\n * For whatever reason, this is not public API on the generic \"Span\" type,\n * so we need to check if we actually have a `SDKTraceBaseSpan` where we can fetch this from.\n * Otherwise, we fall back to `SpanKind.INTERNAL`.\n */\nexport function getSpanKind(span: AbstractSpan): SpanKind {\n  if (spanHasKind(span)) {\n    return span.kind;\n  }\n\n  return SpanKind.INTERNAL;\n}\n", "import { createContextKey } from '@opentelemetry/api';\n\nexport const SENTRY_TRACE_HEADER = 'sentry-trace';\nexport const SENTRY_BAGGAGE_HEADER = 'baggage';\n\nexport const SENTRY_TRACE_STATE_DSC = 'sentry.dsc';\nexport const SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING = 'sentry.sampled_not_recording';\nexport const SENTRY_TRACE_STATE_URL = 'sentry.url';\nexport const SENTRY_TRACE_STATE_SAMPLE_RAND = 'sentry.sample_rand';\nexport const SENTRY_TRACE_STATE_SAMPLE_RATE = 'sentry.sample_rate';\n\nexport const SENTRY_SCOPES_CONTEXT_KEY = createContextKey('sentry_scopes');\n\nexport const SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY = createContextKey('sentry_fork_isolation_scope');\n\nexport const SENTRY_FORK_SET_SCOPE_CONTEXT_KEY = createContextKey('sentry_fork_set_scope');\n\nexport const SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY = createContextKey('sentry_fork_set_isolation_scope');\n", "import type { Context } from '@opentelemetry/api';\nimport type { Scope } from '@sentry/core';\nimport { addNonEnumerableProperty } from '@sentry/core';\nimport { SENTRY_SCOPES_CONTEXT_KEY } from '../constants';\nimport type { CurrentScopes } from '../types';\n\nconst SCOPE_CONTEXT_FIELD = '_scopeContext';\n\n/**\n * Try to get the current scopes from the given OTEL context.\n * This requires a Context Manager that was wrapped with getWrappedContextManager.\n */\nexport function getScopesFromContext(context: Context): CurrentScopes | undefined {\n  return context.getValue(SENTRY_SCOPES_CONTEXT_KEY) as CurrentScopes | undefined;\n}\n\n/**\n * Set the current scopes on an OTEL context.\n * This will return a forked context with the Propagation Context set.\n */\nexport function setScopesOnContext(context: Context, scopes: CurrentScopes): Context {\n  return context.setValue(SENTRY_SCOPES_CONTEXT_KEY, scopes);\n}\n\n/**\n * Set the context on the scope so we can later look it up.\n * We need this to get the context from the scope in the `trace` functions.\n */\nexport function setContextOnScope(scope: Scope, context: Context): void {\n  addNonEnumerableProperty(scope, SCOPE_CONTEXT_FIELD, context);\n}\n\n/**\n * Get the context related to a scope.\n */\nexport function getContextFromScope(scope: Scope): Context | undefined {\n  return (scope as { [SCOPE_CONTEXT_FIELD]?: Context })[SCOPE_CONTEXT_FIELD];\n}\n", "import { ATTR_URL_FULL, SEMATTRS_HTTP_URL } from '@opentelemetry/semantic-conventions';\nimport { getClient, isSentryRequestUrl } from '@sentry/core';\nimport type { AbstractSpan } from '../types';\nimport { spanHasAttributes } from './spanTypes';\n\n/**\n *\n * @param otelSpan Checks whether a given OTEL Span is an http request to sentry.\n * @returns boolean\n */\nexport function isSentryRequestSpan(span: AbstractSpan): boolean {\n  if (!spanHasAttributes(span)) {\n    return false;\n  }\n\n  const { attributes } = span;\n\n  // `ATTR_URL_FULL` is the new attribute, but we still support the old one, `ATTR_HTTP_URL`, for now.\n  // eslint-disable-next-line deprecation/deprecation\n  const httpUrl = attributes[SEMATTRS_HTTP_URL] || attributes[ATTR_URL_FULL];\n\n  if (!httpUrl) {\n    return false;\n  }\n\n  return isSentryRequestUrl(httpUrl.toString(), getClient());\n}\n", "import type { SpanContext } from '@opentelemetry/api';\nimport { TraceFlags } from '@opentelemetry/api';\nimport { baggageHeaderToDynamicSamplingContext } from '@sentry/core';\nimport { SENTRY_TRACE_STATE_DSC, SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING } from '../constants';\n\n/**\n * OpenTelemetry only knows about SAMPLED or NONE decision,\n * but for us it is important to differentiate between unset and unsampled.\n *\n * Both of these are identified as `traceFlags === TracegFlags.NONE`,\n * but we additionally look at a special trace state to differentiate between them.\n */\nexport function getSamplingDecision(spanContext: SpanContext): boolean | undefined {\n  const { traceFlags, traceState } = spanContext;\n\n  const sampledNotRecording = traceState ? traceState.get(SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING) === '1' : false;\n\n  // If trace flag is `SAMPLED`, we interpret this as sampled\n  // If it is `NONE`, it could mean either it was sampled to be not recorder, or that it was not sampled at all\n  // For us this is an important difference, sow e look at the SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING\n  // to identify which it is\n  if (traceFlags === TraceFlags.SAMPLED) {\n    return true;\n  }\n\n  if (sampledNotRecording) {\n    return false;\n  }\n\n  // Fall back to DSC as a last resort, that may also contain `sampled`...\n  const dscString = traceState ? traceState.get(SENTRY_TRACE_STATE_DSC) : undefined;\n  const dsc = dscString ? baggageHeaderToDynamicSamplingContext(dscString) : undefined;\n\n  if (dsc?.sampled === 'true') {\n    return true;\n  }\n  if (dsc?.sampled === 'false') {\n    return false;\n  }\n\n  return undefined;\n}\n", "import type { Attributes, AttributeValue } from '@opentelemetry/api';\nimport { SpanKind } from '@opentelemetry/api';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_HTTP_ROUTE,\n  ATTR_URL_FULL,\n  SEMATTRS_DB_STATEMENT,\n  SEMATTRS_DB_SYSTEM,\n  SEMATTRS_FAAS_TRIGGER,\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_TARGET,\n  SEMATTRS_HTTP_URL,\n  SEMATTRS_MESSAGING_SYSTEM,\n  SEMATTRS_RPC_SERVICE,\n} from '@opentelemetry/semantic-conventions';\nimport type { SpanAttributes, TransactionSource } from '@sentry/core';\nimport {\n  getSanitizedUrlString,\n  parseUrl,\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  stripUrlQueryAndFragment,\n} from '@sentry/core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION } from '../semanticAttributes';\nimport type { AbstractSpan } from '../types';\nimport { getSpanKind } from './getSpanKind';\nimport { spanHasAttributes, spanHasName } from './spanTypes';\n\ninterface SpanDescription {\n  op: string | undefined;\n  description: string;\n  source: TransactionSource;\n  data?: Record<string, string | undefined>;\n}\n\n/**\n * Infer the op & description for a set of name, attributes and kind of a span.\n */\nexport function inferSpanData(spanName: string, attributes: SpanAttributes, kind: SpanKind): SpanDescription {\n  // if http.method exists, this is an http request span\n  // eslint-disable-next-line deprecation/deprecation\n  const httpMethod = attributes[ATTR_HTTP_REQUEST_METHOD] || attributes[SEMATTRS_HTTP_METHOD];\n  if (httpMethod) {\n    return descriptionForHttpMethod({ attributes, name: spanName, kind }, httpMethod);\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  const dbSystem = attributes[SEMATTRS_DB_SYSTEM];\n  const opIsCache =\n    typeof attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] === 'string' &&\n    attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP].startsWith('cache.');\n\n  // If db.type exists then this is a database call span\n  // If the Redis DB is used as a cache, the span description should not be changed\n  if (dbSystem && !opIsCache) {\n    return descriptionForDbSystem({ attributes, name: spanName });\n  }\n\n  const customSourceOrRoute = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] === 'custom' ? 'custom' : 'route';\n\n  // If rpc.service exists then this is a rpc call span.\n  // eslint-disable-next-line deprecation/deprecation\n  const rpcService = attributes[SEMATTRS_RPC_SERVICE];\n  if (rpcService) {\n    return {\n      ...getUserUpdatedNameAndSource(spanName, attributes, 'route'),\n      op: 'rpc',\n    };\n  }\n\n  // If messaging.system exists then this is a messaging system span.\n  // eslint-disable-next-line deprecation/deprecation\n  const messagingSystem = attributes[SEMATTRS_MESSAGING_SYSTEM];\n  if (messagingSystem) {\n    return {\n      ...getUserUpdatedNameAndSource(spanName, attributes, customSourceOrRoute),\n      op: 'message',\n    };\n  }\n\n  // If faas.trigger exists then this is a function as a service span.\n  // eslint-disable-next-line deprecation/deprecation\n  const faasTrigger = attributes[SEMATTRS_FAAS_TRIGGER];\n  if (faasTrigger) {\n    return {\n      ...getUserUpdatedNameAndSource(spanName, attributes, customSourceOrRoute),\n      op: faasTrigger.toString(),\n    };\n  }\n\n  return { op: undefined, description: spanName, source: 'custom' };\n}\n\n/**\n * Extract better op/description from an otel span.\n *\n * Does not overwrite the span name if the source is already set to custom to ensure\n * that user-updated span names are preserved. In this case, we only adjust the op but\n * leave span description and source unchanged.\n *\n * Based on https://github.com/open-telemetry/opentelemetry-collector-contrib/blob/7422ce2a06337f68a59b552b8c5a2ac125d6bae5/exporter/sentryexporter/sentry_exporter.go#L306\n */\nexport function parseSpanDescription(span: AbstractSpan): SpanDescription {\n  const attributes = spanHasAttributes(span) ? span.attributes : {};\n  const name = spanHasName(span) ? span.name : '<unknown>';\n  const kind = getSpanKind(span);\n\n  return inferSpanData(name, attributes, kind);\n}\n\nfunction descriptionForDbSystem({ attributes, name }: { attributes: Attributes; name: string }): SpanDescription {\n  // if we already have a custom name, we don't overwrite it but only set the op\n  const userDefinedName = attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n  if (typeof userDefinedName === 'string') {\n    return {\n      op: 'db',\n      description: userDefinedName,\n      source: (attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource) || 'custom',\n    };\n  }\n\n  // if we already have the source set to custom, we don't overwrite the span description but only set the op\n  if (attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] === 'custom') {\n    return { op: 'db', description: name, source: 'custom' };\n  }\n\n  // Use DB statement (Ex \"SELECT * FROM table\") if possible as description.\n  // eslint-disable-next-line deprecation/deprecation\n  const statement = attributes[SEMATTRS_DB_STATEMENT];\n\n  const description = statement ? statement.toString() : name;\n\n  return { op: 'db', description, source: 'task' };\n}\n\n/** Only exported for tests. */\nexport function descriptionForHttpMethod(\n  { name, kind, attributes }: { name: string; attributes: Attributes; kind: SpanKind },\n  httpMethod: AttributeValue,\n): SpanDescription {\n  const opParts = ['http'];\n\n  switch (kind) {\n    case SpanKind.CLIENT:\n      opParts.push('client');\n      break;\n    case SpanKind.SERVER:\n      opParts.push('server');\n      break;\n  }\n\n  // Spans for HTTP requests we have determined to be prefetch requests will have a `.prefetch` postfix in the op\n  if (attributes['sentry.http.prefetch']) {\n    opParts.push('prefetch');\n  }\n\n  const { urlPath, url, query, fragment, hasRoute } = getSanitizedUrl(attributes, kind);\n\n  if (!urlPath) {\n    return { ...getUserUpdatedNameAndSource(name, attributes), op: opParts.join('.') };\n  }\n\n  const graphqlOperationsAttribute = attributes[SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION];\n\n  // Ex. GET /api/users\n  const baseDescription = `${httpMethod} ${urlPath}`;\n\n  // When the http span has a graphql operation, append it to the description\n  // We add these in the graphqlIntegration\n  const inferredDescription = graphqlOperationsAttribute\n    ? `${baseDescription} (${getGraphqlOperationNamesFromAttribute(graphqlOperationsAttribute)})`\n    : baseDescription;\n\n  // If `httpPath` is a root path, then we can categorize the transaction source as route.\n  const inferredSource: TransactionSource = hasRoute || urlPath === '/' ? 'route' : 'url';\n\n  const data: Record<string, string> = {};\n\n  if (url) {\n    data.url = url;\n  }\n  if (query) {\n    data['http.query'] = query;\n  }\n  if (fragment) {\n    data['http.fragment'] = fragment;\n  }\n\n  // If the span kind is neither client nor server, we use the original name\n  // this infers that somebody manually started this span, in which case we don't want to overwrite the name\n  const isClientOrServerKind = kind === SpanKind.CLIENT || kind === SpanKind.SERVER;\n\n  // If the span is an auto-span (=it comes from one of our instrumentations),\n  // we always want to infer the name\n  // this is necessary because some of the auto-instrumentation we use uses kind=INTERNAL\n  const origin = attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] || 'manual';\n  const isManualSpan = !`${origin}`.startsWith('auto');\n\n  // If users (or in very rare occasions we) set the source to custom, we don't overwrite the name\n  const alreadyHasCustomSource = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] === 'custom';\n  const customSpanName = attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n\n  const useInferredDescription =\n    !alreadyHasCustomSource && customSpanName == null && (isClientOrServerKind || !isManualSpan);\n\n  const { description, source } = useInferredDescription\n    ? { description: inferredDescription, source: inferredSource }\n    : getUserUpdatedNameAndSource(name, attributes);\n\n  return {\n    op: opParts.join('.'),\n    description,\n    source,\n    data,\n  };\n}\n\nfunction getGraphqlOperationNamesFromAttribute(attr: AttributeValue): string {\n  if (Array.isArray(attr)) {\n    const sorted = attr.slice().sort();\n\n    // Up to 5 items, we just add all of them\n    if (sorted.length <= 5) {\n      return sorted.join(', ');\n    } else {\n      // Else, we add the first 5 and the diff of other operations\n      return `${sorted.slice(0, 5).join(', ')}, +${sorted.length - 5}`;\n    }\n  }\n\n  return `${attr}`;\n}\n\n/** Exported for tests only */\nexport function getSanitizedUrl(\n  attributes: Attributes,\n  kind: SpanKind,\n): {\n  url: string | undefined;\n  urlPath: string | undefined;\n  query: string | undefined;\n  fragment: string | undefined;\n  hasRoute: boolean;\n} {\n  // This is the relative path of the URL, e.g. /sub\n  // eslint-disable-next-line deprecation/deprecation\n  const httpTarget = attributes[SEMATTRS_HTTP_TARGET];\n  // This is the full URL, including host & query params etc., e.g. https://example.com/sub?foo=bar\n  // eslint-disable-next-line deprecation/deprecation\n  const httpUrl = attributes[SEMATTRS_HTTP_URL] || attributes[ATTR_URL_FULL];\n  // This is the normalized route name - may not always be available!\n  const httpRoute = attributes[ATTR_HTTP_ROUTE];\n\n  const parsedUrl = typeof httpUrl === 'string' ? parseUrl(httpUrl) : undefined;\n  const url = parsedUrl ? getSanitizedUrlString(parsedUrl) : undefined;\n  const query = parsedUrl?.search || undefined;\n  const fragment = parsedUrl?.hash || undefined;\n\n  if (typeof httpRoute === 'string') {\n    return { urlPath: httpRoute, url, query, fragment, hasRoute: true };\n  }\n\n  if (kind === SpanKind.SERVER && typeof httpTarget === 'string') {\n    return { urlPath: stripUrlQueryAndFragment(httpTarget), url, query, fragment, hasRoute: false };\n  }\n\n  if (parsedUrl) {\n    return { urlPath: url, url, query, fragment, hasRoute: false };\n  }\n\n  // fall back to target even for client spans, if no URL is present\n  if (typeof httpTarget === 'string') {\n    return { urlPath: stripUrlQueryAndFragment(httpTarget), url, query, fragment, hasRoute: false };\n  }\n\n  return { urlPath: undefined, url, query, fragment, hasRoute: false };\n}\n\n/**\n * Because Otel instrumentation sometimes mutates span names via `span.updateName`, the only way\n * to ensure that a user-set span name is preserved is to store it as a tmp attribute on the span.\n * We delete this attribute once we're done with it when preparing the event envelope.\n *\n * This temp attribute always takes precedence over the original name.\n *\n * We also need to take care of setting the correct source. Users can always update the source\n * after updating the name, so we need to respect that.\n *\n * @internal exported only for testing\n */\nexport function getUserUpdatedNameAndSource(\n  originalName: string,\n  attributes: Attributes,\n  fallbackSource: TransactionSource = 'custom',\n): {\n  description: string;\n  source: TransactionSource;\n} {\n  const source = (attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource) || fallbackSource;\n  const description = attributes[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n\n  if (description && typeof description === 'string') {\n    return {\n      description,\n      source,\n    };\n  }\n\n  return { description: originalName, source };\n}\n", "import type { Client } from '@sentry/core';\nimport { hasSpansEnabled, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, spanToJSON } from '@sentry/core';\nimport { getSamplingDecision } from './getSamplingDecision';\nimport { parseSpanDescription } from './parseSpanDescription';\nimport { spanHasName } from './spanTypes';\n\n/**\n * Setup a DSC handler on the passed client,\n * ensuring that the transaction name is inferred from the span correctly.\n */\nexport function enhanceDscWithOpenTelemetryRootSpanName(client: Client): void {\n  client.on('createDsc', (dsc, rootSpan) => {\n    if (!rootSpan) {\n      return;\n    }\n\n    // We want to overwrite the transaction on the DSC that is created by default in core\n    // The reason for this is that we want to infer the span name, not use the initial one\n    // Otherwise, we'll get names like \"GET\" instead of e.g. \"GET /foo\"\n    // `parseSpanDescription` takes the attributes of the span into account for the name\n    // This mutates the passed-in DSC\n\n    const jsonSpan = spanToJSON(rootSpan);\n    const attributes = jsonSpan.data;\n    const source = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n\n    const { description } = spanHasName(rootSpan) ? parseSpanDescription(rootSpan) : { description: undefined };\n    if (source !== 'url' && description) {\n      dsc.transaction = description;\n    }\n\n    // Also ensure sampling decision is correctly inferred\n    // In core, we use `spanIsSampled`, which just looks at the trace flags\n    // but in OTEL, we use a slightly more complex logic to be able to differntiate between unsampled and deferred sampling\n    if (hasSpansEnabled()) {\n      const sampled = getSamplingDecision(rootSpan.spanContext());\n      dsc.sampled = sampled == undefined ? undefined : String(sampled);\n    }\n  });\n}\n", "import type { Span } from '@opentelemetry/api';\nimport { trace } from '@opentelemetry/api';\n\n/**\n * Returns the currently active span.\n */\nexport function getActiveSpan(): Span | undefined {\n  return trace.getActiveSpan();\n}\n", "declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n", "import { TraceState } from '@opentelemetry/core';\nimport type { DynamicSamplingContext } from '@sentry/core';\nimport { dynamicSamplingContextToSentryBaggageHeader } from '@sentry/core';\nimport { SENTRY_TRACE_STATE_DSC, SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING } from '../constants';\n\n/**\n * Generate a TraceState for the given data.\n */\nexport function makeTraceState({\n  dsc,\n  sampled,\n}: {\n  dsc?: Partial<DynamicSamplingContext>;\n  sampled?: boolean;\n}): TraceState {\n  // We store the DSC as OTEL trace state on the span context\n  const dscString = dsc ? dynamicSamplingContextToSentryBaggageHeader(dsc) : undefined;\n\n  const traceStateBase = new TraceState();\n\n  const traceStateWithDsc = dscString ? traceStateBase.set(SENTRY_TRACE_STATE_DSC, dscString) : traceStateBase;\n\n  // We also specifically want to store if this is sampled to be not recording,\n  // or unsampled (=could be either sampled or not)\n  return sampled === false ? traceStateWithDsc.set(SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING, '1') : traceStateWithDsc;\n}\n", "type OpenTelemetryElement = 'SentrySpanProcessor' | 'SentryContextManager' | 'SentryPropagator' | 'SentrySampler';\n\nconst setupElements = new Set<OpenTelemetryElement>();\n\n/** Get all the OpenTelemetry elements that have been set up. */\nexport function openTelemetrySetupCheck(): OpenTelemetryElement[] {\n  return Array.from(setupElements);\n}\n\n/** Mark an OpenTelemetry element as setup. */\nexport function setIsSetup(element: OpenTelemetryElement): void {\n  setupElements.add(element);\n}\n\n/** Only exported for tests. */\nexport function clearOpenTelemetrySetupCheck(): void {\n  setupElements.clear();\n}\n", "import type { Baggage, Context, Span, SpanContext, TextMapGetter, TextMapSetter } from '@opentelemetry/api';\nimport { context, INVALID_TRACEID, propagation, trace, TraceFlags } from '@opentelemetry/api';\nimport { isTracingSuppressed, W3CBaggagePropagator } from '@opentelemetry/core';\nimport { ATTR_URL_FULL, SEMATTRS_HTTP_URL } from '@opentelemetry/semantic-conventions';\nimport type { Client, continueTrace, DynamicSamplingContext, Options, Scope } from '@sentry/core';\nimport {\n  generateSentryTraceHeader,\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromScope,\n  getDynamicSamplingContextFromSpan,\n  getIsolationScope,\n  logger,\n  LRUMap,\n  parseBaggageHeader,\n  propagationContextFromHeaders,\n  SENTRY_BAGGAGE_KEY_PREFIX,\n  spanToJSON,\n  stringMatchesSomePattern,\n} from '@sentry/core';\nimport { SENTRY_BAGGAGE_HEADER, SENTRY_TRACE_HEADER, SENTRY_TRACE_STATE_URL } from './constants';\nimport { DEBUG_BUILD } from './debug-build';\nimport { getScopesFromContext, setScopesOnContext } from './utils/contextData';\nimport { getSamplingDecision } from './utils/getSamplingDecision';\nimport { makeTraceState } from './utils/makeTraceState';\nimport { setIsSetup } from './utils/setupCheck';\n\n/**\n * Injects and extracts `sentry-trace` and `baggage` headers from carriers.\n */\nexport class SentryPropagator extends W3CBaggagePropagator {\n  /** A map of URLs that have already been checked for if they match tracePropagationTargets. */\n  private _urlMatchesTargetsMap: LRUMap<string, boolean>;\n\n  public constructor() {\n    super();\n    setIsSetup('SentryPropagator');\n\n    // We're caching results so we don't have to recompute regexp every time we create a request.\n    this._urlMatchesTargetsMap = new LRUMap<string, boolean>(100);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    if (isTracingSuppressed(context)) {\n      DEBUG_BUILD && logger.log('[Tracing] Not injecting trace data for url because tracing is suppressed.');\n      return;\n    }\n\n    const activeSpan = trace.getSpan(context);\n    const url = activeSpan && getCurrentURL(activeSpan);\n\n    const tracePropagationTargets = getClient()?.getOptions()?.tracePropagationTargets;\n    if (!shouldPropagateTraceForUrl(url, tracePropagationTargets, this._urlMatchesTargetsMap)) {\n      DEBUG_BUILD &&\n        logger.log(\n          '[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:',\n          url,\n        );\n      return;\n    }\n\n    const existingBaggageHeader = getExistingBaggage(carrier);\n    let baggage = propagation.getBaggage(context) || propagation.createBaggage({});\n\n    const { dynamicSamplingContext, traceId, spanId, sampled } = getInjectionData(context);\n\n    if (existingBaggageHeader) {\n      const baggageEntries = parseBaggageHeader(existingBaggageHeader);\n\n      if (baggageEntries) {\n        Object.entries(baggageEntries).forEach(([key, value]) => {\n          baggage = baggage.setEntry(key, { value });\n        });\n      }\n    }\n\n    if (dynamicSamplingContext) {\n      baggage = Object.entries(dynamicSamplingContext).reduce<Baggage>((b, [dscKey, dscValue]) => {\n        if (dscValue) {\n          return b.setEntry(`${SENTRY_BAGGAGE_KEY_PREFIX}${dscKey}`, { value: dscValue });\n        }\n        return b;\n      }, baggage);\n    }\n\n    // We also want to avoid setting the default OTEL trace ID, if we get that for whatever reason\n    if (traceId && traceId !== INVALID_TRACEID) {\n      setter.set(carrier, SENTRY_TRACE_HEADER, generateSentryTraceHeader(traceId, spanId, sampled));\n    }\n\n    super.inject(propagation.setBaggage(context, baggage), carrier, setter);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const maybeSentryTraceHeader: string | string[] | undefined = getter.get(carrier, SENTRY_TRACE_HEADER);\n    const baggage = getter.get(carrier, SENTRY_BAGGAGE_HEADER);\n\n    const sentryTrace = maybeSentryTraceHeader\n      ? Array.isArray(maybeSentryTraceHeader)\n        ? maybeSentryTraceHeader[0]\n        : maybeSentryTraceHeader\n      : undefined;\n\n    // Add remote parent span context\n    // If there is no incoming trace, this will return the context as-is\n    return ensureScopesOnContext(getContextWithRemoteActiveSpan(context, { sentryTrace, baggage }));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public fields(): string[] {\n    return [SENTRY_TRACE_HEADER, SENTRY_BAGGAGE_HEADER];\n  }\n}\n\nconst NOT_PROPAGATED_MESSAGE =\n  '[Tracing] Not injecting trace data for url because it does not match tracePropagationTargets:';\n\n/**\n * Check if a given URL should be propagated to or not.\n * If no url is defined, or no trace propagation targets are defined, this will always return `true`.\n * You can also optionally provide a decision map, to cache decisions and avoid repeated regex lookups.\n */\nexport function shouldPropagateTraceForUrl(\n  url: string | undefined,\n  tracePropagationTargets: Options['tracePropagationTargets'],\n  decisionMap?: LRUMap<string, boolean>,\n): boolean {\n  if (typeof url !== 'string' || !tracePropagationTargets) {\n    return true;\n  }\n\n  const cachedDecision = decisionMap?.get(url);\n  if (cachedDecision !== undefined) {\n    DEBUG_BUILD && !cachedDecision && logger.log(NOT_PROPAGATED_MESSAGE, url);\n    return cachedDecision;\n  }\n\n  const decision = stringMatchesSomePattern(url, tracePropagationTargets);\n  decisionMap?.set(url, decision);\n\n  DEBUG_BUILD && !decision && logger.log(NOT_PROPAGATED_MESSAGE, url);\n  return decision;\n}\n\n/**\n * Get propagation injection data for the given context.\n * The additional options can be passed to override the scope and client that is otherwise derived from the context.\n */\nexport function getInjectionData(\n  context: Context,\n  options: { scope?: Scope; client?: Client } = {},\n): {\n  dynamicSamplingContext: Partial<DynamicSamplingContext> | undefined;\n  traceId: string | undefined;\n  spanId: string | undefined;\n  sampled: boolean | undefined;\n} {\n  const span = trace.getSpan(context);\n\n  // If we have a remote span, the spanId should be considered as the parentSpanId, not spanId itself\n  // Instead, we use a virtual (generated) spanId for propagation\n  if (span?.spanContext().isRemote) {\n    const spanContext = span.spanContext();\n    const dynamicSamplingContext = getDynamicSamplingContextFromSpan(span);\n\n    return {\n      dynamicSamplingContext,\n      traceId: spanContext.traceId,\n      spanId: undefined,\n      sampled: getSamplingDecision(spanContext), // TODO: Do we need to change something here?\n    };\n  }\n\n  // If we have a local span, we just use this\n  if (span) {\n    const spanContext = span.spanContext();\n    const dynamicSamplingContext = getDynamicSamplingContextFromSpan(span);\n\n    return {\n      dynamicSamplingContext,\n      traceId: spanContext.traceId,\n      spanId: spanContext.spanId,\n      sampled: getSamplingDecision(spanContext), // TODO: Do we need to change something here?\n    };\n  }\n\n  // Else we try to use the propagation context from the scope\n  // The only scenario where this should happen is when we neither have a span, nor an incoming trace\n  const scope = options.scope || getScopesFromContext(context)?.scope || getCurrentScope();\n  const client = options.client || getClient();\n\n  const propagationContext = scope.getPropagationContext();\n  const dynamicSamplingContext = client ? getDynamicSamplingContextFromScope(client, scope) : undefined;\n  return {\n    dynamicSamplingContext,\n    traceId: propagationContext.traceId,\n    spanId: propagationContext.propagationSpanId,\n    sampled: propagationContext.sampled,\n  };\n}\n\nfunction getContextWithRemoteActiveSpan(\n  ctx: Context,\n  { sentryTrace, baggage }: Parameters<typeof continueTrace>[0],\n): Context {\n  const propagationContext = propagationContextFromHeaders(sentryTrace, baggage);\n\n  const { traceId, parentSpanId, sampled, dsc } = propagationContext;\n\n  // We only want to set the virtual span if we are continuing a concrete trace\n  // Otherwise, we ignore the incoming trace here, e.g. if we have no trace headers\n  if (!parentSpanId) {\n    return ctx;\n  }\n\n  const spanContext = generateRemoteSpanContext({\n    traceId,\n    spanId: parentSpanId,\n    sampled,\n    dsc,\n  });\n\n  return trace.setSpanContext(ctx, spanContext);\n}\n\n/**\n * Takes trace strings and propagates them as a remote active span.\n * This should be used in addition to `continueTrace` in OTEL-powered environments.\n */\nexport function continueTraceAsRemoteSpan<T>(\n  ctx: Context,\n  options: Parameters<typeof continueTrace>[0],\n  callback: () => T,\n): T {\n  const ctxWithSpanContext = ensureScopesOnContext(getContextWithRemoteActiveSpan(ctx, options));\n\n  return context.with(ctxWithSpanContext, callback);\n}\n\nfunction ensureScopesOnContext(ctx: Context): Context {\n  // If there are no scopes yet on the context, ensure we have them\n  const scopes = getScopesFromContext(ctx);\n  const newScopes = {\n    // If we have no scope here, this is most likely either the root context or a context manually derived from it\n    // In this case, we want to fork the current scope, to ensure we do not pollute the root scope\n    scope: scopes ? scopes.scope : getCurrentScope().clone(),\n    isolationScope: scopes ? scopes.isolationScope : getIsolationScope(),\n  };\n\n  return setScopesOnContext(ctx, newScopes);\n}\n\n/** Try to get the existing baggage header so we can merge this in. */\nfunction getExistingBaggage(carrier: unknown): string | undefined {\n  try {\n    const baggage = (carrier as Record<string, string | string[]>)[SENTRY_BAGGAGE_HEADER];\n    return Array.isArray(baggage) ? baggage.join(',') : baggage;\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * It is pretty tricky to get access to the outgoing request URL of a request in the propagator.\n * As we only have access to the context of the span to be sent and the carrier (=headers),\n * but the span may be unsampled and thus have no attributes.\n *\n * So we use the following logic:\n * 1. If we have an active span, we check if it has a URL attribute.\n * 2. Else, if the active span has no URL attribute (e.g. it is unsampled), we check a special trace state (which we set in our sampler).\n */\nfunction getCurrentURL(span: Span): string | undefined {\n  const spanData = spanToJSON(span).data;\n  // `ATTR_URL_FULL` is the new attribute, but we still support the old one, `SEMATTRS_HTTP_URL`, for now.\n  // eslint-disable-next-line deprecation/deprecation\n  const urlAttribute = spanData[SEMATTRS_HTTP_URL] || spanData[ATTR_URL_FULL];\n  if (typeof urlAttribute === 'string') {\n    return urlAttribute;\n  }\n\n  // Also look at the traceState, which we may set in the sampler even for unsampled spans\n  const urlTraceState = span.spanContext().traceState?.get(SENTRY_TRACE_STATE_URL);\n  if (urlTraceState) {\n    return urlTraceState;\n  }\n\n  return undefined;\n}\n\nfunction generateRemoteSpanContext({\n  spanId,\n  traceId,\n  sampled,\n  dsc,\n}: {\n  spanId: string;\n  traceId: string;\n  sampled: boolean | undefined;\n  dsc?: Partial<DynamicSamplingContext>;\n}): SpanContext {\n  // We store the DSC as OTEL trace state on the span context\n  const traceState = makeTraceState({\n    dsc,\n    sampled,\n  });\n\n  const spanContext: SpanContext = {\n    traceId,\n    spanId,\n    isRemote: true,\n    traceFlags: sampled ? TraceFlags.SAMPLED : TraceFlags.NONE,\n    traceState,\n  };\n\n  return spanContext;\n}\n", "import type { Con<PERSON>, Span, SpanContext, SpanO<PERSON><PERSON>, Tracer } from '@opentelemetry/api';\nimport { context, SpanStatusCode, trace, TraceFlags } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport type {\n  Client,\n  continueTrace as baseContinueTrace,\n  DynamicSamplingContext,\n  Scope,\n  Span as SentrySpan,\n  TraceContext,\n} from '@sentry/core';\nimport {\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromScope,\n  getDynamicSamplingContextFromSpan,\n  getRootSpan,\n  getTraceContextFromScope,\n  handleCallbackErrors,\n  SDK_VERSION,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  spanToJSON,\n  spanToTraceContext,\n} from '@sentry/core';\nimport { continueTraceAsRemoteSpan } from './propagator';\nimport type { OpenTelemetryClient, OpenTelemetrySpanContext } from './types';\nimport { getContextFromScope } from './utils/contextData';\nimport { getSamplingDecision } from './utils/getSamplingDecision';\nimport { makeTraceState } from './utils/makeTraceState';\n\n/**\n * Wraps a function with a transaction/span and finishes the span after the function is done.\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * If you want to create a span that is not set as active, use {@link startInactiveSpan}.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpan<T>(options: OpenTelemetrySpanContext, callback: (span: Span) => T): T {\n  const tracer = getTracer();\n\n  const { name, parentSpan: customParentSpan } = options;\n\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n  return wrapper(() => {\n    const activeCtx = getContext(options.scope, options.forceTransaction);\n    const shouldSkipSpan = options.onlyIfParent && !trace.getSpan(activeCtx);\n    const ctx = shouldSkipSpan ? suppressTracing(activeCtx) : activeCtx;\n\n    const spanOptions = getSpanOptions(options);\n\n    return tracer.startActiveSpan(name, spanOptions, ctx, span => {\n      return handleCallbackErrors(\n        () => callback(span),\n        () => {\n          // Only set the span status to ERROR when there wasn't any status set before, in order to avoid stomping useful span statuses\n          if (spanToJSON(span).status === undefined) {\n            span.setStatus({ code: SpanStatusCode.ERROR });\n          }\n        },\n        () => span.end(),\n      );\n    });\n  });\n}\n\n/**\n * Similar to `Sentry.startSpan`. Wraps a function with a span, but does not finish the span\n * after the function is done automatically. You'll have to call `span.end()` manually.\n *\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpanManual<T>(\n  options: OpenTelemetrySpanContext,\n  callback: (span: Span, finish: () => void) => T,\n): T {\n  const tracer = getTracer();\n\n  const { name, parentSpan: customParentSpan } = options;\n\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n  return wrapper(() => {\n    const activeCtx = getContext(options.scope, options.forceTransaction);\n    const shouldSkipSpan = options.onlyIfParent && !trace.getSpan(activeCtx);\n    const ctx = shouldSkipSpan ? suppressTracing(activeCtx) : activeCtx;\n\n    const spanOptions = getSpanOptions(options);\n\n    return tracer.startActiveSpan(name, spanOptions, ctx, span => {\n      return handleCallbackErrors(\n        () => callback(span, () => span.end()),\n        () => {\n          // Only set the span status to ERROR when there wasn't any status set before, in order to avoid stomping useful span statuses\n          if (spanToJSON(span).status === undefined) {\n            span.setStatus({ code: SpanStatusCode.ERROR });\n          }\n        },\n      );\n    });\n  });\n}\n\n/**\n * Creates a span. This span is not set as active, so will not get automatic instrumentation spans\n * as children or be able to be accessed via `Sentry.getActiveSpan()`.\n *\n * If you want to create a span that is set as active, use {@link startSpan}.\n *\n * This function will always return a span,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startInactiveSpan(options: OpenTelemetrySpanContext): Span {\n  const tracer = getTracer();\n\n  const { name, parentSpan: customParentSpan } = options;\n\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = getActiveSpanWrapper<Span>(customParentSpan);\n\n  return wrapper(() => {\n    const activeCtx = getContext(options.scope, options.forceTransaction);\n    const shouldSkipSpan = options.onlyIfParent && !trace.getSpan(activeCtx);\n    const ctx = shouldSkipSpan ? suppressTracing(activeCtx) : activeCtx;\n\n    const spanOptions = getSpanOptions(options);\n\n    const span = tracer.startSpan(name, spanOptions, ctx);\n\n    return span;\n  });\n}\n\n/**\n * Forks the current scope and sets the provided span as active span in the context of the provided callback. Can be\n * passed `null` to start an entirely new span tree.\n *\n * @param span Spans started in the context of the provided callback will be children of this span. If `null` is passed,\n * spans started within the callback will be root spans.\n * @param callback Execution context in which the provided span will be active. Is passed the newly forked scope.\n * @returns the value returned from the provided callback function.\n */\nexport function withActiveSpan<T>(span: Span | null, callback: (scope: Scope) => T): T {\n  const newContextWithActiveSpan = span ? trace.setSpan(context.active(), span) : trace.deleteSpan(context.active());\n  return context.with(newContextWithActiveSpan, () => callback(getCurrentScope()));\n}\n\nfunction getTracer(): Tracer {\n  const client = getClient<Client & OpenTelemetryClient>();\n  return client?.tracer || trace.getTracer('@sentry/opentelemetry', SDK_VERSION);\n}\n\nfunction getSpanOptions(options: OpenTelemetrySpanContext): SpanOptions {\n  const { startTime, attributes, kind, op, links } = options;\n\n  // OTEL expects timestamps in ms, not seconds\n  const fixedStartTime = typeof startTime === 'number' ? ensureTimestampInMilliseconds(startTime) : startTime;\n\n  return {\n    attributes: op\n      ? {\n          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: op,\n          ...attributes,\n        }\n      : attributes,\n    kind,\n    links,\n    startTime: fixedStartTime,\n  };\n}\n\nfunction ensureTimestampInMilliseconds(timestamp: number): number {\n  const isMs = timestamp < 9999999999;\n  return isMs ? timestamp * 1000 : timestamp;\n}\n\nfunction getContext(scope: Scope | undefined, forceTransaction: boolean | undefined): Context {\n  const ctx = getContextForScope(scope);\n  const parentSpan = trace.getSpan(ctx);\n\n  // In the case that we have no parent span, we start a new trace\n  // Note that if we continue a trace, we'll always have a remote parent span here anyhow\n  if (!parentSpan) {\n    return ctx;\n  }\n\n  // If we don't want to force a transaction, and we have a parent span, all good, we just return as-is!\n  if (!forceTransaction) {\n    return ctx;\n  }\n\n  // Else, if we do have a parent span but want to force a transaction, we have to simulate a \"root\" context\n\n  // Else, we need to do two things:\n  // 1. Unset the parent span from the context, so we'll create a new root span\n  // 2. Ensure the propagation context is correct, so we'll continue from the parent span\n  const ctxWithoutSpan = trace.deleteSpan(ctx);\n\n  const { spanId, traceId } = parentSpan.spanContext();\n  const sampled = getSamplingDecision(parentSpan.spanContext());\n\n  // In this case, when we are forcing a transaction, we want to treat this like continuing an incoming trace\n  // so we set the traceState according to the root span\n  const rootSpan = getRootSpan(parentSpan);\n  const dsc = getDynamicSamplingContextFromSpan(rootSpan);\n\n  const traceState = makeTraceState({\n    dsc,\n    sampled,\n  });\n\n  const spanOptions: SpanContext = {\n    traceId,\n    spanId,\n    isRemote: true,\n    traceFlags: sampled ? TraceFlags.SAMPLED : TraceFlags.NONE,\n    traceState,\n  };\n\n  const ctxWithSpanContext = trace.setSpanContext(ctxWithoutSpan, spanOptions);\n\n  return ctxWithSpanContext;\n}\n\nfunction getContextForScope(scope?: Scope): Context {\n  if (scope) {\n    const ctx = getContextFromScope(scope);\n    if (ctx) {\n      return ctx;\n    }\n  }\n\n  return context.active();\n}\n\n/**\n * Continue a trace from `sentry-trace` and `baggage` values.\n * These values can be obtained from incoming request headers, or in the browser from `<meta name=\"sentry-trace\">`\n * and `<meta name=\"baggage\">` HTML tags.\n *\n * Spans started with `startSpan`, `startSpanManual` and `startInactiveSpan`, within the callback will automatically\n * be attached to the incoming trace.\n *\n * This is a custom version of `continueTrace` that is used in OTEL-powered environments.\n * It propagates the trace as a remote span, in addition to setting it on the propagation context.\n */\nexport function continueTrace<T>(options: Parameters<typeof baseContinueTrace>[0], callback: () => T): T {\n  return continueTraceAsRemoteSpan(context.active(), options, callback);\n}\n\n/**\n * Get the trace context for a given scope.\n * We have a custom implemention here because we need an OTEL-specific way to get the span from a scope.\n */\nexport function getTraceContextForScope(\n  client: Client,\n  scope: Scope,\n): [dynamicSamplingContext: Partial<DynamicSamplingContext>, traceContext: TraceContext] {\n  const ctx = getContextFromScope(scope);\n  const span = ctx && trace.getSpan(ctx);\n\n  const traceContext = span ? spanToTraceContext(span) : getTraceContextFromScope(scope);\n\n  const dynamicSamplingContext = span\n    ? getDynamicSamplingContextFromSpan(span)\n    : getDynamicSamplingContextFromScope(client, scope);\n  return [dynamicSamplingContext, traceContext];\n}\n\nfunction getActiveSpanWrapper<T>(parentSpan: Span | SentrySpan | undefined | null): (callback: () => T) => T {\n  return parentSpan !== undefined\n    ? (callback: () => T) => {\n        return withActiveSpan(parentSpan, callback);\n      }\n    : (callback: () => T) => callback();\n}\n", "import { context } from '@opentelemetry/api';\nimport { suppressTracing as suppressTracingImpl } from '@opentelemetry/core';\n\n/** Suppress tracing in the given callback, ensuring no spans are generated inside of it. */\nexport function suppressTracing<T>(callback: () => T): T {\n  const ctx = suppressTracingImpl(context.active());\n  return context.with(ctx, callback);\n}\n", "import type { Client } from '@sentry/core';\nimport { getDynamicSamplingContextFromSpan, getRootSpan, spanToTraceContext } from '@sentry/core';\nimport { getActiveSpan } from './utils/getActiveSpan';\n\n/** Ensure the `trace` context is set on all events. */\nexport function setupEventContextTrace(client: Client): void {\n  client.on('preprocessEvent', event => {\n    const span = getActiveSpan();\n    // For transaction events, this is handled separately\n    // Because the active span may not be the span that is actually the transaction event\n    if (!span || event.type === 'transaction') {\n      return;\n    }\n\n    // If event has already set `trace` context, use that one.\n    event.contexts = {\n      trace: spanToTraceContext(span),\n      ...event.contexts,\n    };\n\n    const rootSpan = getRootSpan(span);\n\n    event.sdkProcessingMetadata = {\n      dynamicSamplingContext: getDynamicSamplingContextFromSpan(rootSpan),\n      ...event.sdkProcessingMetadata,\n    };\n\n    return event;\n  });\n}\n", "import * as api from '@opentelemetry/api';\nimport type { Client, Scope, SerializedTraceData, Span } from '@sentry/core';\nimport {\n  dynamicSamplingContextToSentryBaggageHeader,\n  generateSentryTraceHeader,\n  getCapturedScopesOnSpan,\n} from '@sentry/core';\nimport { getInjectionData } from '../propagator';\nimport { getContextFromScope } from './contextData';\n\n/**\n * Otel-specific implementation of `getTraceData`.\n * @see `@sentry/core` version of `getTraceData` for more information\n */\nexport function getTraceData({\n  span,\n  scope,\n  client,\n}: { span?: Span; scope?: Scope; client?: Client } = {}): SerializedTraceData {\n  let ctx = (scope && getContextFromScope(scope)) ?? api.context.active();\n\n  if (span) {\n    const { scope } = getCapturedScopesOnSpan(span);\n    // fall back to current context if for whatever reason we can't find the one of the span\n    ctx = (scope && getContextFromScope(scope)) || api.trace.setSpan(api.context.active(), span);\n  }\n\n  const { traceId, spanId, sampled, dynamicSamplingContext } = getInjectionData(ctx, { scope, client });\n\n  return {\n    'sentry-trace': generateSentryTraceHeader(traceId, spanId, sampled),\n    baggage: dynamicSamplingContextToSentryBaggageHeader(dynamicSamplingContext),\n  };\n}\n", "import * as api from '@opentelemetry/api';\nimport type { Scope, withActiveSpan as defaultWithActiveSpan } from '@sentry/core';\nimport { getDefaultCurrentScope, getDefaultIsolationScope, setAsyncContextStrategy } from '@sentry/core';\nimport {\n  SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_SCOPE_CONTEXT_KEY,\n} from './constants';\nimport { continueTrace, startInactiveSpan, startSpan, startSpanManual, withActiveSpan } from './trace';\nimport type { CurrentScopes } from './types';\nimport { getContextFromScope, getScopesFromContext } from './utils/contextData';\nimport { getActiveSpan } from './utils/getActiveSpan';\nimport { getTraceData } from './utils/getTraceData';\nimport { suppressTracing } from './utils/suppressTracing';\n\n/**\n * Sets the async context strategy to use follow the OTEL context under the hood.\n * We handle forking a hub inside of our custom OTEL Context Manager (./otelContextManager.ts)\n */\nexport function setOpenTelemetryContextAsyncContextStrategy(): void {\n  function getScopes(): CurrentScopes {\n    const ctx = api.context.active();\n    const scopes = getScopesFromContext(ctx);\n\n    if (scopes) {\n      return scopes;\n    }\n\n    // fallback behavior:\n    // if, for whatever reason, we can't find scopes on the context here, we have to fix this somehow\n    return {\n      scope: getDefaultCurrentScope(),\n      isolationScope: getDefaultIsolationScope(),\n    };\n  }\n\n  function withScope<T>(callback: (scope: Scope) => T): T {\n    const ctx = api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which uses the presence of this key to determine if it should\n    // fork the isolation scope, or not\n    // as by default, we don't want to fork this, unless triggered explicitly by `withScope`\n    return api.context.with(ctx, () => {\n      return callback(getCurrentScope());\n    });\n  }\n\n  function withSetScope<T>(scope: Scope, callback: (scope: Scope) => T): T {\n    const ctx = getContextFromScope(scope) || api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_SET_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which picks up this scope as the current scope\n    return api.context.with(ctx.setValue(SENTRY_FORK_SET_SCOPE_CONTEXT_KEY, scope), () => {\n      return callback(scope);\n    });\n  }\n\n  function withIsolationScope<T>(callback: (isolationScope: Scope) => T): T {\n    const ctx = api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which uses the presence of this key to determine if it should\n    // fork the isolation scope, or not\n    return api.context.with(ctx.setValue(SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY, true), () => {\n      return callback(getIsolationScope());\n    });\n  }\n\n  function withSetIsolationScope<T>(isolationScope: Scope, callback: (isolationScope: Scope) => T): T {\n    const ctx = api.context.active();\n\n    // We depend on the otelContextManager to handle the context/hub\n    // We set the `SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY` context value, which is picked up by\n    // the OTEL context manager, which uses the presence of this key to determine if it should\n    // fork the isolation scope, or not\n    return api.context.with(ctx.setValue(SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY, isolationScope), () => {\n      return callback(getIsolationScope());\n    });\n  }\n\n  function getCurrentScope(): Scope {\n    return getScopes().scope;\n  }\n\n  function getIsolationScope(): Scope {\n    return getScopes().isolationScope;\n  }\n\n  setAsyncContextStrategy({\n    withScope,\n    withSetScope,\n    withSetIsolationScope,\n    withIsolationScope,\n    getCurrentScope,\n    getIsolationScope,\n    startSpan,\n    startSpanManual,\n    startInactiveSpan,\n    getActiveSpan,\n    suppressTracing,\n    getTraceData,\n    continueTrace,\n    // The types here don't fully align, because our own `Span` type is narrower\n    // than the OTEL one - but this is OK for here, as we now we'll only have OTEL spans passed around\n    withActiveSpan: withActiveSpan as typeof defaultWithActiveSpan,\n  });\n}\n", "import type { Context, ContextManager } from '@opentelemetry/api';\nimport type { Scope } from '@sentry/core';\nimport { getCurrentScope, getIsolationScope } from '@sentry/core';\nimport {\n  SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY,\n  SENTRY_FORK_SET_SCOPE_CONTEXT_KEY,\n} from './constants';\nimport { getScopesFromContext, setContextOnScope, setScopesOnContext } from './utils/contextData';\nimport { setIsSetup } from './utils/setupCheck';\n\n/**\n * Wrap an OpenTelemetry ContextManager in a way that ensures the context is kept in sync with the Sentry Scope.\n *\n * Usage:\n * import { AsyncLocalStorageContextManager } from '@opentelemetry/context-async-hooks';\n * const SentryContextManager = wrapContextManagerClass(AsyncLocalStorageContextManager);\n * const contextManager = new SentryContextManager();\n */\nexport function wrapContextManagerClass<ContextManagerInstance extends ContextManager>(\n  ContextManagerClass: new (...args: unknown[]) => ContextManagerInstance,\n): typeof ContextManagerClass {\n  /**\n   * This is a custom ContextManager for OpenTelemetry, which extends the default AsyncLocalStorageContextManager.\n   * It ensures that we create new scopes per context, so that the OTEL Context & the Sentry Scope are always in sync.\n   *\n   * Note that we currently only support AsyncHooks with this,\n   * but since this should work for Node 14+ anyhow that should be good enough.\n   */\n\n  // @ts-expect-error TS does not like this, but we know this is fine\n  class SentryContextManager extends ContextManagerClass {\n    public constructor(...args: unknown[]) {\n      super(...args);\n      setIsSetup('SentryContextManager');\n    }\n    /**\n     * Overwrite with() of the original AsyncLocalStorageContextManager\n     * to ensure we also create new scopes per context.\n     */\n    public with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n      context: Context,\n      fn: F,\n      thisArg?: ThisParameterType<F>,\n      ...args: A\n    ): ReturnType<F> {\n      const currentScopes = getScopesFromContext(context);\n      const currentScope = currentScopes?.scope || getCurrentScope();\n      const currentIsolationScope = currentScopes?.isolationScope || getIsolationScope();\n\n      const shouldForkIsolationScope = context.getValue(SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY) === true;\n      const scope = context.getValue(SENTRY_FORK_SET_SCOPE_CONTEXT_KEY) as Scope | undefined;\n      const isolationScope = context.getValue(SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY) as Scope | undefined;\n\n      const newCurrentScope = scope || currentScope.clone();\n      const newIsolationScope =\n        isolationScope || (shouldForkIsolationScope ? currentIsolationScope.clone() : currentIsolationScope);\n      const scopes = { scope: newCurrentScope, isolationScope: newIsolationScope };\n\n      const ctx1 = setScopesOnContext(context, scopes);\n\n      // Remove the unneeded values again\n      const ctx2 = ctx1\n        .deleteValue(SENTRY_FORK_ISOLATION_SCOPE_CONTEXT_KEY)\n        .deleteValue(SENTRY_FORK_SET_SCOPE_CONTEXT_KEY)\n        .deleteValue(SENTRY_FORK_SET_ISOLATION_SCOPE_CONTEXT_KEY);\n\n      setContextOnScope(newCurrentScope, ctx2);\n\n      return super.with(ctx2, fn, thisArg, ...args);\n    }\n  }\n\n  return SentryContextManager as unknown as typeof ContextManagerClass;\n}\n", "import type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE } from '../semanticAttributes';\nimport { getParentSpanId } from './getParentSpanId';\n\nexport interface SpanNode {\n  id: string;\n  span?: ReadableSpan;\n  parentNode?: SpanNode | undefined;\n  children: SpanNode[];\n}\n\ntype SpanMap = Map<string, SpanNode>;\n\n/**\n * This function runs through a list of OTEL Spans, and wraps them in an `SpanNode`\n * where each node holds a reference to their parent node.\n */\nexport function groupSpansWithParents(spans: ReadableSpan[]): SpanNode[] {\n  const nodeMap: SpanMap = new Map<string, SpanNode>();\n\n  for (const span of spans) {\n    createOrUpdateSpanNodeAndRefs(nodeMap, span);\n  }\n\n  return Array.from(nodeMap, function ([_id, spanNode]) {\n    return spanNode;\n  });\n}\n\n/**\n * This returns the _local_ parent ID - `parentId` on the span may point to a remote span.\n */\nexport function getLocalParentId(span: ReadableSpan): string | undefined {\n  const parentIsRemote = span.attributes[SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE] === true;\n  // If the parentId is the trace parent ID, we pretend it's undefined\n  // As this means the parent exists somewhere else\n  return !parentIsRemote ? getParentSpanId(span) : undefined;\n}\n\nfunction createOrUpdateSpanNodeAndRefs(nodeMap: SpanMap, span: ReadableSpan): void {\n  const id = span.spanContext().spanId;\n  const parentId = getLocalParentId(span);\n\n  if (!parentId) {\n    createOrUpdateNode(nodeMap, { id, span, children: [] });\n    return;\n  }\n\n  // Else make sure to create parent node as well\n  // Note that the parent may not know it's parent _yet_, this may be updated in a later pass\n  const parentNode = createOrGetParentNode(nodeMap, parentId);\n  const node = createOrUpdateNode(nodeMap, { id, span, parentNode, children: [] });\n  parentNode.children.push(node);\n}\n\nfunction createOrGetParentNode(nodeMap: SpanMap, id: string): SpanNode {\n  const existing = nodeMap.get(id);\n\n  if (existing) {\n    return existing;\n  }\n\n  return createOrUpdateNode(nodeMap, { id, children: [] });\n}\n\nfunction createOrUpdateNode(nodeMap: SpanMap, spanNode: SpanNode): SpanNode {\n  const existing = nodeMap.get(spanNode.id);\n\n  // If span is already set, nothing to do here\n  if (existing?.span) {\n    return existing;\n  }\n\n  // If it exists but span is not set yet, we update it\n  if (existing && !existing.span) {\n    existing.span = spanNode.span;\n    existing.parentNode = spanNode.parentNode;\n    return existing;\n  }\n\n  // Else, we create a new one...\n  nodeMap.set(spanNode.id, spanNode);\n  return spanNode;\n}\n", "import { SpanStatusCode } from '@opentelemetry/api';\nimport {\n  ATTR_HTTP_RESPONSE_STATUS_CODE,\n  SEMATTRS_HTTP_STATUS_CODE,\n  SEMATTRS_RPC_GRPC_STATUS_CODE,\n} from '@opentelemetry/semantic-conventions';\nimport type { SpanAttributes, SpanStatus } from '@sentry/core';\nimport { getSpanStatusFromHttpCode, SPAN_STATUS_ERROR, SPAN_STATUS_OK } from '@sentry/core';\nimport type { AbstractSpan } from '../types';\nimport { spanHasAttributes, spanHasStatus } from './spanTypes';\n\n// canonicalCodesGrpcMap maps some GRPC codes to Sentry's span statuses. See description in grpc documentation.\nconst canonicalGrpcErrorCodesMap: Record<string, SpanStatus['message']> = {\n  '1': 'cancelled',\n  '2': 'unknown_error',\n  '3': 'invalid_argument',\n  '4': 'deadline_exceeded',\n  '5': 'not_found',\n  '6': 'already_exists',\n  '7': 'permission_denied',\n  '8': 'resource_exhausted',\n  '9': 'failed_precondition',\n  '10': 'aborted',\n  '11': 'out_of_range',\n  '12': 'unimplemented',\n  '13': 'internal_error',\n  '14': 'unavailable',\n  '15': 'data_loss',\n  '16': 'unauthenticated',\n} as const;\n\nconst isStatusErrorMessageValid = (message: string): boolean => {\n  return Object.values(canonicalGrpcErrorCodesMap).includes(message as SpanStatus['message']);\n};\n\n/**\n * Get a Sentry span status from an otel span.\n */\nexport function mapStatus(span: AbstractSpan): SpanStatus {\n  const attributes = spanHasAttributes(span) ? span.attributes : {};\n  const status = spanHasStatus(span) ? span.status : undefined;\n\n  if (status) {\n    // Since span status OK is not set by default, we give it priority: https://opentelemetry.io/docs/concepts/signals/traces/#span-status\n    if (status.code === SpanStatusCode.OK) {\n      return { code: SPAN_STATUS_OK };\n      // If the span is already marked as erroneous we return that exact status\n    } else if (status.code === SpanStatusCode.ERROR) {\n      if (typeof status.message === 'undefined') {\n        const inferredStatus = inferStatusFromAttributes(attributes);\n        if (inferredStatus) {\n          return inferredStatus;\n        }\n      }\n\n      if (status.message && isStatusErrorMessageValid(status.message)) {\n        return { code: SPAN_STATUS_ERROR, message: status.message };\n      } else {\n        return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n      }\n    }\n  }\n\n  // If the span status is UNSET, we try to infer it from HTTP or GRPC status codes.\n  const inferredStatus = inferStatusFromAttributes(attributes);\n\n  if (inferredStatus) {\n    return inferredStatus;\n  }\n\n  // We default to setting the spans status to ok.\n  if (status?.code === SpanStatusCode.UNSET) {\n    return { code: SPAN_STATUS_OK };\n  } else {\n    return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n  }\n}\n\nfunction inferStatusFromAttributes(attributes: SpanAttributes): SpanStatus | undefined {\n  // If the span status is UNSET, we try to infer it from HTTP or GRPC status codes.\n\n  // eslint-disable-next-line deprecation/deprecation\n  const httpCodeAttribute = attributes[ATTR_HTTP_RESPONSE_STATUS_CODE] || attributes[SEMATTRS_HTTP_STATUS_CODE];\n  // eslint-disable-next-line deprecation/deprecation\n  const grpcCodeAttribute = attributes[SEMATTRS_RPC_GRPC_STATUS_CODE];\n\n  const numberHttpCode =\n    typeof httpCodeAttribute === 'number'\n      ? httpCodeAttribute\n      : typeof httpCodeAttribute === 'string'\n        ? parseInt(httpCodeAttribute)\n        : undefined;\n\n  if (typeof numberHttpCode === 'number') {\n    return getSpanStatusFromHttpCode(numberHttpCode);\n  }\n\n  if (typeof grpcCodeAttribute === 'string') {\n    return { code: SPAN_STATUS_ERROR, message: canonicalGrpcErrorCodesMap[grpcCodeAttribute] || 'unknown_error' };\n  }\n\n  return undefined;\n}\n", "/* eslint-disable max-lines */\nimport type { Span } from '@opentelemetry/api';\nimport { SpanKind } from '@opentelemetry/api';\nimport type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { ATTR_HTTP_RESPONSE_STATUS_CODE, SEMATTRS_HTTP_STATUS_CODE } from '@opentelemetry/semantic-conventions';\nimport type {\n  SpanAttributes,\n  SpanJSON,\n  SpanOrigin,\n  TraceContext,\n  TransactionEvent,\n  TransactionSource,\n} from '@sentry/core';\nimport {\n  captureEvent,\n  convertSpanLinksForEnvelope,\n  debounce,\n  getCapturedScopesOnSpan,\n  getDynamicSamplingContextFromSpan,\n  getStatusMessage,\n  logger,\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  spanTimeInputToSeconds,\n  timedEventsToMeasurements,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from './debug-build';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE } from './semanticAttributes';\nimport { getParentSpanId } from './utils/getParentSpanId';\nimport { getRequestSpanData } from './utils/getRequestSpanData';\nimport type { SpanNode } from './utils/groupSpansWithParents';\nimport { getLocalParentId, groupSpansWithParents } from './utils/groupSpansWithParents';\nimport { mapStatus } from './utils/mapStatus';\nimport { parseSpanDescription } from './utils/parseSpanDescription';\n\ntype SpanNodeCompleted = SpanNode & { span: ReadableSpan };\n\nconst MAX_SPAN_COUNT = 1000;\nconst DEFAULT_TIMEOUT = 300; // 5 min\n\ninterface FinishedSpanBucket {\n  timestampInS: number;\n  spans: Set<ReadableSpan>;\n}\n\n/**\n * A Sentry-specific exporter that converts OpenTelemetry Spans to Sentry Spans & Transactions.\n */\nexport class SentrySpanExporter {\n  /*\n   * A quick explanation on the buckets: We do bucketing of finished spans for efficiency. This span exporter is\n   * accumulating spans until a root span is encountered and then it flushes all the spans that are descendants of that\n   * root span. Because it is totally in the realm of possibilities that root spans are never finished, and we don't\n   * want to accumulate spans indefinitely in memory, we need to periodically evacuate spans. Naively we could simply\n   * store the spans in an array and each time a new span comes in we could iterate through the entire array and\n   * evacuate all spans that have an end-timestamp that is older than our limit. This could get quite expensive because\n   * we would have to iterate a potentially large number of spans every time we evacuate. We want to avoid these large\n   * bursts of computation.\n   *\n   * Instead we go for a bucketing approach and put spans into buckets, based on what second\n   * (modulo the time limit) the span was put into the exporter. With buckets, when we decide to evacuate, we can\n   * iterate through the bucket entries instead, which have an upper bound of items, making the evacuation much more\n   * efficient. Cleaning up also becomes much more efficient since it simply involves de-referencing a bucket within the\n   * bucket array, and letting garbage collection take care of the rest.\n   */\n  private _finishedSpanBuckets: (FinishedSpanBucket | undefined)[];\n  private _finishedSpanBucketSize: number;\n  private _spansToBucketEntry: WeakMap<ReadableSpan, FinishedSpanBucket>;\n  private _lastCleanupTimestampInS: number;\n  // Essentially a a set of span ids that are already sent. The values are expiration\n  // times in this cache so we don't hold onto them indefinitely.\n  private _sentSpans: Map<string, number>;\n  /* Internally, we use a debounced flush to give some wiggle room to the span processor to accumulate more spans. */\n  private _debouncedFlush: ReturnType<typeof debounce>;\n\n  public constructor(options?: {\n    /** Lower bound of time in seconds until spans that are buffered but have not been sent as part of a transaction get cleared from memory. */\n    timeout?: number;\n  }) {\n    this._finishedSpanBucketSize = options?.timeout || DEFAULT_TIMEOUT;\n    this._finishedSpanBuckets = new Array(this._finishedSpanBucketSize).fill(undefined);\n    this._lastCleanupTimestampInS = Math.floor(Date.now() / 1000);\n    this._spansToBucketEntry = new WeakMap();\n    this._sentSpans = new Map<string, number>();\n    this._debouncedFlush = debounce(this.flush.bind(this), 1, { maxWait: 100 });\n  }\n\n  /**\n   * Export a single span.\n   * This is called by the span processor whenever a span is ended.\n   */\n  public export(span: ReadableSpan): void {\n    const currentTimestampInS = Math.floor(Date.now() / 1000);\n\n    if (this._lastCleanupTimestampInS !== currentTimestampInS) {\n      let droppedSpanCount = 0;\n      this._finishedSpanBuckets.forEach((bucket, i) => {\n        if (bucket && bucket.timestampInS <= currentTimestampInS - this._finishedSpanBucketSize) {\n          droppedSpanCount += bucket.spans.size;\n          this._finishedSpanBuckets[i] = undefined;\n        }\n      });\n      if (droppedSpanCount > 0) {\n        DEBUG_BUILD &&\n          logger.log(\n            `SpanExporter dropped ${droppedSpanCount} spans because they were pending for more than ${this._finishedSpanBucketSize} seconds.`,\n          );\n      }\n      this._lastCleanupTimestampInS = currentTimestampInS;\n    }\n\n    const currentBucketIndex = currentTimestampInS % this._finishedSpanBucketSize;\n    const currentBucket = this._finishedSpanBuckets[currentBucketIndex] || {\n      timestampInS: currentTimestampInS,\n      spans: new Set(),\n    };\n    this._finishedSpanBuckets[currentBucketIndex] = currentBucket;\n    currentBucket.spans.add(span);\n    this._spansToBucketEntry.set(span, currentBucket);\n\n    // If the span doesn't have a local parent ID (it's a root span), we're gonna flush all the ended spans\n    const localParentId = getLocalParentId(span);\n    if (!localParentId || this._sentSpans.has(localParentId)) {\n      this._debouncedFlush();\n    }\n  }\n\n  /**\n   * Try to flush any pending spans immediately.\n   * This is called internally by the exporter (via _debouncedFlush),\n   * but can also be triggered externally if we force-flush.\n   */\n  public flush(): void {\n    const finishedSpans = this._finishedSpanBuckets.flatMap(bucket => (bucket ? Array.from(bucket.spans) : []));\n\n    this._flushSentSpanCache();\n    const sentSpans = this._maybeSend(finishedSpans);\n\n    const sentSpanCount = sentSpans.size;\n    const remainingOpenSpanCount = finishedSpans.length - sentSpanCount;\n    DEBUG_BUILD &&\n      logger.log(\n        `SpanExporter exported ${sentSpanCount} spans, ${remainingOpenSpanCount} spans are waiting for their parent spans to finish`,\n      );\n\n    const expirationDate = Date.now() + DEFAULT_TIMEOUT * 1000;\n\n    for (const span of sentSpans) {\n      this._sentSpans.set(span.spanContext().spanId, expirationDate);\n      const bucketEntry = this._spansToBucketEntry.get(span);\n      if (bucketEntry) {\n        bucketEntry.spans.delete(span);\n      }\n    }\n    // Cancel a pending debounced flush, if there is one\n    // This can be relevant if we directly flush, circumventing the debounce\n    // in that case, we want to cancel any pending debounced flush\n    this._debouncedFlush.cancel();\n  }\n\n  /**\n   * Clear the exporter.\n   * This is called when the span processor is shut down.\n   */\n  public clear(): void {\n    this._finishedSpanBuckets = this._finishedSpanBuckets.fill(undefined);\n    this._sentSpans.clear();\n    this._debouncedFlush.cancel();\n  }\n\n  /**\n   * Send the given spans, but only if they are part of a finished transaction.\n   *\n   * Returns the sent spans.\n   * Spans remain unsent when their parent span is not yet finished.\n   * This will happen regularly, as child spans are generally finished before their parents.\n   * But it _could_ also happen because, for whatever reason, a parent span was lost.\n   * In this case, we'll eventually need to clean this up.\n   */\n  private _maybeSend(spans: ReadableSpan[]): Set<ReadableSpan> {\n    const grouped = groupSpansWithParents(spans);\n    const sentSpans = new Set<ReadableSpan>();\n\n    const rootNodes = this._getCompletedRootNodes(grouped);\n\n    for (const root of rootNodes) {\n      const span = root.span;\n      sentSpans.add(span);\n      const transactionEvent = createTransactionForOtelSpan(span);\n\n      // We'll recursively add all the child spans to this array\n      const spans = transactionEvent.spans || [];\n\n      for (const child of root.children) {\n        createAndFinishSpanForOtelSpan(child, spans, sentSpans);\n      }\n\n      // spans.sort() mutates the array, but we do not use this anymore after this point\n      // so we can safely mutate it here\n      transactionEvent.spans =\n        spans.length > MAX_SPAN_COUNT\n          ? spans.sort((a, b) => a.start_timestamp - b.start_timestamp).slice(0, MAX_SPAN_COUNT)\n          : spans;\n\n      const measurements = timedEventsToMeasurements(span.events);\n      if (measurements) {\n        transactionEvent.measurements = measurements;\n      }\n\n      captureEvent(transactionEvent);\n    }\n\n    return sentSpans;\n  }\n\n  /** Remove \"expired\" span id entries from the _sentSpans cache. */\n  private _flushSentSpanCache(): void {\n    const currentTimestamp = Date.now();\n    // Note, it is safe to delete items from the map as we go: https://stackoverflow.com/a/35943995/90297\n    for (const [spanId, expirationTime] of this._sentSpans.entries()) {\n      if (expirationTime <= currentTimestamp) {\n        this._sentSpans.delete(spanId);\n      }\n    }\n  }\n\n  /** Check if a node is a completed root node or a node whose parent has already been sent */\n  private _nodeIsCompletedRootNodeOrHasSentParent(node: SpanNode): node is SpanNodeCompleted {\n    return !!node.span && (!node.parentNode || this._sentSpans.has(node.parentNode.id));\n  }\n\n  /** Get all completed root nodes from a list of nodes */\n  private _getCompletedRootNodes(nodes: SpanNode[]): SpanNodeCompleted[] {\n    // TODO: We should be able to remove the explicit `node is SpanNodeCompleted` type guard\n    //       once we stop supporting TS < 5.5\n    return nodes.filter((node): node is SpanNodeCompleted => this._nodeIsCompletedRootNodeOrHasSentParent(node));\n  }\n}\n\nfunction parseSpan(span: ReadableSpan): { op?: string; origin?: SpanOrigin; source?: TransactionSource } {\n  const attributes = span.attributes;\n\n  const origin = attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined;\n  const op = attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] as string | undefined;\n  const source = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionSource | undefined;\n\n  return { origin, op, source };\n}\n\n/** Exported only for tests. */\nexport function createTransactionForOtelSpan(span: ReadableSpan): TransactionEvent {\n  const { op, description, data, origin = 'manual', source } = getSpanData(span);\n  const capturedSpanScopes = getCapturedScopesOnSpan(span as unknown as Span);\n\n  const sampleRate = span.attributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] as number | undefined;\n\n  const attributes: SpanAttributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,\n    [SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]: sampleRate,\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: op,\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: origin,\n    ...data,\n    ...removeSentryAttributes(span.attributes),\n  };\n\n  const { links } = span;\n  const { traceId: trace_id, spanId: span_id } = span.spanContext();\n\n  // If parentSpanIdFromTraceState is defined at all, we want it to take precedence\n  // In that case, an empty string should be interpreted as \"no parent span id\",\n  // even if `span.parentSpanId` is set\n  // this is the case when we are starting a new trace, where we have a virtual span based on the propagationContext\n  // We only want to continue the traceId in this case, but ignore the parent span\n  const parent_span_id = getParentSpanId(span);\n\n  const status = mapStatus(span);\n\n  const traceContext: TraceContext = {\n    parent_span_id,\n    span_id,\n    trace_id,\n    data: attributes,\n    origin,\n    op,\n    status: getStatusMessage(status), // As per protocol, span status is allowed to be undefined\n    links: convertSpanLinksForEnvelope(links),\n  };\n\n  const statusCode = attributes[ATTR_HTTP_RESPONSE_STATUS_CODE];\n  const responseContext = typeof statusCode === 'number' ? { response: { status_code: statusCode } } : undefined;\n\n  const transactionEvent: TransactionEvent = {\n    contexts: {\n      trace: traceContext,\n      otel: {\n        resource: span.resource.attributes,\n      },\n      ...responseContext,\n    },\n    spans: [],\n    start_timestamp: spanTimeInputToSeconds(span.startTime),\n    timestamp: spanTimeInputToSeconds(span.endTime),\n    transaction: description,\n    type: 'transaction',\n    sdkProcessingMetadata: {\n      capturedSpanScope: capturedSpanScopes.scope,\n      capturedSpanIsolationScope: capturedSpanScopes.isolationScope,\n      sampleRate,\n      dynamicSamplingContext: getDynamicSamplingContextFromSpan(span as unknown as Span),\n    },\n    ...(source && {\n      transaction_info: {\n        source,\n      },\n    }),\n  };\n\n  return transactionEvent;\n}\n\nfunction createAndFinishSpanForOtelSpan(node: SpanNode, spans: SpanJSON[], sentSpans: Set<ReadableSpan>): void {\n  const span = node.span;\n\n  if (span) {\n    sentSpans.add(span);\n  }\n\n  const shouldDrop = !span;\n\n  // If this span should be dropped, we still want to create spans for the children of this\n  if (shouldDrop) {\n    node.children.forEach(child => {\n      createAndFinishSpanForOtelSpan(child, spans, sentSpans);\n    });\n    return;\n  }\n\n  const span_id = span.spanContext().spanId;\n  const trace_id = span.spanContext().traceId;\n  const parentSpanId = getParentSpanId(span);\n\n  const { attributes, startTime, endTime, links } = span;\n\n  const { op, description, data, origin = 'manual' } = getSpanData(span);\n  const allData = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: origin,\n    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: op,\n    ...removeSentryAttributes(attributes),\n    ...data,\n  };\n\n  const status = mapStatus(span);\n\n  const spanJSON: SpanJSON = {\n    span_id,\n    trace_id,\n    data: allData,\n    description,\n    parent_span_id: parentSpanId,\n    start_timestamp: spanTimeInputToSeconds(startTime),\n    // This is [0,0] by default in OTEL, in which case we want to interpret this as no end time\n    timestamp: spanTimeInputToSeconds(endTime) || undefined,\n    status: getStatusMessage(status), // As per protocol, span status is allowed to be undefined\n    op,\n    origin,\n    measurements: timedEventsToMeasurements(span.events),\n    links: convertSpanLinksForEnvelope(links),\n  };\n\n  spans.push(spanJSON);\n\n  node.children.forEach(child => {\n    createAndFinishSpanForOtelSpan(child, spans, sentSpans);\n  });\n}\n\nfunction getSpanData(span: ReadableSpan): {\n  data: Record<string, unknown>;\n  op?: string;\n  description: string;\n  source?: TransactionSource;\n  origin?: SpanOrigin;\n} {\n  const { op: definedOp, source: definedSource, origin } = parseSpan(span);\n  const { op: inferredOp, description, source: inferredSource, data: inferredData } = parseSpanDescription(span);\n\n  const op = definedOp || inferredOp;\n  const source = definedSource || inferredSource;\n\n  const data = { ...inferredData, ...getData(span) };\n\n  return {\n    op,\n    description,\n    source,\n    origin,\n    data,\n  };\n}\n\n/**\n * Remove custom `sentry.` attributes we do not need to send.\n * These are more carrier attributes we use inside of the SDK, we do not need to send them to the API.\n */\nfunction removeSentryAttributes(data: Record<string, unknown>): Record<string, unknown> {\n  const cleanedData = { ...data };\n\n  /* eslint-disable @typescript-eslint/no-dynamic-delete */\n  delete cleanedData[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE];\n  delete cleanedData[SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE];\n  delete cleanedData[SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME];\n  /* eslint-enable @typescript-eslint/no-dynamic-delete */\n\n  return cleanedData;\n}\n\nfunction getData(span: ReadableSpan): Record<string, unknown> {\n  const attributes = span.attributes;\n  const data: Record<string, unknown> = {};\n\n  if (span.kind !== SpanKind.INTERNAL) {\n    data['otel.kind'] = SpanKind[span.kind];\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  const maybeHttpStatusCodeAttribute = attributes[SEMATTRS_HTTP_STATUS_CODE];\n  if (maybeHttpStatusCodeAttribute) {\n    data[ATTR_HTTP_RESPONSE_STATUS_CODE] = maybeHttpStatusCodeAttribute as string;\n  }\n\n  const requestData = getRequestSpanData(span);\n\n  if (requestData.url) {\n    data.url = requestData.url;\n  }\n\n  if (requestData['http.query']) {\n    data['http.query'] = requestData['http.query'].slice(1);\n  }\n  if (requestData['http.fragment']) {\n    data['http.fragment'] = requestData['http.fragment'].slice(1);\n  }\n\n  return data;\n}\n", "import type { Context } from '@opentelemetry/api';\nimport { ROOT_CONTEXT, trace } from '@opentelemetry/api';\nimport type { ReadableSpan, Span, SpanProcessor as SpanProcessorInterface } from '@opentelemetry/sdk-trace-base';\nimport {\n  addChildSpanToSpan,\n  getClient,\n  getDefaultCurrentScope,\n  getDefaultIsolationScope,\n  logSpanEnd,\n  logSpanStart,\n  setCapturedScopesOnSpan,\n} from '@sentry/core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE } from './semanticAttributes';\nimport { SentrySpanExporter } from './spanExporter';\nimport { getScopesFromContext } from './utils/contextData';\nimport { setIsSetup } from './utils/setupCheck';\n\nfunction onSpanStart(span: Span, parentContext: Context): void {\n  // This is a reliable way to get the parent span - because this is exactly how the parent is identified in the OTEL SDK\n  const parentSpan = trace.getSpan(parentContext);\n\n  let scopes = getScopesFromContext(parentContext);\n\n  // We need access to the parent span in order to be able to move up the span tree for breadcrumbs\n  if (parentSpan && !parentSpan.spanContext().isRemote) {\n    addChildSpanToSpan(parentSpan, span);\n  }\n\n  // We need this in the span exporter\n  if (parentSpan?.spanContext().isRemote) {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_PARENT_IS_REMOTE, true);\n  }\n\n  // The root context does not have scopes stored, so we check for this specifically\n  // As fallback we attach the global scopes\n  if (parentContext === ROOT_CONTEXT) {\n    scopes = {\n      scope: getDefaultCurrentScope(),\n      isolationScope: getDefaultIsolationScope(),\n    };\n  }\n\n  // We need the scope at time of span creation in order to apply it to the event when the span is finished\n  if (scopes) {\n    setCapturedScopesOnSpan(span, scopes.scope, scopes.isolationScope);\n  }\n\n  logSpanStart(span);\n\n  const client = getClient();\n  client?.emit('spanStart', span);\n}\n\nfunction onSpanEnd(span: Span): void {\n  logSpanEnd(span);\n\n  const client = getClient();\n  client?.emit('spanEnd', span);\n}\n\n/**\n * Converts OpenTelemetry Spans to Sentry Spans and sends them to Sentry via\n * the Sentry SDK.\n */\nexport class SentrySpanProcessor implements SpanProcessorInterface {\n  private _exporter: SentrySpanExporter;\n\n  public constructor(options?: { timeout?: number }) {\n    setIsSetup('SentrySpanProcessor');\n    this._exporter = new SentrySpanExporter(options);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public async forceFlush(): Promise<void> {\n    this._exporter.flush();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public async shutdown(): Promise<void> {\n    this._exporter.clear();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public onStart(span: Span, parentContext: Context): void {\n    onSpanStart(span, parentContext);\n  }\n\n  /** @inheritDoc */\n  public onEnd(span: Span & ReadableSpan): void {\n    onSpanEnd(span);\n\n    this._exporter.export(span);\n  }\n}\n", "/* eslint-disable complexity */\nimport type { Context, Span, TraceState as TraceStateInterface } from '@opentelemetry/api';\nimport { isSpanContextValid, SpanKind, trace } from '@opentelemetry/api';\nimport { TraceState } from '@opentelemetry/core';\nimport type { Sam<PERSON>, SamplingResult } from '@opentelemetry/sdk-trace-base';\nimport { SamplingDecision } from '@opentelemetry/sdk-trace-base';\nimport {\n  ATTR_HTTP_REQUEST_METHOD,\n  ATTR_URL_FULL,\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_URL,\n} from '@opentelemetry/semantic-conventions';\nimport type { Client, SpanAttributes } from '@sentry/core';\nimport {\n  baggageHeaderToDynamicSamplingContext,\n  hasSpansEnabled,\n  logger,\n  parseSampleRate,\n  sampleSpan,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n} from '@sentry/core';\nimport {\n  SENTRY_TRACE_STATE_DSC,\n  SENTRY_TRACE_STATE_SAMPLE_RAND,\n  SENTRY_TRACE_STATE_SAMPLE_RATE,\n  SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING,\n  SENTRY_TRACE_STATE_URL,\n} from './constants';\nimport { DEBUG_BUILD } from './debug-build';\nimport { getScopesFromContext } from './utils/contextData';\nimport { getSamplingDecision } from './utils/getSamplingDecision';\nimport { inferSpanData } from './utils/parseSpanDescription';\nimport { setIsSetup } from './utils/setupCheck';\n\n/**\n * A custom OTEL sampler that uses Sentry sampling rates to make its decision\n */\nexport class SentrySampler implements Sampler {\n  private _client: Client;\n\n  public constructor(client: Client) {\n    this._client = client;\n    setIsSetup('SentrySampler');\n  }\n\n  /** @inheritDoc */\n  public shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    spanAttributes: SpanAttributes,\n    _links: unknown,\n  ): SamplingResult {\n    const options = this._client.getOptions();\n\n    const parentSpan = getValidSpan(context);\n    const parentContext = parentSpan?.spanContext();\n\n    if (!hasSpansEnabled(options)) {\n      return wrapSamplingDecision({ decision: undefined, context, spanAttributes });\n    }\n\n    // `ATTR_HTTP_REQUEST_METHOD` is the new attribute, but we still support the old one, `SEMATTRS_HTTP_METHOD`, for now.\n    // eslint-disable-next-line deprecation/deprecation\n    const maybeSpanHttpMethod = spanAttributes[SEMATTRS_HTTP_METHOD] || spanAttributes[ATTR_HTTP_REQUEST_METHOD];\n\n    // If we have a http.client span that has no local parent, we never want to sample it\n    // but we want to leave downstream sampling decisions up to the server\n    if (spanKind === SpanKind.CLIENT && maybeSpanHttpMethod && (!parentSpan || parentContext?.isRemote)) {\n      return wrapSamplingDecision({ decision: undefined, context, spanAttributes });\n    }\n\n    const parentSampled = parentSpan ? getParentSampled(parentSpan, traceId, spanName) : undefined;\n    const isRootSpan = !parentSpan || parentContext?.isRemote;\n\n    // We only sample based on parameters (like tracesSampleRate or tracesSampler) for root spans (which is done in sampleSpan).\n    // Non-root-spans simply inherit the sampling decision from their parent.\n    if (!isRootSpan) {\n      return wrapSamplingDecision({\n        decision: parentSampled ? SamplingDecision.RECORD_AND_SAMPLED : SamplingDecision.NOT_RECORD,\n        context,\n        spanAttributes,\n      });\n    }\n\n    // We want to pass the inferred name & attributes to the sampler method\n    const {\n      description: inferredSpanName,\n      data: inferredAttributes,\n      op,\n    } = inferSpanData(spanName, spanAttributes, spanKind);\n\n    const mergedAttributes = {\n      ...inferredAttributes,\n      ...spanAttributes,\n    };\n\n    if (op) {\n      mergedAttributes[SEMANTIC_ATTRIBUTE_SENTRY_OP] = op;\n    }\n\n    const mutableSamplingDecision = { decision: true };\n    this._client.emit(\n      'beforeSampling',\n      {\n        spanAttributes: mergedAttributes,\n        spanName: inferredSpanName,\n        parentSampled: parentSampled,\n        parentContext: parentContext,\n      },\n      mutableSamplingDecision,\n    );\n    if (!mutableSamplingDecision.decision) {\n      return wrapSamplingDecision({ decision: undefined, context, spanAttributes });\n    }\n\n    const { isolationScope } = getScopesFromContext(context) ?? {};\n\n    const dscString = parentContext?.traceState ? parentContext.traceState.get(SENTRY_TRACE_STATE_DSC) : undefined;\n    const dsc = dscString ? baggageHeaderToDynamicSamplingContext(dscString) : undefined;\n\n    const sampleRand = parseSampleRate(dsc?.sample_rand) ?? Math.random();\n\n    const [sampled, sampleRate, localSampleRateWasApplied] = sampleSpan(\n      options,\n      {\n        name: inferredSpanName,\n        attributes: mergedAttributes,\n        normalizedRequest: isolationScope?.getScopeData().sdkProcessingMetadata.normalizedRequest,\n        parentSampled,\n        parentSampleRate: parseSampleRate(dsc?.sample_rate),\n      },\n      sampleRand,\n    );\n\n    const method = `${maybeSpanHttpMethod}`.toUpperCase();\n    if (method === 'OPTIONS' || method === 'HEAD') {\n      DEBUG_BUILD && logger.log(`[Tracing] Not sampling span because HTTP method is '${method}' for ${spanName}`);\n\n      return wrapSamplingDecision({\n        decision: SamplingDecision.NOT_RECORD,\n        context,\n        spanAttributes,\n        sampleRand,\n        downstreamTraceSampleRate: 0, // we don't want to sample anything in the downstream trace either\n      });\n    }\n\n    if (\n      !sampled &&\n      // We check for `parentSampled === undefined` because we only want to record client reports for spans that are trace roots (ie. when there was incoming trace)\n      parentSampled === undefined\n    ) {\n      DEBUG_BUILD && logger.log('[Tracing] Discarding root span because its trace was not chosen to be sampled.');\n      this._client.recordDroppedEvent('sample_rate', 'transaction');\n    }\n\n    return {\n      ...wrapSamplingDecision({\n        decision: sampled ? SamplingDecision.RECORD_AND_SAMPLED : SamplingDecision.NOT_RECORD,\n        context,\n        spanAttributes,\n        sampleRand,\n        downstreamTraceSampleRate: localSampleRateWasApplied ? sampleRate : undefined,\n      }),\n      attributes: {\n        // We set the sample rate on the span when a local sample rate was applied to better understand how traces were sampled in Sentry\n        [SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]: localSampleRateWasApplied ? sampleRate : undefined,\n      },\n    };\n  }\n\n  /** Returns the sampler name or short description with the configuration. */\n  public toString(): string {\n    return 'SentrySampler';\n  }\n}\n\nfunction getParentSampled(parentSpan: Span, traceId: string, spanName: string): boolean | undefined {\n  const parentContext = parentSpan.spanContext();\n\n  // Only inherit sample rate if `traceId` is the same\n  // Note for testing: `isSpanContextValid()` checks the format of the traceId/spanId, so we need to pass valid ones\n  if (isSpanContextValid(parentContext) && parentContext.traceId === traceId) {\n    if (parentContext.isRemote) {\n      const parentSampled = getSamplingDecision(parentSpan.spanContext());\n      DEBUG_BUILD &&\n        logger.log(`[Tracing] Inheriting remote parent's sampled decision for ${spanName}: ${parentSampled}`);\n      return parentSampled;\n    }\n\n    const parentSampled = getSamplingDecision(parentContext);\n    DEBUG_BUILD && logger.log(`[Tracing] Inheriting parent's sampled decision for ${spanName}: ${parentSampled}`);\n    return parentSampled;\n  }\n\n  return undefined;\n}\n\n/**\n * Wrap a sampling decision with data that Sentry needs to work properly with it.\n * If you pass `decision: undefined`, it will be treated as `NOT_RECORDING`, but in contrast to passing `NOT_RECORDING`\n * it will not propagate this decision to downstream Sentry SDKs.\n */\nexport function wrapSamplingDecision({\n  decision,\n  context,\n  spanAttributes,\n  sampleRand,\n  downstreamTraceSampleRate,\n}: {\n  decision: SamplingDecision | undefined;\n  context: Context;\n  spanAttributes: SpanAttributes;\n  sampleRand?: number;\n  downstreamTraceSampleRate?: number;\n}): SamplingResult {\n  let traceState = getBaseTraceState(context, spanAttributes);\n\n  // We will override the propagated sample rate downstream when\n  // - the tracesSampleRate is applied\n  // - the tracesSampler is invoked\n  // Since unsampled OTEL spans (NonRecordingSpans) cannot hold attributes we need to store this on the (trace)context.\n  if (downstreamTraceSampleRate !== undefined) {\n    traceState = traceState.set(SENTRY_TRACE_STATE_SAMPLE_RATE, `${downstreamTraceSampleRate}`);\n  }\n\n  if (sampleRand !== undefined) {\n    traceState = traceState.set(SENTRY_TRACE_STATE_SAMPLE_RAND, `${sampleRand}`);\n  }\n\n  // If the decision is undefined, we treat it as NOT_RECORDING, but we don't propagate this decision to downstream SDKs\n  // Which is done by not setting `SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING` traceState\n  if (decision == undefined) {\n    return { decision: SamplingDecision.NOT_RECORD, traceState };\n  }\n\n  if (decision === SamplingDecision.NOT_RECORD) {\n    return { decision, traceState: traceState.set(SENTRY_TRACE_STATE_SAMPLED_NOT_RECORDING, '1') };\n  }\n\n  return { decision, traceState };\n}\n\nfunction getBaseTraceState(context: Context, spanAttributes: SpanAttributes): TraceStateInterface {\n  const parentSpan = trace.getSpan(context);\n  const parentContext = parentSpan?.spanContext();\n\n  let traceState = parentContext?.traceState || new TraceState();\n\n  // We always keep the URL on the trace state, so we can access it in the propagator\n  // `ATTR_URL_FULL` is the new attribute, but we still support the old one, `ATTR_HTTP_URL`, for now.\n  // eslint-disable-next-line deprecation/deprecation\n  const url = spanAttributes[SEMATTRS_HTTP_URL] || spanAttributes[ATTR_URL_FULL];\n  if (url && typeof url === 'string') {\n    traceState = traceState.set(SENTRY_TRACE_STATE_URL, url);\n  }\n\n  return traceState;\n}\n\n/**\n * If the active span is invalid, we want to ignore it as parent.\n * This aligns with how otel tracers and default samplers handle these cases.\n */\nfunction getValidSpan(context: Context): Span | undefined {\n  const span = trace.getSpan(context);\n  return span && isSpanContextValid(span.spanContext()) ? span : undefined;\n}\n"], "names": ["ATTR_URL_FULL", "SEMATTRS_HTTP_URL", "ATTR_HTTP_REQUEST_METHOD", "SEMATTRS_HTTP_METHOD", "parseUrl", "getSanitizedUrlString", "SDK_VERSION", "trace", "SpanKind", "createContextKey", "addNonEnumerableProperty", "isSentryRequestUrl", "getClient", "TraceFlags", "baggageHeaderToDynamicSamplingContext", "SEMATTRS_DB_SYSTEM", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMATTRS_RPC_SERVICE", "SEMATTRS_MESSAGING_SYSTEM", "SEMATTRS_FAAS_TRIGGER", "SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME", "SEMATTRS_DB_STATEMENT", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "SEMATTRS_HTTP_TARGET", "ATTR_HTTP_ROUTE", "stripUrlQueryAndFragment", "spanToJSON", "hasSpansEnabled", "dynamicSamplingContextToSentryBaggageHeader", "TraceState", "W3CBaggagePropagator", "LRUMap", "isTracingSuppressed", "logger", "propagation", "parseBaggageHeader", "SENTRY_BAGGAGE_KEY_PREFIX", "INVALID_TRACEID", "generateSentryTraceHeader", "stringMatchesSomePattern", "getDynamicSamplingContextFromSpan", "getCurrentScope", "getDynamicSamplingContextFromScope", "propagationContextFromHeaders", "context", "getIsolationScope", "suppressTracing", "handleCallbackErrors", "SpanStatusCode", "getRootSpan", "spanToTraceContext", "getTraceContextFromScope", "suppressTracingImpl", "getCapturedScopesOnSpan", "getDefaultCurrentScope", "getDefaultIsolationScope", "setAsyncContextStrategy", "SPAN_STATUS_OK", "SPAN_STATUS_ERROR", "ATTR_HTTP_RESPONSE_STATUS_CODE", "SEMATTRS_HTTP_STATUS_CODE", "SEMATTRS_RPC_GRPC_STATUS_CODE", "getSpanStatusFromHttpCode", "debounce", "timedEventsToMeasurements", "captureEvent", "SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE", "getStatusMessage", "convertSpanLinksForEnvelope", "spanTimeInputToSeconds", "addChildSpanToSpan", "ROOT_CONTEXT", "setCapturedScopesOnSpan", "logSpanStart", "logSpanEnd", "SamplingDecision", "parseSampleRate", "sampleSpan", "isSpanContextValid"], "mappings": ";;;;;;;;AAAA;AACO,MAAM,0CAA2C,GAAE,uBAAuB;;AAEjF;AACO,MAAM,2CAA4C,GAAE;;ACF3D;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,IAAI,EAAoC;AACxE,EAAE,IAAI,cAAe,IAAG,IAAI,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC,YAAa;AAC7B,SAAS,IAAI,mBAAoB,IAAG,IAAI,EAAE;AAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,iBAAkB,IAAqC,MAAM;AAC9E;;AAEA,EAAE,OAAO,SAAS;AAClB;;ACVA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB;AACjC,EAAE,IAAI;AACN,EAAiE;AACjE,EAAE,MAAM,QAAS,GAAE,IAAK;AACxB,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAA,IAAc,OAAO,QAAQ,CAAC,UAAW,KAAI,QAAQ;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAgC,IAAI,EAAmD;AAClH,EAAE,MAAM,QAAS,GAAE,IAAK;AACxB,EAAE,OAAO,OAAO,QAAQ,CAAC,IAAA,KAAS,QAAQ;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa;AAC7B,EAAE,IAAI;AACN,EAA6C;AAC7C,EAAE,MAAM,QAAS,GAAE,IAAK;AACxB,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAgC,IAAI,EAAiD;AAChH,EAAE,MAAM,QAAS,GAAE,IAAK;AACxB,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,EAAE,IAAI;AACN,EAA+C;AAC/C,EAAE,MAAM,QAAS,GAAE,IAAK;AACxB,EAAE,OAAO,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa;AAC7B,EAAE,IAAI;AACN,EAA+C;AAC/C,EAAE,MAAM,QAAS,GAAE,IAAK;AACxB,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;AACvC;;AC3DA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,IAAI,EAAsD;AAC7F;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;AAChC,IAAI,OAAO,EAAE;AACb;;AAEA;AACA,EAAE,MAAM,iBAAkB,IAAG,IAAI,CAAC,UAAU,CAACA,iCAAa,CAAA,IAAK,IAAI,CAAC,UAAU,CAACC,qCAAiB,CAAC;;AAE7F;;AAEJ,EAAE,MAAM,IAAI,GAAkC;AAC9C,IAAI,GAAG,EAAE,iBAAiB;AAC1B;AACA,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAACC,4CAAwB,CAAE,IAAG,IAAI,CAAC,UAAU,CAACC,wCAAoB,CAAC;;AAEhG;AACN,GAAG;;AAEH;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAA,IAAK,IAAI,CAAC,GAAG,EAAE;AACxC,IAAI,IAAI,CAAC,aAAa,CAAA,GAAI,KAAK;AAC/B;;AAEA,EAAE,IAAI;AACN,IAAI,IAAI,OAAO,iBAAkB,KAAI,QAAQ,EAAE;AAC/C,MAAM,MAAM,GAAI,GAAEC,aAAQ,CAAC,iBAAiB,CAAC;;AAE7C,MAAM,IAAI,CAAC,GAAA,GAAMC,0BAAqB,CAAC,GAAG,CAAC;;AAE3C,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE;AACtB,QAAQ,IAAI,CAAC,YAAY,IAAI,GAAG,CAAC,MAAM;AACvC;AACA,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE;AACpB,QAAQ,IAAI,CAAC,eAAe,IAAI,GAAG,CAAC,IAAI;AACxC;AACA;AACA,IAAI,MAAM;AACV;AACA;;AAEA,EAAE,OAAO,IAAI;AACb;;AClDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS;;AAGhB,CAAE,WAAW,EAA6C;AAC1D;AACA,EAAE,MAAM,mBAAA,SAA4B,WAAA,EAAoD;;AAIxF,KAAW,WAAW,CAAC,GAAG,IAAI,EAAS;AACvC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;AACpB;;AAEA;AACA,KAAW,IAAI,MAAM,GAAW;AAChC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,OAAO,IAAI,CAAC,OAAO;AAC3B;;AAEA,MAAM,MAAM,IAAK,GAAE,uBAAuB;AAC1C,MAAM,MAAM,OAAQ,GAAEC,gBAAW;AACjC,MAAM,MAAM,MAAO,GAAEC,SAAK,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC;AACnD,MAAM,IAAI,CAAC,OAAQ,GAAE,MAAM;;AAE3B,MAAM,OAAO,MAAM;AACnB;;AAEA;AACA;AACA;AACA,KAAW,MAAM,KAAK,CAAC,OAAO,EAA6B;AAC3D,MAAM,MAAM,QAAA,GAAW,IAAI,CAAC,aAAa;AACzC,MAAM,MAAM,QAAQ,EAAE,UAAU,EAAE;AAClC,MAAM,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;AACjC;AACA;;AAEA,EAAE,OAAO,mBAAoB;AAC7B;AACA;;ACtDA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,IAAI,EAA0B;AAC1D,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,IAAI;AACpB;;AAEA,EAAE,OAAOC,YAAQ,CAAC,QAAQ;AAC1B;;ACdO,MAAM,mBAAoB,GAAE,cAAc;AAC1C,MAAM,qBAAsB,GAAE,SAAS;;AAEvC,MAAM,sBAAuB,GAAE,YAAY;AAC3C,MAAM,wCAAyC,GAAE,8BAA8B;AAC/E,MAAM,sBAAuB,GAAE,YAAY;AAC3C,MAAM,8BAA+B,GAAE,oBAAoB;AAC3D,MAAM,8BAA+B,GAAE,oBAAoB;;AAE3D,MAAM,yBAA0B,GAAEC,oBAAgB,CAAC,eAAe,CAAC;;AAEnE,MAAM,uCAAwC,GAAEA,oBAAgB,CAAC,6BAA6B,CAAC;;AAE/F,MAAM,iCAAkC,GAAEA,oBAAgB,CAAC,uBAAuB,CAAC;;AAEnF,MAAM,2CAA4C,GAAEA,oBAAgB,CAAC,iCAAiC,CAAC;;ACX9G,MAAM,mBAAA,GAAsB,eAAe;;AAE3C;AACA;AACA;AACA;AACO,SAAS,oBAAoB,CAAC,OAAO,EAAsC;AAClF,EAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,yBAAyB,CAAE;AACrD;;AAEA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,OAAO,EAAW,MAAM,EAA0B;AACrF,EAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,KAAK,EAAS,OAAO,EAAiB;AACxE,EAAEC,6BAAwB,CAAC,KAAK,EAAE,mBAAmB,EAAE,OAAO,CAAC;AAC/D;;AAEA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,KAAK,EAA8B;AACvE,EAAE,OAAO,CAAC,KAAA,GAA8C,mBAAmB,CAAC;AAC5E;;AChCA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,IAAI,EAAyB;AACjE,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;AAChC,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,MAAM,EAAE,UAAW,EAAA,GAAI,IAAI;;AAE7B;AACA;AACA,EAAE,MAAM,OAAQ,GAAE,UAAU,CAACT,qCAAiB,CAAA,IAAK,UAAU,CAACD,iCAAa,CAAC;;AAE5E,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,OAAOW,uBAAkB,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAEC,cAAS,EAAE,CAAC;AAC5D;;ACrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,WAAW,EAAoC;AACnF,EAAE,MAAM,EAAE,UAAU,EAAE,UAAW,EAAA,GAAI,WAAW;;AAEhD,EAAE,MAAM,mBAAA,GAAsB,UAAA,GAAa,UAAU,CAAC,GAAG,CAAC,wCAAwC,CAAA,KAAM,GAAA,GAAM,KAAK;;AAEnH;AACA;AACA;AACA;AACA,EAAE,IAAI,UAAA,KAAeC,cAAU,CAAC,OAAO,EAAE;AACzC,IAAI,OAAO,IAAI;AACf;;AAEA,EAAE,IAAI,mBAAmB,EAAE;AAC3B,IAAI,OAAO,KAAK;AAChB;;AAEA;AACA,EAAE,MAAM,SAAA,GAAY,UAAA,GAAa,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAE,GAAE,SAAS;AACnF,EAAE,MAAM,GAAI,GAAE,SAAU,GAAEC,0CAAqC,CAAC,SAAS,CAAE,GAAE,SAAS;;AAEtF,EAAE,IAAI,GAAG,EAAE,OAAQ,KAAI,MAAM,EAAE;AAC/B,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI,GAAG,EAAE,OAAQ,KAAI,OAAO,EAAE;AAChC,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,OAAO,SAAS;AAClB;;ACJA;AACA;AACA;AACO,SAAS,aAAa,CAAC,QAAQ,EAAU,UAAU,EAAkB,IAAI,EAA6B;AAC7G;AACA;AACA,EAAE,MAAM,UAAW,GAAE,UAAU,CAACZ,4CAAwB,CAAA,IAAK,UAAU,CAACC,wCAAoB,CAAC;AAC7F,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,OAAO,wBAAwB,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAA,EAAM,EAAE,UAAU,CAAC;AACrF;;AAEA;AACA,EAAE,MAAM,QAAS,GAAE,UAAU,CAACY,sCAAkB,CAAC;AACjD,EAAE,MAAM,SAAU;AAClB,IAAI,OAAO,UAAU,CAACC,iCAA4B,CAAA,KAAM,QAAS;AACjE,IAAI,UAAU,CAACA,iCAA4B,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC;;AAEjE;AACA;AACA,EAAE,IAAI,QAAA,IAAY,CAAC,SAAS,EAAE;AAC9B,IAAI,OAAO,sBAAsB,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,QAAS,EAAC,CAAC;AACjE;;AAEA,EAAE,MAAM,mBAAA,GAAsB,UAAU,CAACC,qCAAgC,CAAE,KAAI,QAAS,GAAE,QAAS,GAAE,OAAO;;AAE5G;AACA;AACA,EAAE,MAAM,UAAW,GAAE,UAAU,CAACC,wCAAoB,CAAC;AACrD,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,OAAO;AACX,MAAM,GAAG,2BAA2B,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;AACnE,MAAM,EAAE,EAAE,KAAK;AACf,KAAK;AACL;;AAEA;AACA;AACA,EAAE,MAAM,eAAgB,GAAE,UAAU,CAACC,6CAAyB,CAAC;AAC/D,EAAE,IAAI,eAAe,EAAE;AACvB,IAAI,OAAO;AACX,MAAM,GAAG,2BAA2B,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC;AAC/E,MAAM,EAAE,EAAE,SAAS;AACnB,KAAK;AACL;;AAEA;AACA;AACA,EAAE,MAAM,WAAY,GAAE,UAAU,CAACC,yCAAqB,CAAC;AACvD,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,OAAO;AACX,MAAM,GAAG,2BAA2B,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC;AAC/E,MAAM,EAAE,EAAE,WAAW,CAAC,QAAQ,EAAE;AAChC,KAAK;AACL;;AAEA,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB,CAAC,IAAI,EAAiC;AAC1E,EAAE,MAAM,UAAA,GAAa,iBAAiB,CAAC,IAAI,CAAE,GAAE,IAAI,CAAC,UAAW,GAAE,EAAE;AACnE,EAAE,MAAM,IAAA,GAAO,WAAW,CAAC,IAAI,CAAE,GAAE,IAAI,CAAC,IAAK,GAAE,WAAW;AAC1D,EAAE,MAAM,IAAK,GAAE,WAAW,CAAC,IAAI,CAAC;;AAEhC,EAAE,OAAO,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;AAC9C;;AAEA,SAAS,sBAAsB,CAAC,EAAE,UAAU,EAAE,IAAK,EAAC,EAA6D;AACjH;AACA,EAAE,MAAM,eAAgB,GAAE,UAAU,CAACC,+CAA0C,CAAC;AAChF,EAAE,IAAI,OAAO,eAAgB,KAAI,QAAQ,EAAE;AAC3C,IAAI,OAAO;AACX,MAAM,EAAE,EAAE,IAAI;AACd,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM,MAAM,EAAE,CAAC,UAAU,CAACJ,qCAAgC,CAAA,MAA2B,QAAQ;AAC7F,KAAK;AACL;;AAEA;AACA,EAAE,IAAI,UAAU,CAACA,qCAAgC,CAAE,KAAI,QAAQ,EAAE;AACjE,IAAI,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU;AAC5D;;AAEA;AACA;AACA,EAAE,MAAM,SAAU,GAAE,UAAU,CAACK,yCAAqB,CAAC;;AAErD,EAAE,MAAM,WAAY,GAAE,SAAU,GAAE,SAAS,CAAC,QAAQ,EAAG,GAAE,IAAI;;AAE7D,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,MAAA,EAAQ;AAClD;;AAEA;AACO,SAAS,wBAAwB;AACxC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY;AAC5B,EAAE,UAAU;AACZ,EAAmB;AACnB,EAAE,MAAM,OAAA,GAAU,CAAC,MAAM,CAAC;;AAE1B,EAAE,QAAQ,IAAI;AACd,IAAI,KAAKd,YAAQ,CAAC,MAAM;AACxB,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC5B,MAAM;AACN,IAAI,KAAKA,YAAQ,CAAC,MAAM;AACxB,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC5B,MAAM;AACN;;AAEA;AACA,EAAE,IAAI,UAAU,CAAC,sBAAsB,CAAC,EAAE;AAC1C,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5B;;AAEA,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAA,KAAa,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC;;AAEvF,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,EAAE,GAAG,2BAA2B,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG;AACtF;;AAEA,EAAE,MAAM,0BAA2B,GAAE,UAAU,CAAC,2CAA2C,CAAC;;AAE5F;AACA,EAAE,MAAM,eAAA,GAAkB,CAAC,EAAA,UAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA;;AAEA;AACA;AACA,EAAA,MAAA,mBAAA,GAAA;AACA,MAAA,CAAA,EAAA,eAAA,CAAA,EAAA,EAAA,qCAAA,CAAA,0BAAA,CAAA,CAAA,CAAA;AACA,MAAA,eAAA;;AAEA;AACA,EAAA,MAAA,cAAA,GAAA,QAAA,IAAA,OAAA,KAAA,GAAA,GAAA,OAAA,GAAA,KAAA;;AAEA,EAAA,MAAA,IAAA,GAAA,EAAA;;AAEA,EAAA,IAAA,GAAA,EAAA;AACA,IAAA,IAAA,CAAA,GAAA,GAAA,GAAA;AACA;AACA,EAAA,IAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,KAAA;AACA;AACA,EAAA,IAAA,QAAA,EAAA;AACA,IAAA,IAAA,CAAA,eAAA,CAAA,GAAA,QAAA;AACA;;AAEA;AACA;AACA,EAAA,MAAA,oBAAA,GAAA,IAAA,KAAAA,YAAA,CAAA,MAAA,IAAA,IAAA,KAAAA,YAAA,CAAA,MAAA;;AAEA;AACA;AACA;AACA,EAAA,MAAA,MAAA,GAAA,UAAA,CAAAe,qCAAA,CAAA,IAAA,QAAA;AACA,EAAA,MAAA,YAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,UAAA,CAAA,MAAA,CAAA;;AAEA;AACA,EAAA,MAAA,sBAAA,GAAA,UAAA,CAAAN,qCAAA,CAAA,KAAA,QAAA;AACA,EAAA,MAAA,cAAA,GAAA,UAAA,CAAAI,+CAAA,CAAA;;AAEA,EAAA,MAAA,sBAAA;AACA,IAAA,CAAA,sBAAA,IAAA,cAAA,IAAA,IAAA,KAAA,oBAAA,IAAA,CAAA,YAAA,CAAA;;AAEA,EAAA,MAAA,EAAA,WAAA,EAAA,MAAA,EAAA,GAAA;AACA,MAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,cAAA;AACA,MAAA,2BAAA,CAAA,IAAA,EAAA,UAAA,CAAA;;AAEA,EAAA,OAAA;AACA,IAAA,EAAA,EAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,IAAA,WAAA;AACA,IAAA,MAAA;AACA,IAAA,IAAA;AACA,GAAA;AACA;;AAEA,SAAA,qCAAA,CAAA,IAAA,EAAA;AACA,EAAA,IAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AACA,IAAA,MAAA,MAAA,GAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,EAAA;;AAEA;AACA,IAAA,IAAA,MAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,MAAA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,KAAA,MAAA;AACA;AACA,MAAA,OAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA;;AAEA;AACA,SAAA,eAAA;AACA,EAAA,UAAA;AACA,EAAA,IAAA;AACA;;AAMA,CAAA;AACA;AACA;AACA,EAAA,MAAA,UAAA,GAAA,UAAA,CAAAG,wCAAA,CAAA;AACA;AACA;AACA,EAAA,MAAA,OAAA,GAAA,UAAA,CAAAvB,qCAAA,CAAA,IAAA,UAAA,CAAAD,iCAAA,CAAA;AACA;AACA,EAAA,MAAA,SAAA,GAAA,UAAA,CAAAyB,mCAAA,CAAA;;AAEA,EAAA,MAAA,SAAA,GAAA,OAAA,OAAA,KAAA,QAAA,GAAArB,aAAA,CAAA,OAAA,CAAA,GAAA,SAAA;AACA,EAAA,MAAA,GAAA,GAAA,SAAA,GAAAC,0BAAA,CAAA,SAAA,CAAA,GAAA,SAAA;AACA,EAAA,MAAA,KAAA,GAAA,SAAA,EAAA,MAAA,IAAA,SAAA;AACA,EAAA,MAAA,QAAA,GAAA,SAAA,EAAA,IAAA,IAAA,SAAA;;AAEA,EAAA,IAAA,OAAA,SAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,GAAA,EAAA,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAAG,YAAA,CAAA,MAAA,IAAA,OAAA,UAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,EAAA,OAAA,EAAAkB,6BAAA,CAAA,UAAA,CAAA,EAAA,GAAA,EAAA,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,KAAA,EAAA;AACA;;AAEA,EAAA,IAAA,SAAA,EAAA;AACA,IAAA,OAAA,EAAA,OAAA,EAAA,GAAA,EAAA,GAAA,EAAA,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,KAAA,EAAA;AACA;;AAEA;AACA,EAAA,IAAA,OAAA,UAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,EAAA,OAAA,EAAAA,6BAAA,CAAA,UAAA,CAAA,EAAA,GAAA,EAAA,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,KAAA,EAAA;AACA;;AAEA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,GAAA,EAAA,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,KAAA,EAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,2BAAA;AACA,EAAA,YAAA;AACA,EAAA,UAAA;AACA,EAAA,cAAA,GAAA,QAAA;AACA;;AAGA,CAAA;AACA,EAAA,MAAA,MAAA,GAAA,CAAA,UAAA,CAAAT,qCAAA,CAAA,MAAA,cAAA;AACA,EAAA,MAAA,WAAA,GAAA,UAAA,CAAAI,+CAAA,CAAA;;AAEA,EAAA,IAAA,WAAA,IAAA,OAAA,WAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA;AACA,MAAA,WAAA;AACA,MAAA,MAAA;AACA,KAAA;AACA;;AAEA,EAAA,OAAA,EAAA,WAAA,EAAA,YAAA,EAAA,MAAA,EAAA;AACA;;ACjT3B;AACA;AACA;AACA;AACO,SAAS,uCAAuC,CAAC,MAAM,EAAgB;AAC9E,EAAE,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,QAAQ,KAAK;AAC5C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM;AACN;;AAEA;AACA;AACA;AACA;AACA;;AAEA,IAAI,MAAM,QAAS,GAAEM,eAAU,CAAC,QAAQ,CAAC;AACzC,IAAI,MAAM,UAAA,GAAa,QAAQ,CAAC,IAAI;AACpC,IAAI,MAAM,MAAO,GAAE,UAAU,CAACV,qCAAgC,CAAC;;AAE/D,IAAI,MAAM,EAAE,WAAA,KAAgB,WAAW,CAAC,QAAQ,CAAA,GAAI,oBAAoB,CAAC,QAAQ,CAAE,GAAE,EAAE,WAAW,EAAE,WAAW;AAC/G,IAAI,IAAI,MAAA,KAAW,KAAM,IAAG,WAAW,EAAE;AACzC,MAAM,GAAG,CAAC,WAAY,GAAE,WAAW;AACnC;;AAEA;AACA;AACA;AACA,IAAI,IAAIW,oBAAe,EAAE,EAAE;AAC3B,MAAM,MAAM,OAAQ,GAAE,mBAAmB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;AACjE,MAAM,GAAG,CAAC,OAAQ,GAAE,OAAQ,IAAG,SAAU,GAAE,SAAU,GAAE,MAAM,CAAC,OAAO,CAAC;AACtE;AACA,GAAG,CAAC;AACJ;;ACpCA;AACA;AACA;AACO,SAAS,aAAa,GAAqB;AAClD,EAAE,OAAOrB,SAAK,CAAC,aAAa,EAAE;AAC9B;;ACNA;AACA;AACA;AACA;AACA;AACO,MAAM,WAAY,IAAiB,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA,CAAA;;ACF1C;AACA;AACA;AACO,SAAS,cAAc,CAAC;AAC/B,EAAE,GAAG;AACL,EAAE,OAAO;AACT;;AAGA,EAAe;AACf;AACA,EAAE,MAAM,SAAU,GAAE,GAAI,GAAEsB,gDAA2C,CAAC,GAAG,CAAE,GAAE,SAAS;;AAEtF,EAAE,MAAM,cAAe,GAAE,IAAIC,iBAAU,EAAE;;AAEzC,EAAE,MAAM,iBAAA,GAAoB,SAAA,GAAY,cAAc,CAAC,GAAG,CAAC,sBAAsB,EAAE,SAAS,CAAA,GAAI,cAAc;;AAE9G;AACA;AACA,EAAE,OAAO,OAAA,KAAY,KAAA,GAAQ,iBAAiB,CAAC,GAAG,CAAC,wCAAwC,EAAE,GAAG,CAAA,GAAI,iBAAiB;AACrH;;ACvBA,MAAM,aAAc,GAAE,IAAI,GAAG,EAAwB;;AAErD;AACO,SAAS,uBAAuB,GAA2B;AAClE,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;AAClC;;AAEA;AACO,SAAS,UAAU,CAAC,OAAO,EAA8B;AAChE,EAAE,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC;AAC5B;;ACeA;AACA;AACA;AACO,MAAM,gBAAiB,SAAQC,2BAAqB,CAAA;AAC3D;;AAGA,GAAS,WAAW,GAAG;AACvB,IAAI,KAAK,EAAE;AACX,IAAI,UAAU,CAAC,kBAAkB,CAAC;;AAElC;AACA,IAAI,IAAI,CAAC,qBAAsB,GAAE,IAAIC,WAAM,CAAkB,GAAG,CAAC;AACjE;;AAEA;AACA;AACA;AACA,GAAS,MAAM,CAAC,OAAO,EAAW,OAAO,EAAW,MAAM,EAAuB;AACjF,IAAI,IAAIC,0BAAmB,CAAC,OAAO,CAAC,EAAE;AACtC,MAAM,eAAeC,WAAM,CAAC,GAAG,CAAC,2EAA2E,CAAC;AAC5G,MAAM;AACN;;AAEA,IAAI,MAAM,aAAa3B,SAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AAC7C,IAAI,MAAM,MAAM,UAAA,IAAc,aAAa,CAAC,UAAU,CAAC;;AAEvD,IAAI,MAAM,uBAAwB,GAAEK,cAAS,EAAE,EAAE,UAAU,EAAE,EAAE,uBAAuB;AACtF,IAAI,IAAI,CAAC,0BAA0B,CAAC,GAAG,EAAE,uBAAuB,EAAE,IAAI,CAAC,qBAAqB,CAAC,EAAE;AAC/F,MAAM,WAAY;AAClB,QAAQsB,WAAM,CAAC,GAAG;AAClB,UAAU,+FAA+F;AACzG,UAAU,GAAG;AACb,SAAS;AACT,MAAM;AACN;;AAEA,IAAI,MAAM,qBAAsB,GAAE,kBAAkB,CAAC,OAAO,CAAC;AAC7D,IAAI,IAAI,OAAQ,GAAEC,eAAW,CAAC,UAAU,CAAC,OAAO,CAAE,IAAGA,eAAW,CAAC,aAAa,CAAC,EAAE,CAAC;;AAElF,IAAI,MAAM,EAAE,sBAAsB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAA,EAAU,GAAE,gBAAgB,CAAC,OAAO,CAAC;;AAE1F,IAAI,IAAI,qBAAqB,EAAE;AAC/B,MAAM,MAAM,cAAe,GAAEC,uBAAkB,CAAC,qBAAqB,CAAC;;AAEtE,MAAM,IAAI,cAAc,EAAE;AAC1B,QAAQ,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;AACjE,UAAU,OAAA,GAAU,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAM,EAAC,CAAC;AACpD,SAAS,CAAC;AACV;AACA;;AAEA,IAAI,IAAI,sBAAsB,EAAE;AAChC,MAAM,OAAA,GAAU,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAU,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK;AAClG,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAAC,8BAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,EAAA,KAAA,EAAA,QAAA,EAAA,CAAA;AACA;AACA,QAAA,OAAA,CAAA;AACA,OAAA,EAAA,OAAA,CAAA;AACA;;AAEA;AACA,IAAA,IAAA,OAAA,IAAA,OAAA,KAAAC,mBAAA,EAAA;AACA,MAAA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,mBAAA,EAAAC,8BAAA,CAAA,OAAA,EAAA,MAAA,EAAA,OAAA,CAAA,CAAA;AACA;;AAEA,IAAA,KAAA,CAAA,MAAA,CAAAJ,eAAA,CAAA,UAAA,CAAA,OAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,GAAA,OAAA,CAAA,OAAA,EAAA,OAAA,EAAA,MAAA,EAAA;AACA,IAAA,MAAA,sBAAA,GAAA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,mBAAA,CAAA;AACA,IAAA,MAAA,OAAA,GAAA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA,qBAAA,CAAA;;AAEA,IAAA,MAAA,WAAA,GAAA;AACA,QAAA,KAAA,CAAA,OAAA,CAAA,sBAAA;AACA,UAAA,sBAAA,CAAA,CAAA;AACA,UAAA;AACA,QAAA,SAAA;;AAEA;AACA;AACA,IAAA,OAAA,qBAAA,CAAA,8BAAA,CAAA,OAAA,EAAA,EAAA,WAAA,EAAA,OAAA,EAAA,CAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,GAAA,MAAA,GAAA;AACA,IAAA,OAAA,CAAA,mBAAA,EAAA,qBAAA,CAAA;AACA;AACA;;AAEA,MAAA,sBAAA;AACA,EAAA,+FAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAA,0BAAA;AACA,EAAA,GAAA;AACA,EAAA,uBAAA;AACA,EAAA,WAAA;AACA,EAAA;AACA,EAAA,IAAA,OAAA,GAAA,KAAA,QAAA,IAAA,CAAA,uBAAA,EAAA;AACA,IAAA,OAAA,IAAA;AACA;;AAEA,EAAA,MAAA,cAAA,GAAA,WAAA,EAAA,GAAA,CAAA,GAAA,CAAA;AACA,EAAA,IAAA,cAAA,KAAA,SAAA,EAAA;AACA,IAAA,WAAA,IAAA,CAAA,cAAA,IAAAD,WAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,GAAA,CAAA;AACA,IAAA,OAAA,cAAA;AACA;;AAEA,EAAA,MAAA,QAAA,GAAAM,6BAAA,CAAA,GAAA,EAAA,uBAAA,CAAA;AACA,EAAA,WAAA,EAAA,GAAA,CAAA,GAAA,EAAA,QAAA,CAAA;;AAEA,EAAA,WAAA,IAAA,CAAA,QAAA,IAAAN,WAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,GAAA,CAAA;AACA,EAAA,OAAA,QAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,gBAAA;AACA,EAAA,OAAA;AACA,EAAA,OAAA,GAAA,EAAA;AACA;;AAKA,CAAA;AACA,EAAA,MAAA,IAAA,GAAA3B,SAAA,CAAA,OAAA,CAAA,OAAA,CAAA;;AAEA;AACA;AACA,EAAA,IAAA,IAAA,EAAA,WAAA,EAAA,CAAA,QAAA,EAAA;AACA,IAAA,MAAA,WAAA,GAAA,IAAA,CAAA,WAAA,EAAA;AACA,IAAA,MAAA,sBAAA,GAAAkC,sCAAA,CAAA,IAAA,CAAA;;AAEA,IAAA,OAAA;AACA,MAAA,sBAAA;AACA,MAAA,OAAA,EAAA,WAAA,CAAA,OAAA;AACA,MAAA,MAAA,EAAA,SAAA;AACA,MAAA,OAAA,EAAA,mBAAA,CAAA,WAAA,CAAA;AACA,KAAA;AACA;;AAEA;AACA,EAAA,IAAA,IAAA,EAAA;AACA,IAAA,MAAA,WAAA,GAAA,IAAA,CAAA,WAAA,EAAA;AACA,IAAA,MAAA,sBAAA,GAAAA,sCAAA,CAAA,IAAA,CAAA;;AAEA,IAAA,OAAA;AACA,MAAA,sBAAA;AACA,MAAA,OAAA,EAAA,WAAA,CAAA,OAAA;AACA,MAAA,MAAA,EAAA,WAAA,CAAA,MAAA;AACA,MAAA,OAAA,EAAA,mBAAA,CAAA,WAAA,CAAA;AACA,KAAA;AACA;;AAEA;AACA;AACA,EAAA,MAAA,KAAA,GAAA,OAAA,CAAA,KAAA,IAAA,oBAAA,CAAA,OAAA,CAAA,EAAA,KAAA,IAAAC,oBAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAA,OAAA,CAAA,MAAA,IAAA9B,cAAA,EAAA;;AAEA,EAAA,MAAA,kBAAA,GAAA,KAAA,CAAA,qBAAA,EAAA;AACA,EAAA,MAAA,sBAAA,GAAA,MAAA,GAAA+B,uCAAA,CAAA,MAAA,EAAA,KAAA,CAAA,GAAA,SAAA;AACA,EAAA,OAAA;AACA,IAAA,sBAAA;AACA,IAAA,OAAA,EAAA,kBAAA,CAAA,OAAA;AACA,IAAA,MAAA,EAAA,kBAAA,CAAA,iBAAA;AACA,IAAA,OAAA,EAAA,kBAAA,CAAA,OAAA;AACA,GAAA;AACA;;AAEA,SAAA,8BAAA;AACA,EAAA,GAAA;AACA,EAAA,EAAA,WAAA,EAAA,OAAA,EAAA;AACA,EAAA;AACA,EAAA,MAAA,kBAAA,GAAAC,kCAAA,CAAA,WAAA,EAAA,OAAA,CAAA;;AAEA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,OAAA,EAAA,GAAA,EAAA,GAAA,kBAAA;;AAEA;AACA;AACA,EAAA,IAAA,CAAA,YAAA,EAAA;AACA,IAAA,OAAA,GAAA;AACA;;AAEA,EAAA,MAAA,WAAA,GAAA,yBAAA,CAAA;AACA,IAAA,OAAA;AACA,IAAA,MAAA,EAAA,YAAA;AACA,IAAA,OAAA;AACA,IAAA,GAAA;AACA,GAAA,CAAA;;AAEA,EAAA,OAAArC,SAAA,CAAA,cAAA,CAAA,GAAA,EAAA,WAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,yBAAA;AACA,EAAA,GAAA;AACA,EAAA,OAAA;AACA,EAAA,QAAA;AACA,EAAA;AACA,EAAA,MAAA,kBAAA,GAAA,qBAAA,CAAA,8BAAA,CAAA,GAAA,EAAA,OAAA,CAAA,CAAA;;AAEA,EAAA,OAAAsC,WAAA,CAAA,IAAA,CAAA,kBAAA,EAAA,QAAA,CAAA;AACA;;AAEA,SAAA,qBAAA,CAAA,GAAA,EAAA;AACA;AACA,EAAA,MAAA,MAAA,GAAA,oBAAA,CAAA,GAAA,CAAA;AACA,EAAA,MAAA,SAAA,GAAA;AACA;AACA;AACA,IAAA,KAAA,EAAA,MAAA,GAAA,MAAA,CAAA,KAAA,GAAAH,oBAAA,EAAA,CAAA,KAAA,EAAA;AACA,IAAA,cAAA,EAAA,MAAA,GAAA,MAAA,CAAA,cAAA,GAAAI,sBAAA,EAAA;AACA,GAAA;;AAEA,EAAA,OAAA,kBAAA,CAAA,GAAA,EAAA,SAAA,CAAA;AACA;;AAEA;AACA,SAAA,kBAAA,CAAA,OAAA,EAAA;AACA,EAAA,IAAA;AACA,IAAA,MAAA,OAAA,GAAA,CAAA,OAAA,GAAA,qBAAA,CAAA;AACA,IAAA,OAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,OAAA;AACA,GAAA,CAAA,MAAA;AACA,IAAA,OAAA,SAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,aAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,QAAA,GAAAnB,eAAA,CAAA,IAAA,CAAA,CAAA,IAAA;AACA;AACA;AACA,EAAA,MAAA,YAAA,GAAA,QAAA,CAAA1B,qCAAA,CAAA,IAAA,QAAA,CAAAD,iCAAA,CAAA;AACA,EAAA,IAAA,OAAA,YAAA,KAAA,QAAA,EAAA;AACA,IAAA,OAAA,YAAA;AACA;;AAEA;AACA,EAAA,MAAA,aAAA,GAAA,IAAA,CAAA,WAAA,EAAA,CAAA,UAAA,EAAA,GAAA,CAAA,sBAAA,CAAA;AACA,EAAA,IAAA,aAAA,EAAA;AACA,IAAA,OAAA,aAAA;AACA;;AAEA,EAAA,OAAA,SAAA;AACA;;AAEA,SAAA,yBAAA,CAAA;AACA,EAAA,MAAA;AACA,EAAA,OAAA;AACA,EAAA,OAAA;AACA,EAAA,GAAA;AACA;;AAKA,EAAA;AACA;AACA,EAAA,MAAA,UAAA,GAAA,cAAA,CAAA;AACA,IAAA,GAAA;AACA,IAAA,OAAA;AACA,GAAA,CAAA;;AAEA,EAAA,MAAA,WAAA,GAAA;AACA,IAAA,OAAA;AACA,IAAA,MAAA;AACA,IAAA,QAAA,EAAA,IAAA;AACA,IAAA,UAAA,EAAA,OAAA,GAAAa,cAAA,CAAA,OAAA,GAAAA,cAAA,CAAA,IAAA;AACA,IAAA,UAAA;AACA,GAAA;;AAEA,EAAA,OAAA,WAAA;AACA;;ACrS7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAI,OAAO,EAA4B,QAAQ,EAAwB;AAChG,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;;AAE5B,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAA,EAAmB,GAAE,OAAO;;AAExD;AACA,EAAE,MAAM,OAAQ,GAAE,oBAAoB,CAAI,gBAAgB,CAAC;;AAE3D,EAAE,OAAO,OAAO,CAAC,MAAM;AACvB,IAAI,MAAM,SAAA,GAAY,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC;AACzE,IAAI,MAAM,cAAA,GAAiB,OAAO,CAAC,YAAA,IAAgB,CAACN,SAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5E,IAAI,MAAM,GAAI,GAAE,cAAe,GAAEwC,sBAAe,CAAC,SAAS,CAAE,GAAE,SAAS;;AAEvE,IAAI,MAAM,WAAY,GAAE,cAAc,CAAC,OAAO,CAAC;;AAE/C,IAAI,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,IAAA,IAAQ;AAClE,MAAM,OAAOC,yBAAoB;AACjC,QAAQ,MAAM,QAAQ,CAAC,IAAI,CAAC;AAC5B,QAAQ,MAAM;AACd;AACA,UAAU,IAAIrB,eAAU,CAAC,IAAI,CAAC,CAAC,MAAA,KAAW,SAAS,EAAE;AACrD,YAAY,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEsB,kBAAc,CAAC,KAAM,EAAC,CAAC;AAC1D;AACA,SAAS;AACT,QAAQ,MAAM,IAAI,CAAC,GAAG,EAAE;AACxB,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAK;AACL,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;;AAE5B,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAA,EAAmB,GAAE,OAAO;;AAExD;AACA,EAAE,MAAM,OAAQ,GAAE,oBAAoB,CAAI,gBAAgB,CAAC;;AAE3D,EAAE,OAAO,OAAO,CAAC,MAAM;AACvB,IAAI,MAAM,SAAA,GAAY,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC;AACzE,IAAI,MAAM,cAAA,GAAiB,OAAO,CAAC,YAAA,IAAgB,CAAC1C,SAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5E,IAAI,MAAM,GAAI,GAAE,cAAe,GAAEwC,sBAAe,CAAC,SAAS,CAAE,GAAE,SAAS;;AAEvE,IAAI,MAAM,WAAY,GAAE,cAAc,CAAC,OAAO,CAAC;;AAE/C,IAAI,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,IAAA,IAAQ;AAClE,MAAM,OAAOC,yBAAoB;AACjC,QAAQ,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;AAC9C,QAAQ,MAAM;AACd;AACA,UAAU,IAAIrB,eAAU,CAAC,IAAI,CAAC,CAAC,MAAA,KAAW,SAAS,EAAE;AACrD,YAAY,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEsB,kBAAc,CAAC,KAAM,EAAC,CAAC;AAC1D;AACA,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,OAAO,EAAkC;AAC3E,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;;AAE5B,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAA,EAAmB,GAAE,OAAO;;AAExD;AACA,EAAE,MAAM,OAAQ,GAAE,oBAAoB,CAAO,gBAAgB,CAAC;;AAE9D,EAAE,OAAO,OAAO,CAAC,MAAM;AACvB,IAAI,MAAM,SAAA,GAAY,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,gBAAgB,CAAC;AACzE,IAAI,MAAM,cAAA,GAAiB,OAAO,CAAC,YAAA,IAAgB,CAAC1C,SAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5E,IAAI,MAAM,GAAI,GAAE,cAAe,GAAEwC,sBAAe,CAAC,SAAS,CAAE,GAAE,SAAS;;AAEvE,IAAI,MAAM,WAAY,GAAE,cAAc,CAAC,OAAO,CAAC;;AAE/C,IAAI,MAAM,IAAA,GAAO,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC;;AAEzD,IAAI,OAAO,IAAI;AACf,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAI,IAAI,EAAe,QAAQ,EAA0B;AACvF,EAAE,MAAM,wBAAyB,GAAE,IAAK,GAAExC,SAAK,CAAC,OAAO,CAACsC,WAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAA,GAAItC,SAAK,CAAC,UAAU,CAACsC,WAAO,CAAC,MAAM,EAAE,CAAC;AACpH,EAAE,OAAOA,WAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,MAAM,QAAQ,CAACH,oBAAe,EAAE,CAAC,CAAC;AAClF;;AAEA,SAAS,SAAS,GAAW;AAC7B,EAAE,MAAM,MAAA,GAAS9B,cAAS,EAAgC;AAC1D,EAAE,OAAO,MAAM,EAAE,MAAA,IAAUL,SAAK,CAAC,SAAS,CAAC,uBAAuB,EAAED,gBAAW,CAAC;AAChF;;AAEA,SAAS,cAAc,CAAC,OAAO,EAAyC;AACxE,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,KAAM,EAAA,GAAI,OAAO;;AAE5D;AACA,EAAE,MAAM,cAAA,GAAiB,OAAO,SAAU,KAAI,QAAS,GAAE,6BAA6B,CAAC,SAAS,CAAA,GAAI,SAAS;;AAE7G,EAAE,OAAO;AACT,IAAI,UAAU,EAAE;AAChB,QAAQ;AACR,UAAU,CAACU,iCAA4B,GAAG,EAAE;AAC5C,UAAU,GAAG,UAAU;AACvB;AACA,QAAQ,UAAU;AAClB,IAAI,IAAI;AACR,IAAI,KAAK;AACT,IAAI,SAAS,EAAE,cAAc;AAC7B,GAAG;AACH;;AAEA,SAAS,6BAA6B,CAAC,SAAS,EAAkB;AAClE,EAAE,MAAM,IAAA,GAAO,SAAA,GAAY,UAAU;AACrC,EAAE,OAAO,IAAK,GAAE,YAAY,IAAA,GAAO,SAAS;AAC5C;;AAEA,SAAS,UAAU,CAAC,KAAK,EAAqB,gBAAgB,EAAgC;AAC9F,EAAE,MAAM,GAAI,GAAE,kBAAkB,CAAC,KAAK,CAAC;AACvC,EAAE,MAAM,aAAaT,SAAK,CAAC,OAAO,CAAC,GAAG,CAAC;;AAEvC;AACA;AACA,EAAE,IAAI,CAAC,UAAU,EAAE;AACnB,IAAI,OAAO,GAAG;AACd;;AAEA;AACA,EAAE,IAAI,CAAC,gBAAgB,EAAE;AACzB,IAAI,OAAO,GAAG;AACd;;AAEA;;AAEA;AACA;AACA;AACA,EAAE,MAAM,iBAAiBA,SAAK,CAAC,UAAU,CAAC,GAAG,CAAC;;AAE9C,EAAE,MAAM,EAAE,MAAM,EAAE,OAAA,EAAU,GAAE,UAAU,CAAC,WAAW,EAAE;AACtD,EAAE,MAAM,OAAQ,GAAE,mBAAmB,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;;AAE/D;AACA;AACA,EAAE,MAAM,QAAS,GAAE2C,gBAAW,CAAC,UAAU,CAAC;AAC1C,EAAE,MAAM,GAAI,GAAET,sCAAiC,CAAC,QAAQ,CAAC;;AAEzD,EAAE,MAAM,UAAA,GAAa,cAAc,CAAC;AACpC,IAAI,GAAG;AACP,IAAI,OAAO;AACX,GAAG,CAAC;;AAEJ,EAAE,MAAM,WAAW,GAAgB;AACnC,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,UAAU,EAAE,OAAA,GAAU5B,cAAU,CAAC,OAAQ,GAAEA,cAAU,CAAC,IAAI;AAC9D,IAAI,UAAU;AACd,GAAG;;AAEH,EAAE,MAAM,kBAAmB,GAAEN,SAAK,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC;;AAE9E,EAAE,OAAO,kBAAkB;AAC3B;;AAEA,SAAS,kBAAkB,CAAC,KAAK,EAAmB;AACpD,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,MAAM,GAAI,GAAE,mBAAmB,CAAC,KAAK,CAAC;AAC1C,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,OAAO,GAAG;AAChB;AACA;;AAEA,EAAE,OAAOsC,WAAO,CAAC,MAAM,EAAE;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAI,OAAO,EAA2C,QAAQ,EAAc;AACzG,EAAE,OAAO,yBAAyB,CAACA,WAAO,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC;AACvE;;AAEA;AACA;AACA;AACA;AACO,SAAS,uBAAuB;AACvC,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAyF;AACzF,EAAE,MAAM,GAAI,GAAE,mBAAmB,CAAC,KAAK,CAAC;AACxC,EAAE,MAAM,IAAK,GAAE,GAAI,IAAGtC,SAAK,CAAC,OAAO,CAAC,GAAG,CAAC;;AAExC,EAAE,MAAM,YAAA,GAAe,IAAA,GAAO4C,uBAAkB,CAAC,IAAI,CAAE,GAAEC,6BAAwB,CAAC,KAAK,CAAC;;AAExF,EAAE,MAAM,yBAAyB;AACjC,MAAMX,sCAAiC,CAAC,IAAI;AAC5C,MAAME,uCAAkC,CAAC,MAAM,EAAE,KAAK,CAAC;AACvD,EAAE,OAAO,CAAC,sBAAsB,EAAE,YAAY,CAAC;AAC/C;;AAEA,SAAS,oBAAoB,CAAI,UAAU,EAAkE;AAC7G,EAAE,OAAO,eAAe;AACxB,MAAM,CAAC,QAAQ,KAAc;AAC7B,QAAQ,OAAO,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC;AACnD;AACA,MAAM,CAAC,QAAQ,KAAc,QAAQ,EAAE;AACvC;;ACzRA;AACO,SAAS,eAAe,CAAI,QAAQ,EAAc;AACzD,EAAE,MAAM,GAAI,GAAEU,sBAAmB,CAACR,WAAO,CAAC,MAAM,EAAE,CAAC;AACnD,EAAE,OAAOA,WAAO,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC;AACpC;;ACHA;AACO,SAAS,sBAAsB,CAAC,MAAM,EAAgB;AAC7D,EAAE,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,SAAS;AACxC,IAAI,MAAM,IAAA,GAAO,aAAa,EAAE;AAChC;AACA;AACA,IAAI,IAAI,CAAC,IAAK,IAAG,KAAK,CAAC,IAAA,KAAS,aAAa,EAAE;AAC/C,MAAM;AACN;;AAEA;AACA,IAAI,KAAK,CAAC,QAAA,GAAW;AACrB,MAAM,KAAK,EAAEM,uBAAkB,CAAC,IAAI,CAAC;AACrC,MAAM,GAAG,KAAK,CAAC,QAAQ;AACvB,KAAK;;AAEL,IAAI,MAAM,QAAS,GAAED,gBAAW,CAAC,IAAI,CAAC;;AAEtC,IAAI,KAAK,CAAC,qBAAA,GAAwB;AAClC,MAAM,sBAAsB,EAAET,sCAAiC,CAAC,QAAQ,CAAC;AACzE,MAAM,GAAG,KAAK,CAAC,qBAAqB;AACpC,KAAK;;AAEL,IAAI,OAAO,KAAK;AAChB,GAAG,CAAC;AACJ;;ACnBA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC;AAC7B,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,MAAM;AACR,CAAC,GAAoD,EAAE,EAAuB;AAC9E,EAAE,IAAI,GAAI,GAAE,CAAC,KAAM,IAAG,mBAAmB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;;AAEzE,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,MAAM,EAAE,KAAM,EAAA,GAAIa,4BAAuB,CAAC,IAAI,CAAC;AACnD;AACA,IAAI,GAAA,GAAM,CAAC,KAAM,IAAG,mBAAmB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;AAChG;;AAEA,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAuB,EAAA,GAAI,gBAAgB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,MAAA,EAAQ,CAAC;;AAEvG,EAAE,OAAO;AACT,IAAI,cAAc,EAAEf,8BAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AACvE,IAAI,OAAO,EAAEV,gDAA2C,CAAC,sBAAsB,CAAC;AAChF,GAAG;AACH;;AClBA;AACA;AACA;AACA;AACO,SAAS,2CAA2C,GAAS;AACpE,EAAE,SAAS,SAAS,GAAkB;AACtC,IAAI,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;AACpC,IAAI,MAAM,MAAO,GAAE,oBAAoB,CAAC,GAAG,CAAC;;AAE5C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,MAAM;AACnB;;AAEA;AACA;AACA,IAAI,OAAO;AACX,MAAM,KAAK,EAAE0B,2BAAsB,EAAE;AACrC,MAAM,cAAc,EAAEC,6BAAwB,EAAE;AAChD,KAAK;AACL;;AAEA,EAAE,SAAS,SAAS,CAAI,QAAQ,EAA0B;AAC1D,IAAI,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;;AAEpC;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM;AACvC,MAAM,OAAO,QAAQ,CAAC,eAAe,EAAE,CAAC;AACxC,KAAK,CAAC;AACN;;AAEA,EAAE,SAAS,YAAY,CAAI,KAAK,EAAS,QAAQ,EAA0B;AAC3E,IAAI,MAAM,GAAA,GAAM,mBAAmB,CAAC,KAAK,CAAA,IAAK,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;;AAElE;AACA;AACA;AACA,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE,MAAM;AAC1F,MAAM,OAAO,QAAQ,CAAC,KAAK,CAAC;AAC5B,KAAK,CAAC;AACN;;AAEA,EAAE,SAAS,kBAAkB,CAAI,QAAQ,EAAmC;AAC5E,IAAI,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;;AAEpC;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,uCAAuC,EAAE,IAAI,CAAC,EAAE,MAAM;AAC/F,MAAM,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;AAC1C,KAAK,CAAC;AACN;;AAEA,EAAE,SAAS,qBAAqB,CAAI,cAAc,EAAS,QAAQ,EAAmC;AACtG,IAAI,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;;AAEpC;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,2CAA2C,EAAE,cAAc,CAAC,EAAE,MAAM;AAC7G,MAAM,OAAO,QAAQ,CAAC,iBAAiB,EAAE,CAAC;AAC1C,KAAK,CAAC;AACN;;AAEA,EAAE,SAAS,eAAe,GAAU;AACpC,IAAI,OAAO,SAAS,EAAE,CAAC,KAAK;AAC5B;;AAEA,EAAE,SAAS,iBAAiB,GAAU;AACtC,IAAI,OAAO,SAAS,EAAE,CAAC,cAAc;AACrC;;AAEA,EAAEC,4BAAuB,CAAC;AAC1B,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB;AACA;AACA,IAAI,cAAc,EAAE,cAAe;AACnC,GAAG,CAAC;AACJ;;ACnGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB;AACvC,EAAE,mBAAmB;AACrB,EAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE,MAAM,oBAAqB,SAAQ,mBAAoB,CAAA;AACzD,KAAW,WAAW,CAAC,GAAG,IAAI,EAAa;AAC3C,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;AACpB,MAAM,UAAU,CAAC,sBAAsB,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,KAAW,IAAI;AACf,MAAM,OAAO;AACb,MAAM,EAAE;AACR,MAAM,OAAO;AACb,MAAM,GAAG;AACT,MAAqB;AACrB,MAAM,MAAM,aAAc,GAAE,oBAAoB,CAAC,OAAO,CAAC;AACzD,MAAM,MAAM,eAAe,aAAa,EAAE,KAAM,IAAGf,oBAAe,EAAE;AACpE,MAAM,MAAM,wBAAwB,aAAa,EAAE,cAAe,IAAGI,sBAAiB,EAAE;;AAExF,MAAM,MAAM,wBAAyB,GAAE,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAE,KAAI,IAAI;AACzG,MAAM,MAAM,QAAQ,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAE;AACxE,MAAM,MAAM,iBAAiB,OAAO,CAAC,QAAQ,CAAC,2CAA2C,CAAE;;AAE3F,MAAM,MAAM,kBAAkB,KAAA,IAAS,YAAY,CAAC,KAAK,EAAE;AAC3D,MAAM,MAAM,iBAAkB;AAC9B,QAAQ,cAAe,KAAI,wBAAA,GAA2B,qBAAqB,CAAC,KAAK,EAAC,GAAI,qBAAqB,CAAC;AAC5G,MAAM,MAAM,MAAO,GAAE,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,iBAAA,EAAmB;;AAElF,MAAM,MAAM,OAAO,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC;;AAEtD;AACA,MAAM,MAAM,OAAO;AACnB,SAAS,WAAW,CAAC,uCAAuC;AAC5D,SAAS,WAAW,CAAC,iCAAiC;AACtD,SAAS,WAAW,CAAC,2CAA2C,CAAC;;AAEjE,MAAM,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC;;AAE9C,MAAM,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;AACnD;AACA;;AAEA,EAAE,OAAO,oBAAqB;AAC9B;;AC7DA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,KAAK,EAA8B;AACzE,EAAE,MAAM,OAAO,GAAY,IAAI,GAAG,EAAoB;;AAEtD,EAAE,KAAK,MAAM,IAAK,IAAG,KAAK,EAAE;AAC5B,IAAI,6BAA6B,CAAC,OAAO,EAAE,IAAI,CAAC;AAChD;;AAEA,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;AACxD,IAAI,OAAO,QAAQ;AACnB,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,IAAI,EAAoC;AACzE,EAAE,MAAM,cAAe,GAAE,IAAI,CAAC,UAAU,CAAC,0CAA0C,CAAE,KAAI,IAAI;AAC7F;AACA;AACA,EAAE,OAAO,CAAC,cAAe,GAAE,eAAe,CAAC,IAAI,CAAE,GAAE,SAAS;AAC5D;;AAEA,SAAS,6BAA6B,CAAC,OAAO,EAAW,IAAI,EAAsB;AACnF,EAAE,MAAM,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;AACtC,EAAE,MAAM,QAAS,GAAE,gBAAgB,CAAC,IAAI,CAAC;;AAEzC,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAC,EAAG,CAAC;AAC3D,IAAI;AACJ;;AAEA;AACA;AACA,EAAE,MAAM,aAAa,qBAAqB,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC7D,EAAE,MAAM,IAAK,GAAE,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAC,EAAG,CAAC;AAClF,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAChC;;AAEA,SAAS,qBAAqB,CAAC,OAAO,EAAW,EAAE,EAAoB;AACvE,EAAE,MAAM,WAAW,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;;AAElC,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,OAAO,QAAQ;AACnB;;AAEA,EAAE,OAAO,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAC,EAAG,CAAC;AAC1D;;AAEA,SAAS,kBAAkB,CAAC,OAAO,EAAW,QAAQ,EAAsB;AAC5E,EAAE,MAAM,QAAS,GAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;;AAE3C;AACA,EAAE,IAAI,QAAQ,EAAE,IAAI,EAAE;AACtB,IAAI,OAAO,QAAQ;AACnB;;AAEA;AACA,EAAE,IAAI,QAAS,IAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AAClC,IAAI,QAAQ,CAAC,IAAA,GAAO,QAAQ,CAAC,IAAI;AACjC,IAAI,QAAQ,CAAC,UAAA,GAAa,QAAQ,CAAC,UAAU;AAC7C,IAAI,OAAO,QAAQ;AACnB;;AAEA;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC;AACpC,EAAE,OAAO,QAAQ;AACjB;;ACxEA;AACA,MAAM,0BAA0B,GAA0C;AAC1E,EAAE,GAAG,EAAE,WAAW;AAClB,EAAE,GAAG,EAAE,eAAe;AACtB,EAAE,GAAG,EAAE,kBAAkB;AACzB,EAAE,GAAG,EAAE,mBAAmB;AAC1B,EAAE,GAAG,EAAE,WAAW;AAClB,EAAE,GAAG,EAAE,gBAAgB;AACvB,EAAE,GAAG,EAAE,mBAAmB;AAC1B,EAAE,GAAG,EAAE,oBAAoB;AAC3B,EAAE,GAAG,EAAE,qBAAqB;AAC5B,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,IAAI,EAAE,eAAe;AACvB,EAAE,IAAI,EAAE,gBAAgB;AACxB,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,IAAI,EAAE,iBAAiB;AACzB,CAAE;;AAEF,MAAM,yBAA0B,GAAE,CAAC,OAAO,KAAsB;AAChE,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,OAAA,EAAiC;AAC7F,CAAC;;AAED;AACA;AACA;AACO,SAAS,SAAS,CAAC,IAAI,EAA4B;AAC1D,EAAE,MAAM,UAAA,GAAa,iBAAiB,CAAC,IAAI,CAAE,GAAE,IAAI,CAAC,UAAW,GAAE,EAAE;AACnE,EAAE,MAAM,MAAA,GAAS,aAAa,CAAC,IAAI,CAAE,GAAE,IAAI,CAAC,MAAO,GAAE,SAAS;;AAE9D,EAAE,IAAI,MAAM,EAAE;AACd;AACA,IAAI,IAAI,MAAM,CAAC,SAASG,kBAAc,CAAC,EAAE,EAAE;AAC3C,MAAM,OAAO,EAAE,IAAI,EAAES,qBAAgB;AACrC;AACA,KAAI,MAAO,IAAI,MAAM,CAAC,IAAA,KAAST,kBAAc,CAAC,KAAK,EAAE;AACrD,MAAM,IAAI,OAAO,MAAM,CAAC,OAAQ,KAAI,WAAW,EAAE;AACjD,QAAQ,MAAM,cAAe,GAAE,yBAAyB,CAAC,UAAU,CAAC;AACpE,QAAQ,IAAI,cAAc,EAAE;AAC5B,UAAU,OAAO,cAAc;AAC/B;AACA;;AAEA,MAAM,IAAI,MAAM,CAAC,OAAQ,IAAG,yBAAyB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;AACvE,QAAQ,OAAO,EAAE,IAAI,EAAEU,sBAAiB,EAAE,OAAO,EAAE,MAAM,CAAC,OAAA,EAAS;AACnE,aAAa;AACb,QAAQ,OAAO,EAAE,IAAI,EAAEA,sBAAiB,EAAE,OAAO,EAAE,eAAA,EAAiB;AACpE;AACA;AACA;;AAEA;AACA,EAAE,MAAM,cAAe,GAAE,yBAAyB,CAAC,UAAU,CAAC;;AAE9D,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,OAAO,cAAc;AACzB;;AAEA;AACA,EAAE,IAAI,MAAM,EAAE,SAASV,kBAAc,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,EAAE,IAAI,EAAES,qBAAgB;AACnC,SAAS;AACT,IAAI,OAAO,EAAE,IAAI,EAAEC,sBAAiB,EAAE,OAAO,EAAE,eAAA,EAAiB;AAChE;AACA;;AAEA,SAAS,yBAAyB,CAAC,UAAU,EAA0C;AACvF;;AAEA;AACA,EAAE,MAAM,iBAAkB,GAAE,UAAU,CAACC,kDAA8B,CAAA,IAAK,UAAU,CAACC,6CAAyB,CAAC;AAC/G;AACA,EAAE,MAAM,iBAAkB,GAAE,UAAU,CAACC,iDAA6B,CAAC;;AAErE,EAAE,MAAM,cAAe;AACvB,IAAI,OAAO,sBAAsB;AACjC,QAAQ;AACR,QAAQ,OAAO,iBAAA,KAAsB;AACrC,UAAU,QAAQ,CAAC,iBAAiB;AACpC,UAAU,SAAS;;AAEnB,EAAE,IAAI,OAAO,cAAe,KAAI,QAAQ,EAAE;AAC1C,IAAI,OAAOC,8BAAyB,CAAC,cAAc,CAAC;AACpD;;AAEA,EAAE,IAAI,OAAO,iBAAkB,KAAI,QAAQ,EAAE;AAC7C,IAAI,OAAO,EAAE,IAAI,EAAEJ,sBAAiB,EAAE,OAAO,EAAE,0BAA0B,CAAC,iBAAiB,CAAE,IAAG,iBAAiB;AACjH;;AAEA,EAAE,OAAO,SAAS;AAClB;;AC9DA,MAAM,cAAA,GAAiB,IAAI;AAC3B,MAAM,eAAA,GAAkB,GAAG,CAAA;;AAO3B;AACA;AACA;AACO,MAAM,kBAAmB,CAAA;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA;AACA;;AAEA;;AAGA,GAAS,WAAW,CAAC;;AAGnB,EAAG;AACL,IAAI,IAAI,CAAC,uBAAwB,GAAE,OAAO,EAAE,OAAA,IAAW,eAAe;AACtE,IAAI,IAAI,CAAC,oBAAqB,GAAE,IAAI,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;AACvF,IAAI,IAAI,CAAC,wBAAyB,GAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAG,GAAE,IAAI,CAAC;AACjE,IAAI,IAAI,CAAC,mBAAA,GAAsB,IAAI,OAAO,EAAE;AAC5C,IAAI,IAAI,CAAC,UAAA,GAAa,IAAI,GAAG,EAAkB;AAC/C,IAAI,IAAI,CAAC,eAAgB,GAAEK,aAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,GAAI,EAAC,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,IAAI,EAAsB;AAC1C,IAAI,MAAM,mBAAA,GAAsB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAG,GAAE,IAAI,CAAC;;AAE7D,IAAI,IAAI,IAAI,CAAC,wBAAyB,KAAI,mBAAmB,EAAE;AAC/D,MAAM,IAAI,gBAAiB,GAAE,CAAC;AAC9B,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;AACvD,QAAQ,IAAI,MAAO,IAAG,MAAM,CAAC,YAAa,IAAG,mBAAoB,GAAE,IAAI,CAAC,uBAAuB,EAAE;AACjG,UAAU,oBAAoB,MAAM,CAAC,KAAK,CAAC,IAAI;AAC/C,UAAU,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAA,GAAI,SAAS;AAClD;AACA,OAAO,CAAC;AACR,MAAM,IAAI,gBAAiB,GAAE,CAAC,EAAE;AAChC,QAAQ,WAAY;AACpB,UAAU9B,WAAM,CAAC,GAAG;AACpB,YAAY,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,+CAA+C,EAAE,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC;AAC7I,WAAW;AACX;AACA,MAAM,IAAI,CAAC,wBAAyB,GAAE,mBAAmB;AACzD;;AAEA,IAAI,MAAM,kBAAmB,GAAE,sBAAsB,IAAI,CAAC,uBAAuB;AACjF,IAAI,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAA,IAAK;AAC3E,MAAM,YAAY,EAAE,mBAAmB;AACvC,MAAM,KAAK,EAAE,IAAI,GAAG,EAAE;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAA,GAAI,aAAa;AACjE,IAAI,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;AACjC,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC;;AAErD;AACA,IAAI,MAAM,aAAc,GAAE,gBAAgB,CAAC,IAAI,CAAC;AAChD,IAAI,IAAI,CAAC,aAAA,IAAiB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;AAC9D,MAAM,IAAI,CAAC,eAAe,EAAE;AAC5B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB,IAAI,MAAM,aAAc,GAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAO,KAAI,MAAA,GAAS,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA,GAAI,EAAE,CAAC,CAAC;;AAE/G,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC9B,IAAI,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;;AAEpD,IAAI,MAAM,aAAA,GAAgB,SAAS,CAAC,IAAI;AACxC,IAAI,MAAM,sBAAuB,GAAE,aAAa,CAAC,MAAA,GAAS,aAAa;AACvE,IAAI,WAAY;AAChB,MAAMA,WAAM,CAAC,GAAG;AAChB,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC,QAAQ,EAAE,sBAAsB,CAAC,mDAAmD,CAAC;AACpI,OAAO;;AAEP,IAAI,MAAM,cAAe,GAAE,IAAI,CAAC,GAAG,EAAG,GAAE,eAAgB,GAAE,IAAI;;AAE9D,IAAI,KAAK,MAAM,IAAK,IAAG,SAAS,EAAE;AAClC,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;AACpE,MAAM,MAAM,WAAY,GAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;AAC5D,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACjC;;AAEA;AACA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB,IAAI,IAAI,CAAC,oBAAA,GAAuB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC;AACzE,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAU,UAAU,CAAC,KAAK,EAAqC;AAC/D,IAAI,MAAM,OAAQ,GAAE,qBAAqB,CAAC,KAAK,CAAC;AAChD,IAAI,MAAM,SAAU,GAAE,IAAI,GAAG,EAAgB;;AAE7C,IAAI,MAAM,YAAY,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;;AAE1D,IAAI,KAAK,MAAM,IAAK,IAAG,SAAS,EAAE;AAClC,MAAM,MAAM,IAAA,GAAO,IAAI,CAAC,IAAI;AAC5B,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AACzB,MAAM,MAAM,gBAAiB,GAAE,4BAA4B,CAAC,IAAI,CAAC;;AAEjE;AACA,MAAM,MAAM,QAAQ,gBAAgB,CAAC,KAAM,IAAG,EAAE;;AAEhD,MAAM,KAAK,MAAM,KAAA,IAAS,IAAI,CAAC,QAAQ,EAAE;AACzC,QAAQ,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;AAC/D;;AAEA;AACA;AACA,MAAM,gBAAgB,CAAC,KAAM;AAC7B,QAAQ,KAAK,CAAC,MAAA,GAAS;AACvB,YAAY,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,eAAgB,GAAE,CAAC,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc;AAC/F,YAAY,KAAK;;AAEjB,MAAM,MAAM,eAAe+B,8BAAyB,CAAC,IAAI,CAAC,MAAM,CAAC;AACjE,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,gBAAgB,CAAC,YAAa,GAAE,YAAY;AACpD;;AAEA,MAAMC,iBAAY,CAAC,gBAAgB,CAAC;AACpC;;AAEA,IAAI,OAAO,SAAS;AACpB;;AAEA;AACA,GAAU,mBAAmB,GAAS;AACtC,IAAI,MAAM,gBAAiB,GAAE,IAAI,CAAC,GAAG,EAAE;AACvC;AACA,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE,cAAc,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;AACtE,MAAM,IAAI,cAAe,IAAG,gBAAgB,EAAE;AAC9C,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACtC;AACA;AACA;;AAEA;AACA,GAAU,uCAAuC,CAAC,IAAI,EAAuC;AAC7F,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,IAAc,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;AACvF;;AAEA;AACA,GAAU,sBAAsB,CAAC,KAAK,EAAmC;AACzE;AACA;AACA,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAgC,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,CAAC;AAChH;AACA;;AAEA,SAAS,SAAS,CAAC,IAAI,EAAkF;AACzG,EAAE,MAAM,UAAA,GAAa,IAAI,CAAC,UAAU;;AAEpC,EAAE,MAAM,MAAO,GAAE,UAAU,CAAC3C,qCAAgC,CAAE;AAC9D,EAAE,MAAM,EAAG,GAAE,UAAU,CAACP,iCAA4B,CAAE;AACtD,EAAE,MAAM,MAAO,GAAE,UAAU,CAACC,qCAAgC,CAAE;;AAE9D,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ;AAC/B;;AAEA;AACO,SAAS,4BAA4B,CAAC,IAAI,EAAkC;AACnF,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,MAAA,GAAS,QAAQ,EAAE,MAAO,EAAA,GAAI,WAAW,CAAC,IAAI,CAAC;AAChF,EAAE,MAAM,kBAAmB,GAAEqC,4BAAuB,CAAC,MAAwB;;AAE7E,EAAE,MAAM,aAAa,IAAI,CAAC,UAAU,CAACa,0CAAqC,CAAE;;AAE5E,EAAE,MAAM,UAAU,GAAmB;AACrC,IAAI,CAAClD,qCAAgC,GAAG,MAAM;AAC9C,IAAI,CAACkD,0CAAqC,GAAG,UAAU;AACvD,IAAI,CAACnD,iCAA4B,GAAG,EAAE;AACtC,IAAI,CAACO,qCAAgC,GAAG,MAAM;AAC9C,IAAI,GAAG,IAAI;AACX,IAAI,GAAG,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,GAAG;;AAEH,EAAE,MAAM,EAAE,KAAM,EAAA,GAAI,IAAI;AACxB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAA,KAAY,IAAI,CAAC,WAAW,EAAE;;AAEnE;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,cAAe,GAAE,eAAe,CAAC,IAAI,CAAC;;AAE9C,EAAE,MAAM,MAAO,GAAE,SAAS,CAAC,IAAI,CAAC;;AAEhC,EAAE,MAAM,YAAY,GAAiB;AACrC,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,MAAM;AACV,IAAI,EAAE;AACN,IAAI,MAAM,EAAE6C,qBAAgB,CAAC,MAAM,CAAC;AACpC,IAAI,KAAK,EAAEC,gCAA2B,CAAC,KAAK,CAAC;AAC7C,GAAG;;AAEH,EAAE,MAAM,UAAW,GAAE,UAAU,CAACT,kDAA8B,CAAC;AAC/D,EAAE,MAAM,eAAgB,GAAE,OAAO,UAAW,KAAI,WAAW,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,YAAa,EAAA,GAAI,SAAS;;AAEhH,EAAE,MAAM,gBAAgB,GAAqB;AAC7C,IAAI,QAAQ,EAAE;AACd,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;AAC1C,OAAO;AACP,MAAM,GAAG,eAAe;AACxB,KAAK;AACL,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,eAAe,EAAEU,2BAAsB,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3D,IAAI,SAAS,EAAEA,2BAAsB,CAAC,IAAI,CAAC,OAAO,CAAC;AACnD,IAAI,WAAW,EAAE,WAAW;AAC5B,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,qBAAqB,EAAE;AAC3B,MAAM,iBAAiB,EAAE,kBAAkB,CAAC,KAAK;AACjD,MAAM,0BAA0B,EAAE,kBAAkB,CAAC,cAAc;AACnE,MAAM,UAAU;AAChB,MAAM,sBAAsB,EAAE7B,sCAAiC,CAAC,MAAwB;AACxF,KAAK;AACL,IAAI,IAAI,MAAA,IAAU;AAClB,MAAM,gBAAgB,EAAE;AACxB,QAAQ,MAAM;AACd,OAAO;AACP,KAAK,CAAC;AACN,GAAG;;AAEH,EAAE,OAAO,gBAAgB;AACzB;;AAEA,SAAS,8BAA8B,CAAC,IAAI,EAAY,KAAK,EAAc,SAAS,EAA2B;AAC/G,EAAE,MAAM,IAAA,GAAO,IAAI,CAAC,IAAI;;AAExB,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AACvB;;AAEA,EAAE,MAAM,UAAA,GAAa,CAAC,IAAI;;AAE1B;AACA,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS;AACnC,MAAM,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;AAC7D,KAAK,CAAC;AACN,IAAI;AACJ;;AAEA,EAAE,MAAM,UAAU,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;AAC3C,EAAE,MAAM,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO;AAC7C,EAAE,MAAM,YAAa,GAAE,eAAe,CAAC,IAAI,CAAC;;AAE5C,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,KAAA,EAAQ,GAAE,IAAI;;AAExD,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,MAAO,GAAE,UAAW,GAAE,WAAW,CAAC,IAAI,CAAC;AACxE,EAAE,MAAM,UAAU;AAClB,IAAI,CAAClB,qCAAgC,GAAG,MAAM;AAC9C,IAAI,CAACP,iCAA4B,GAAG,EAAE;AACtC,IAAI,GAAG,sBAAsB,CAAC,UAAU,CAAC;AACzC,IAAI,GAAG,IAAI;AACX,GAAG;;AAEH,EAAE,MAAM,MAAO,GAAE,SAAS,CAAC,IAAI,CAAC;;AAEhC,EAAE,MAAM,QAAQ,GAAa;AAC7B,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,WAAW;AACf,IAAI,cAAc,EAAE,YAAY;AAChC,IAAI,eAAe,EAAEsD,2BAAsB,CAAC,SAAS,CAAC;AACtD;AACA,IAAI,SAAS,EAAEA,2BAAsB,CAAC,OAAO,CAAA,IAAK,SAAS;AAC3D,IAAI,MAAM,EAAEF,qBAAgB,CAAC,MAAM,CAAC;AACpC,IAAI,EAAE;AACN,IAAI,MAAM;AACV,IAAI,YAAY,EAAEH,8BAAyB,CAAC,IAAI,CAAC,MAAM,CAAC;AACxD,IAAI,KAAK,EAAEI,gCAA2B,CAAC,KAAK,CAAC;AAC7C,GAAG;;AAEH,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAEtB,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS;AACjC,IAAI,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;AAC3D,GAAG,CAAC;AACJ;;AAEA,SAAS,WAAW,CAAC,IAAI;;AAMzB,CAAE;AACF,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,QAAS,GAAE,SAAS,CAAC,IAAI,CAAC;AAC1E,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,YAAa,EAAA,GAAI,oBAAoB,CAAC,IAAI,CAAC;;AAEhH,EAAE,MAAM,EAAA,GAAK,SAAA,IAAa,UAAU;AACpC,EAAE,MAAM,MAAA,GAAS,aAAA,IAAiB,cAAc;;AAEhD,EAAE,MAAM,IAAA,GAAO,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAA,EAAG;;AAEpD,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,WAAW;AACf,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,IAAI;AACR,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,IAAI,EAAoD;AACxF,EAAE,MAAM,WAAY,GAAE,EAAE,GAAG,MAAM;;AAEjC;AACA,EAAE,OAAO,WAAW,CAACF,0CAAqC,CAAC;AAC3D,EAAE,OAAO,WAAW,CAAC,0CAA0C,CAAC;AAChE,EAAE,OAAO,WAAW,CAAC9C,+CAA0C,CAAC;AAChE;;AAEA,EAAE,OAAO,WAAW;AACpB;;AAEA,SAAS,OAAO,CAAC,IAAI,EAAyC;AAC9D,EAAE,MAAM,UAAA,GAAa,IAAI,CAAC,UAAU;AACpC,EAAE,MAAM,IAAI,GAA4B,EAAE;;AAE1C,EAAE,IAAI,IAAI,CAAC,SAASb,YAAQ,CAAC,QAAQ,EAAE;AACvC,IAAI,IAAI,CAAC,WAAW,CAAE,GAAEA,YAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA,EAAE,MAAM,4BAA6B,GAAE,UAAU,CAACqD,6CAAyB,CAAC;AAC5E,EAAE,IAAI,4BAA4B,EAAE;AACpC,IAAI,IAAI,CAACD,kDAA8B,CAAA,GAAI,4BAA6B;AACxE;;AAEA,EAAE,MAAM,WAAY,GAAE,kBAAkB,CAAC,IAAI,CAAC;;AAE9C,EAAE,IAAI,WAAW,CAAC,GAAG,EAAE;AACvB,IAAI,IAAI,CAAC,GAAA,GAAM,WAAW,CAAC,GAAG;AAC9B;;AAEA,EAAE,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE;AACjC,IAAI,IAAI,CAAC,YAAY,CAAA,GAAI,WAAW,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3D;AACA,EAAE,IAAI,WAAW,CAAC,eAAe,CAAC,EAAE;AACpC,IAAI,IAAI,CAAC,eAAe,CAAA,GAAI,WAAW,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACjE;;AAEA,EAAE,OAAO,IAAI;AACb;;AC9aA,SAAS,WAAW,CAAC,IAAI,EAAQ,aAAa,EAAiB;AAC/D;AACA,EAAE,MAAM,aAAarD,SAAK,CAAC,OAAO,CAAC,aAAa,CAAC;;AAEjD,EAAE,IAAI,MAAO,GAAE,oBAAoB,CAAC,aAAa,CAAC;;AAElD;AACA,EAAE,IAAI,UAAW,IAAG,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;AACxD,IAAIgE,uBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC;AACxC;;AAEA;AACA,EAAE,IAAI,UAAU,EAAE,WAAW,EAAE,CAAC,QAAQ,EAAE;AAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,0CAA0C,EAAE,IAAI,CAAC;AACvE;;AAEA;AACA;AACA,EAAE,IAAI,aAAc,KAAIC,gBAAY,EAAE;AACtC,IAAI,SAAS;AACb,MAAM,KAAK,EAAEjB,2BAAsB,EAAE;AACrC,MAAM,cAAc,EAAEC,6BAAwB,EAAE;AAChD,KAAK;AACL;;AAEA;AACA,EAAE,IAAI,MAAM,EAAE;AACd,IAAIiB,4BAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,cAAc,CAAC;AACtE;;AAEA,EAAEC,iBAAY,CAAC,IAAI,CAAC;;AAEpB,EAAE,MAAM,MAAA,GAAS9D,cAAS,EAAE;AAC5B,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;AACjC;;AAEA,SAAS,SAAS,CAAC,IAAI,EAAc;AACrC,EAAE+D,eAAU,CAAC,IAAI,CAAC;;AAElB,EAAE,MAAM,MAAA,GAAS/D,cAAS,EAAE;AAC5B,EAAE,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACO,MAAM,qBAAsD;;AAGnE,GAAS,WAAW,CAAC,OAAO,EAAyB;AACrD,IAAI,UAAU,CAAC,qBAAqB,CAAC;AACrC,IAAI,IAAI,CAAC,SAAU,GAAE,IAAI,kBAAkB,CAAC,OAAO,CAAC;AACpD;;AAEA;AACA;AACA;AACA,GAAS,MAAM,UAAU,GAAkB;AAC3C,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AAC1B;;AAEA;AACA;AACA;AACA,GAAS,MAAM,QAAQ,GAAkB;AACzC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AAC1B;;AAEA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAQ,aAAa,EAAiB;AAC3D,IAAI,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC;AACpC;;AAEA;AACA,GAAS,KAAK,CAAC,IAAI,EAA6B;AAChD,IAAI,SAAS,CAAC,IAAI,CAAC;;AAEnB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AAC/B;AACA;;AChEA;AACA;AACA;AACO,MAAM,eAAiC;;AAG9C,GAAS,WAAW,CAAC,MAAM,EAAU;AACrC,IAAI,IAAI,CAAC,OAAQ,GAAE,MAAM;AACzB,IAAI,UAAU,CAAC,eAAe,CAAC;AAC/B;;AAEA;AACA,GAAS,YAAY;AACrB,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,MAAM;AACV,IAAoB;AACpB,IAAI,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;;AAE7C,IAAI,MAAM,UAAW,GAAE,YAAY,CAAC,OAAO,CAAC;AAC5C,IAAI,MAAM,aAAc,GAAE,UAAU,EAAE,WAAW,EAAE;;AAEnD,IAAI,IAAI,CAACgB,oBAAe,CAAC,OAAO,CAAC,EAAE;AACnC,MAAM,OAAO,oBAAoB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,cAAe,EAAC,CAAC;AACnF;;AAEA;AACA;AACA,IAAI,MAAM,mBAAoB,GAAE,cAAc,CAACzB,wCAAoB,CAAA,IAAK,cAAc,CAACD,4CAAwB,CAAC;;AAEhH;AACA;AACA,IAAI,IAAI,QAAA,KAAaM,YAAQ,CAAC,UAAU,mBAAA,KAAwB,CAAC,UAAW,IAAG,aAAa,EAAE,QAAQ,CAAC,EAAE;AACzG,MAAM,OAAO,oBAAoB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,cAAe,EAAC,CAAC;AACnF;;AAEA,IAAI,MAAM,aAAA,GAAgB,UAAA,GAAa,gBAAgB,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAA,GAAI,SAAS;AAClG,IAAI,MAAM,aAAa,CAAC,cAAc,aAAa,EAAE,QAAQ;;AAE7D;AACA;AACA,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,OAAO,oBAAoB,CAAC;AAClC,QAAQ,QAAQ,EAAE,aAAA,GAAgBoE,6BAAgB,CAAC,kBAAmB,GAAEA,6BAAgB,CAAC,UAAU;AACnG,QAAQ,OAAO;AACf,QAAQ,cAAc;AACtB,OAAO,CAAC;AACR;;AAEA;AACA,IAAI,MAAM;AACV,MAAM,WAAW,EAAE,gBAAgB;AACnC,MAAM,IAAI,EAAE,kBAAkB;AAC9B,MAAM,EAAE;AACR,KAAI,GAAI,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;;AAEzD,IAAI,MAAM,mBAAmB;AAC7B,MAAM,GAAG,kBAAkB;AAC3B,MAAM,GAAG,cAAc;AACvB,KAAK;;AAEL,IAAI,IAAI,EAAE,EAAE;AACZ,MAAM,gBAAgB,CAAC5D,iCAA4B,CAAA,GAAI,EAAE;AACzD;;AAEA,IAAI,MAAM,uBAAwB,GAAE,EAAE,QAAQ,EAAE,MAAM;AACtD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI;AACrB,MAAM,gBAAgB;AACtB,MAAM;AACN,QAAQ,cAAc,EAAE,gBAAgB;AACxC,QAAQ,QAAQ,EAAE,gBAAgB;AAClC,QAAQ,aAAa,EAAE,aAAa;AACpC,QAAQ,aAAa,EAAE,aAAa;AACpC,OAAO;AACP,MAAM,uBAAuB;AAC7B,KAAK;AACL,IAAI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE;AAC3C,MAAM,OAAO,oBAAoB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,cAAe,EAAC,CAAC;AACnF;;AAEA,IAAI,MAAM,EAAE,cAAA,EAAiB,GAAE,oBAAoB,CAAC,OAAO,CAAA,IAAK,EAAE;;AAElE,IAAI,MAAM,SAAU,GAAE,aAAa,EAAE,aAAa,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAA,GAAI,SAAS;AAClH,IAAI,MAAM,GAAI,GAAE,SAAU,GAAEF,0CAAqC,CAAC,SAAS,CAAE,GAAE,SAAS;;AAExF,IAAI,MAAM,UAAA,GAAa+D,oBAAe,CAAC,GAAG,EAAE,WAAW,CAAA,IAAK,IAAI,CAAC,MAAM,EAAE;;AAEzE,IAAI,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,yBAAyB,CAAE,GAAEC,eAAU;AACvE,MAAM,OAAO;AACb,MAAM;AACN,QAAQ,IAAI,EAAE,gBAAgB;AAC9B,QAAQ,UAAU,EAAE,gBAAgB;AACpC,QAAQ,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,qBAAqB,CAAC,iBAAiB;AACjG,QAAQ,aAAa;AACrB,QAAQ,gBAAgB,EAAED,oBAAe,CAAC,GAAG,EAAE,WAAW,CAAC;AAC3D,OAAO;AACP,MAAM,UAAU;AAChB,KAAK;;AAEL,IAAI,MAAM,MAAA,GAAS,CAAC,EAAA,mBAAA,CAAA,CAAA,CAAA,WAAA,EAAA;AACA,IAAA,IAAA,MAAA,KAAA,SAAA,IAAA,MAAA,KAAA,MAAA,EAAA;AACA,MAAA,WAAA,IAAA3C,WAAA,CAAA,GAAA,CAAA,CAAA,oDAAA,EAAA,MAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA,CAAA;;AAEA,MAAA,OAAA,oBAAA,CAAA;AACA,QAAA,QAAA,EAAA0C,6BAAA,CAAA,UAAA;AACA,QAAA,OAAA;AACA,QAAA,cAAA;AACA,QAAA,UAAA;AACA,QAAA,yBAAA,EAAA,CAAA;AACA,OAAA,CAAA;AACA;;AAEA,IAAA;AACA,MAAA,CAAA,OAAA;AACA;AACA,MAAA,aAAA,KAAA;AACA,MAAA;AACA,MAAA,WAAA,IAAA1C,WAAA,CAAA,GAAA,CAAA,gFAAA,CAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,aAAA,CAAA;AACA;;AAEA,IAAA,OAAA;AACA,MAAA,GAAA,oBAAA,CAAA;AACA,QAAA,QAAA,EAAA,OAAA,GAAA0C,6BAAA,CAAA,kBAAA,GAAAA,6BAAA,CAAA,UAAA;AACA,QAAA,OAAA;AACA,QAAA,cAAA;AACA,QAAA,UAAA;AACA,QAAA,yBAAA,EAAA,yBAAA,GAAA,UAAA,GAAA,SAAA;AACA,OAAA,CAAA;AACA,MAAA,UAAA,EAAA;AACA;AACA,QAAA,CAAAT,0CAAA,GAAA,yBAAA,GAAA,UAAA,GAAA,SAAA;AACA,OAAA;AACA,KAAA;AACA;;AAEA;AACA,GAAA,QAAA,GAAA;AACA,IAAA,OAAA,eAAA;AACA;AACA;;AAEA,SAAA,gBAAA,CAAA,UAAA,EAAA,OAAA,EAAA,QAAA,EAAA;AACA,EAAA,MAAA,aAAA,GAAA,UAAA,CAAA,WAAA,EAAA;;AAEA;AACA;AACA,EAAA,IAAAY,sBAAA,CAAA,aAAA,CAAA,IAAA,aAAA,CAAA,OAAA,KAAA,OAAA,EAAA;AACA,IAAA,IAAA,aAAA,CAAA,QAAA,EAAA;AACA,MAAA,MAAA,aAAA,GAAA,mBAAA,CAAA,UAAA,CAAA,WAAA,EAAA,CAAA;AACA,MAAA,WAAA;AACA,QAAA7C,WAAA,CAAA,GAAA,CAAA,CAAA,0DAAA,EAAA,QAAA,CAAA,EAAA,EAAA,aAAA,CAAA,CAAA,CAAA;AACA,MAAA,OAAA,aAAA;AACA;;AAEA,IAAA,MAAA,aAAA,GAAA,mBAAA,CAAA,aAAA,CAAA;AACA,IAAA,WAAA,IAAAA,WAAA,CAAA,GAAA,CAAA,CAAA,mDAAA,EAAA,QAAA,CAAA,EAAA,EAAA,aAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,aAAA;AACA;;AAEA,EAAA,OAAA,SAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAA,oBAAA,CAAA;AACA,EAAA,QAAA;AACA,EAAA,OAAA;AACA,EAAA,cAAA;AACA,EAAA,UAAA;AACA,EAAA,yBAAA;AACA;;AAMA,EAAA;AACA,EAAA,IAAA,UAAA,GAAA,iBAAA,CAAA,OAAA,EAAA,cAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA,EAAA,IAAA,yBAAA,KAAA,SAAA,EAAA;AACA,IAAA,UAAA,GAAA,UAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,CAAA,EAAA,yBAAA,CAAA,CAAA,CAAA;AACA;;AAEA,EAAA,IAAA,UAAA,KAAA,SAAA,EAAA;AACA,IAAA,UAAA,GAAA,UAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA;AACA;;AAEA;AACA;AACA,EAAA,IAAA,QAAA,IAAA,SAAA,EAAA;AACA,IAAA,OAAA,EAAA,QAAA,EAAA0C,6BAAA,CAAA,UAAA,EAAA,UAAA,EAAA;AACA;;AAEA,EAAA,IAAA,QAAA,KAAAA,6BAAA,CAAA,UAAA,EAAA;AACA,IAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA,GAAA,CAAA,wCAAA,EAAA,GAAA,CAAA,EAAA;AACA;;AAEA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA;AACA;;AAEA,SAAA,iBAAA,CAAA,OAAA,EAAA,cAAA,EAAA;AACA,EAAA,MAAA,UAAA,GAAArE,SAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,EAAA,MAAA,aAAA,GAAA,UAAA,EAAA,WAAA,EAAA;;AAEA,EAAA,IAAA,UAAA,GAAA,aAAA,EAAA,UAAA,IAAA,IAAAuB,iBAAA,EAAA;;AAEA;AACA;AACA;AACA,EAAA,MAAA,GAAA,GAAA,cAAA,CAAA7B,qCAAA,CAAA,IAAA,cAAA,CAAAD,iCAAA,CAAA;AACA,EAAA,IAAA,GAAA,IAAA,OAAA,GAAA,KAAA,QAAA,EAAA;AACA,IAAA,UAAA,GAAA,UAAA,CAAA,GAAA,CAAA,sBAAA,EAAA,GAAA,CAAA;AACA;;AAEA,EAAA,OAAA,UAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,YAAA,CAAA,OAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAAO,SAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,EAAA,OAAA,IAAA,IAAAwE,sBAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,GAAA,IAAA,GAAA,SAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}