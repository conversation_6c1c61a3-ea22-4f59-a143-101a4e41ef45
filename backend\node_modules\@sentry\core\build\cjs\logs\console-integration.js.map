{"version": 3, "file": "console-integration.js", "sources": ["../../../src/logs/console-integration.ts"], "sourcesContent": ["import { getClient } from '../currentScopes';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { addConsoleInstrumentationHandler } from '../instrument/console';\nimport { defineIntegration } from '../integration';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '../semanticAttributes';\nimport type { ConsoleLevel } from '../types-hoist/instrument';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport { isPrimitive } from '../utils/is';\nimport { CONSOLE_LEVELS, logger } from '../utils/logger';\nimport { normalize } from '../utils/normalize';\nimport { GLOBAL_OBJ } from '../utils/worldwide';\nimport { _INTERNAL_captureLog } from './exports';\n\ninterface CaptureConsoleOptions {\n  levels: ConsoleLevel[];\n}\n\ntype GlobalObjectWithUtil = typeof GLOBAL_OBJ & {\n  util: {\n    format: (...args: unknown[]) => string;\n  };\n};\n\nconst INTEGRATION_NAME = 'ConsoleLogs';\n\nconst DEFAULT_ATTRIBUTES = {\n  [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.console.logging',\n};\n\nconst _consoleLoggingIntegration = ((options: Partial<CaptureConsoleOptions> = {}) => {\n  const levels = options.levels || CONSOLE_LEVELS;\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      const { _experiments, normalizeDepth = 3, normalizeMaxBreadth = 1_000 } = client.getOptions();\n      if (!_experiments?.enableLogs) {\n        DEBUG_BUILD && logger.warn('`_experiments.enableLogs` is not enabled, ConsoleLogs integration disabled');\n        return;\n      }\n\n      addConsoleInstrumentationHandler(({ args, level }) => {\n        if (getClient() !== client || !levels.includes(level)) {\n          return;\n        }\n\n        if (level === 'assert') {\n          if (!args[0]) {\n            const followingArgs = args.slice(1);\n            const assertionMessage =\n              followingArgs.length > 0\n                ? `Assertion failed: ${formatConsoleArgs(followingArgs, normalizeDepth, normalizeMaxBreadth)}`\n                : 'Assertion failed';\n            _INTERNAL_captureLog({ level: 'error', message: assertionMessage, attributes: DEFAULT_ATTRIBUTES });\n          }\n          return;\n        }\n\n        const isLevelLog = level === 'log';\n        _INTERNAL_captureLog({\n          level: isLevelLog ? 'info' : level,\n          message: formatConsoleArgs(args, normalizeDepth, normalizeMaxBreadth),\n          severityNumber: isLevelLog ? 10 : undefined,\n          attributes: DEFAULT_ATTRIBUTES,\n        });\n      });\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Captures calls to the `console` API as logs in Sentry. Requires `_experiments.enableLogs` to be enabled.\n *\n * @experimental This feature is experimental and may be changed or removed in future versions.\n *\n * By default the integration instruments `console.debug`, `console.info`, `console.warn`, `console.error`,\n * `console.log`, `console.trace`, and `console.assert`. You can use the `levels` option to customize which\n * levels are captured.\n *\n * @example\n *\n * ```ts\n * import * as Sentry from '@sentry/browser';\n *\n * Sentry.init({\n *   integrations: [Sentry.consoleLoggingIntegration({ levels: ['error', 'warn'] })],\n * });\n * ```\n */\nexport const consoleLoggingIntegration = defineIntegration(_consoleLoggingIntegration);\n\nfunction formatConsoleArgs(values: unknown[], normalizeDepth: number, normalizeMaxBreadth: number): string {\n  return 'util' in GLOBAL_OBJ && typeof (GLOBAL_OBJ as GlobalObjectWithUtil).util.format === 'function'\n    ? (GLOBAL_OBJ as GlobalObjectWithUtil).util.format(...values)\n    : safeJoinConsoleArgs(values, normalizeDepth, normalizeMaxBreadth);\n}\n\nfunction safeJoinConsoleArgs(values: unknown[], normalizeDepth: number, normalizeMaxBreadth: number): string {\n  return values\n    .map(value =>\n      isPrimitive(value) ? String(value) : JSON.stringify(normalize(value, normalizeDepth, normalizeMaxBreadth)),\n    )\n    .join(' ');\n}\n"], "names": ["SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "CONSOLE_LEVELS", "DEBUG_BUILD", "logger", "addConsoleInstrumentationHandler", "getClient", "_INTERNAL_captureLog", "defineIntegration", "GLOBAL_OBJ", "isPrimitive", "normalize"], "mappings": ";;;;;;;;;;;;;AAuBA,MAAM,gBAAA,GAAmB,aAAa;;AAEtC,MAAM,qBAAqB;AAC3B,EAAE,CAACA,mDAAgC,GAAG,sBAAsB;AAC5D,CAAC;;AAED,MAAM,0BAAA,IAA8B,CAAC,OAAO,GAAmC,EAAE,KAAK;AACtF,EAAE,MAAM,MAAO,GAAE,OAAO,CAAC,MAAA,IAAUC,qBAAc;;AAEjD,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,MAAM,EAAE,YAAY,EAAE,cAAA,GAAiB,CAAC,EAAE,mBAAoB,GAAE,MAAQ,GAAE,MAAM,CAAC,UAAU,EAAE;AACnG,MAAM,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE;AACrC,QAAQC,0BAAeC,aAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC;AAChH,QAAQ;AACR;;AAEA,MAAMC,wCAAgC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAA,EAAO,KAAK;AAC5D,QAAQ,IAAIC,uBAAS,OAAO,MAAA,IAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC/D,UAAU;AACV;;AAEA,QAAQ,IAAI,KAAM,KAAI,QAAQ,EAAE;AAChC,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACxB,YAAY,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/C,YAAY,MAAM,gBAAiB;AACnC,cAAc,aAAa,CAAC,MAAA,GAAS;AACrC,kBAAkB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,aAAa,EAAE,cAAc,EAAE,mBAAmB,CAAC,CAAC;AAC7G,kBAAA,kBAAA;AACA,YAAAC,8BAAA,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,CAAA;AACA;AACA,UAAA;AACA;;AAEA,QAAA,MAAA,UAAA,GAAA,KAAA,KAAA,KAAA;AACA,QAAAA,8BAAA,CAAA;AACA,UAAA,KAAA,EAAA,UAAA,GAAA,MAAA,GAAA,KAAA;AACA,UAAA,OAAA,EAAA,iBAAA,CAAA,IAAA,EAAA,cAAA,EAAA,mBAAA,CAAA;AACA,UAAA,cAAA,EAAA,UAAA,GAAA,EAAA,GAAA,SAAA;AACA,UAAA,UAAA,EAAA,kBAAA;AACA,SAAA,CAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,yBAAA,GAAAC,6BAAA,CAAA,0BAAA;;AAEA,SAAA,iBAAA,CAAA,MAAA,EAAA,cAAA,EAAA,mBAAA,EAAA;AACA,EAAA,OAAA,MAAA,IAAAC,oBAAA,IAAA,OAAA,CAAAA,oBAAA,GAAA,IAAA,CAAA,MAAA,KAAA;AACA,MAAA,CAAAA,oBAAA,GAAA,IAAA,CAAA,MAAA,CAAA,GAAA,MAAA;AACA,MAAA,mBAAA,CAAA,MAAA,EAAA,cAAA,EAAA,mBAAA,CAAA;AACA;;AAEA,SAAA,mBAAA,CAAA,MAAA,EAAA,cAAA,EAAA,mBAAA,EAAA;AACA,EAAA,OAAA;AACA,KAAA,GAAA,CAAA,KAAA;AACA,MAAAC,cAAA,CAAA,KAAA,CAAA,GAAA,MAAA,CAAA,KAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAAC,mBAAA,CAAA,KAAA,EAAA,cAAA,EAAA,mBAAA,CAAA,CAAA;AACA;AACA,KAAA,IAAA,CAAA,GAAA,CAAA;AACA;;;;"}