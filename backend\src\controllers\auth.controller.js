const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/user.model');
const ApiError = require('../utils/ApiError');

// Validate JWT configuration
const validateJwtConfig = () => {
  if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32 ||
      !process.env.REFRESH_TOKEN_SECRET || process.env.REFRESH_TOKEN_SECRET.length < 32) {
    throw new Error('Invalid JWT configuration - secrets must be at least 32 characters');
  }
};

// Helper function to generate tokens
const generateTokens = (userId) => {
  try {
    validateJwtConfig();
    
    const accessToken = jwt.sign(
      { _id: userId },
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_EXPIRE || '15m',
        algorithm: 'HS256'
      }
    );
    
    const refreshToken = jwt.sign(
      { _id: userId },
      process.env.REFRESH_TOKEN_SECRET,
      {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRE || '7d',
        algorithm: 'HS256'
      }
    );
    
    return {
      accessToken,
      refreshToken,
      expiresIn: parseInt(process.env.JWT_EXPIRE || '900', 10)
    };
  } catch (error) {
    console.error('Token generation error:', error);
    throw new ApiError('Token generation failed', 500, 'TOKEN_GENERATION_ERROR');
  }
};

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
exports.registerUser = async (req, res) => {
  try {
    console.log('Registering user with data:', req.body);
    
    const { name, email, password } = req.body;

    // Validate input
    if (!name || !email || !password) {
      console.log('Validation failed - missing fields');
      return res.status(400).json({ 
        success: false, 
        message: 'Please provide name, email, and password',
        code: 'MISSING_FIELDS'
      });
    }

    // Check if user exists
    console.log('Checking if user exists');
    const userExists = await User.findOne({ email });
    if (userExists) {
      console.log('User already exists');
      return res.status(400).json({ 
        success: false, 
        message: 'User already exists',
        code: 'USER_EXISTS'
      });
    }

    // Hash password
    console.log('Hashing password');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    console.log('Creating user');
    const user = await User.create({
      name,
      email,
      password: hashedPassword,
    });

    if (!user) {
      console.error('User creation failed');
      return res.status(500).json({ 
        success: false, 
        message: 'User creation failed',
        code: 'USER_CREATION_FAILED'
      });
    }

    // Generate tokens
    console.log('Generating tokens');
    const { accessToken, refreshToken } = await generateTokens(user._id);
    
    // Save refresh token to user
    console.log('Saving refresh token');
    user.refreshToken = refreshToken;
    await user.save();

    console.log('Registration successful');
    return res.status(201).json({
      success: true,
      data: {
        _id: user._id,
        name: user.name,
        email: user.email,
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
exports.loginUser = async (req, res) => {
  try {
    console.log('Logging in user with data:', req.body);
    
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      console.log('Validation failed - missing fields');
      return res.status(400).json({ success: false, message: 'Please provide email and password' });
    }

    // Check if user exists
    console.log('Checking if user exists');
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      console.log('User not found');
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    // Validate password
    console.log('Validating password');
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      console.log('Password mismatch');
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    // Generate tokens
    console.log('Generating tokens');
    const { accessToken, refreshToken } = await generateTokens(user._id);
    
    // Save refresh token to user
    console.log('Saving refresh token');
    user.refreshToken = refreshToken;
    await user.save();

    console.log('Login successful');
    return res.status(200).json({
      success: true,
      data: {
        _id: user._id,
        name: user.name,
        email: user.email,
        accessToken,
        refreshToken
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

// @desc    Refresh access token
// @route   POST /api/auth/refresh
// @access  Public
exports.refreshToken = async (req, res) => {
  try {
    console.log('Refreshing token with data:', req.body);
    
    const { refreshToken } = req.body;

    if (!refreshToken) {
      console.log('Refresh token not provided');
      return res.status(401).json({ success: false, message: 'Refresh token required' });
    }

    // Verify refresh token
    console.log('Verifying refresh token');
    const decoded = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);
    
    // Find user with this refresh token
    console.log('Finding user with refresh token');
    const user = await User.findOne({
      _id: decoded._id,
      'refreshTokens.token': refreshToken
    });

    if (!user) {
      console.log('User not found with refresh token');
      return res.status(403).json({ success: false, message: 'Invalid refresh token' });
    }

    // Generate new tokens
    console.log('Generating new tokens');
    const { accessToken, refreshToken: newRefreshToken } = await generateTokens(user._id);
    
    // Remove old refresh token and add new one
    console.log('Updating refresh tokens');
    user.refreshTokens = user.refreshTokens
      .filter(token => token.token !== refreshToken)
      .concat({ token: newRefreshToken });
    
    await user.save();

    console.log('Token refresh successful');
    return res.status(200).json({
      success: true,
      data: {
        accessToken,
        refreshToken: newRefreshToken
      },
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      console.log('Invalid refresh token');
      return res.status(403).json({ success: false, message: 'Invalid refresh token' });
    }
    
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res) => {
  try {
    console.log('Getting current user');
    
    const user = await User.findById(req.user.id);

    console.log('User retrieved successfully');
    return res.status(200).json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        currency: user.currency,
        prefersDarkMode: user.prefersDarkMode,
      },
    });
  } catch (error) {
    console.error('Error in getMe:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

// @desc    Update user profile
// @route   PUT /api/auth/updateprofile
// @access  Private
exports.updateProfile = async (req, res) => {
  try {
    console.log('Updating user profile with data:', req.body);
    
    const { name, email, currency, prefersDarkMode } = req.body;
    
    // Build update object
    const updateFields = {};
    
    if (name) updateFields.name = name;
    if (email) updateFields.email = email;
    if (currency) updateFields.currency = currency;
    if (prefersDarkMode !== undefined) updateFields.prefersDarkMode = prefersDarkMode;

    // Update user
    console.log('Updating user');
    const user = await User.findByIdAndUpdate(
      req.user.id,
      updateFields,
      { new: true, runValidators: true }
    );

    console.log('User updated successfully');
    return res.status(200).json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        currency: user.currency,
        prefersDarkMode: user.prefersDarkMode,
      },
    });
  } catch (error) {
    console.error('Error in updateProfile:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
};
