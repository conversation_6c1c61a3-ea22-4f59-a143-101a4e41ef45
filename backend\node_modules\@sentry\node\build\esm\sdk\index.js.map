{"version": 3, "file": "index.js", "sources": ["../../../src/sdk/index.ts"], "sourcesContent": ["import type { Integration, Options } from '@sentry/core';\nimport {\n  consoleIntegration,\n  consoleSandbox,\n  functionToStringIntegration,\n  getCurrentScope,\n  getIntegrationsToSetup,\n  hasSpansEnabled,\n  inboundFiltersIntegration,\n  linkedErrorsIntegration,\n  logger,\n  propagationContextFromHeaders,\n  requestDataIntegration,\n  stackParserFromStackParserOptions,\n} from '@sentry/core';\nimport {\n  enhanceDscWithOpenTelemetryRootSpanName,\n  openTelemetrySetupCheck,\n  setOpenTelemetryContextAsyncContextStrategy,\n  setupEventContextTrace,\n} from '@sentry/opentelemetry';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { childProcessIntegration } from '../integrations/childProcess';\nimport { nodeContextIntegration } from '../integrations/context';\nimport { contextLinesIntegration } from '../integrations/contextlines';\nimport { httpIntegration } from '../integrations/http';\nimport { localVariablesIntegration } from '../integrations/local-variables';\nimport { modulesIntegration } from '../integrations/modules';\nimport { nativeNodeFetchIntegration } from '../integrations/node-fetch';\nimport { onUncaughtExceptionIntegration } from '../integrations/onuncaughtexception';\nimport { onUnhandledRejectionIntegration } from '../integrations/onunhandledrejection';\nimport { processSessionIntegration } from '../integrations/processSession';\nimport { INTEGRATION_NAME as SPOTLIGHT_INTEGRATION_NAME, spotlightIntegration } from '../integrations/spotlight';\nimport { getAutoPerformanceIntegrations } from '../integrations/tracing';\nimport { makeNodeTransport } from '../transports';\nimport type { NodeClientOptions, NodeOptions } from '../types';\nimport { isCjs } from '../utils/commonjs';\nimport { envToBool } from '../utils/envToBool';\nimport { defaultStackParser, getSentryRelease } from './api';\nimport { NodeClient } from './client';\nimport { initOpenTelemetry, maybeInitializeEsmLoader } from './initOtel';\n\n/**\n * Get default integrations, excluding performance.\n */\nexport function getDefaultIntegrationsWithoutPerformance(): Integration[] {\n  return [\n    // Common\n    // TODO(v10): Replace with `eventFiltersIntegration` once we remove the deprecated `inboundFiltersIntegration`\n    // eslint-disable-next-line deprecation/deprecation\n    inboundFiltersIntegration(),\n    functionToStringIntegration(),\n    linkedErrorsIntegration(),\n    requestDataIntegration(),\n    // Native Wrappers\n    consoleIntegration(),\n    httpIntegration(),\n    nativeNodeFetchIntegration(),\n    // Global Handlers\n    onUncaughtExceptionIntegration(),\n    onUnhandledRejectionIntegration(),\n    // Event Info\n    contextLinesIntegration(),\n    localVariablesIntegration(),\n    nodeContextIntegration(),\n    childProcessIntegration(),\n    processSessionIntegration(),\n    modulesIntegration(),\n  ];\n}\n\n/** Get the default integrations for the Node SDK. */\nexport function getDefaultIntegrations(options: Options): Integration[] {\n  return [\n    ...getDefaultIntegrationsWithoutPerformance(),\n    // We only add performance integrations if tracing is enabled\n    // Note that this means that without tracing enabled, e.g. `expressIntegration()` will not be added\n    // This means that generally request isolation will work (because that is done by httpIntegration)\n    // But `transactionName` will not be set automatically\n    ...(hasSpansEnabled(options) ? getAutoPerformanceIntegrations() : []),\n  ];\n}\n\n/**\n * Initialize Sentry for Node.\n */\nexport function init(options: NodeOptions | undefined = {}): NodeClient | undefined {\n  return _init(options, getDefaultIntegrations);\n}\n\n/**\n * Initialize Sentry for Node, without any integrations added by default.\n */\nexport function initWithoutDefaultIntegrations(options: NodeOptions | undefined = {}): NodeClient {\n  return _init(options, () => []);\n}\n\n/**\n * Initialize Sentry for Node, without performance instrumentation.\n */\nfunction _init(\n  _options: NodeOptions | undefined = {},\n  getDefaultIntegrationsImpl: (options: Options) => Integration[],\n): NodeClient {\n  const options = getClientOptions(_options, getDefaultIntegrationsImpl);\n\n  if (options.debug === true) {\n    if (DEBUG_BUILD) {\n      logger.enable();\n    } else {\n      // use `console.warn` rather than `logger.warn` since by non-debug bundles have all `logger.x` statements stripped\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.warn('[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.');\n      });\n    }\n  }\n\n  if (!isCjs() && options.registerEsmLoaderHooks !== false) {\n    maybeInitializeEsmLoader();\n  }\n\n  setOpenTelemetryContextAsyncContextStrategy();\n\n  const scope = getCurrentScope();\n  scope.update(options.initialScope);\n\n  if (options.spotlight && !options.integrations.some(({ name }) => name === SPOTLIGHT_INTEGRATION_NAME)) {\n    options.integrations.push(\n      spotlightIntegration({\n        sidecarUrl: typeof options.spotlight === 'string' ? options.spotlight : undefined,\n      }),\n    );\n  }\n\n  const client = new NodeClient(options);\n  // The client is on the current scope, from where it generally is inherited\n  getCurrentScope().setClient(client);\n\n  client.init();\n\n  logger.log(`Running in ${isCjs() ? 'CommonJS' : 'ESM'} mode.`);\n\n  client.startClientReportTracking();\n\n  updateScopeFromEnvVariables();\n\n  // If users opt-out of this, they _have_ to set up OpenTelemetry themselves\n  // There is no way to use this SDK without OpenTelemetry!\n  if (!options.skipOpenTelemetrySetup) {\n    initOpenTelemetry(client, {\n      spanProcessors: options.openTelemetrySpanProcessors,\n    });\n    validateOpenTelemetrySetup();\n  }\n\n  enhanceDscWithOpenTelemetryRootSpanName(client);\n  setupEventContextTrace(client);\n\n  return client;\n}\n\n/**\n * Validate that your OpenTelemetry setup is correct.\n */\nexport function validateOpenTelemetrySetup(): void {\n  if (!DEBUG_BUILD) {\n    return;\n  }\n\n  const setup = openTelemetrySetupCheck();\n\n  const required: ReturnType<typeof openTelemetrySetupCheck> = ['SentryContextManager', 'SentryPropagator'];\n\n  if (hasSpansEnabled()) {\n    required.push('SentrySpanProcessor');\n  }\n\n  for (const k of required) {\n    if (!setup.includes(k)) {\n      logger.error(\n        `You have to set up the ${k}. Without this, the OpenTelemetry & Sentry integration will not work properly.`,\n      );\n    }\n  }\n\n  if (!setup.includes('SentrySampler')) {\n    logger.warn(\n      'You have to set up the SentrySampler. Without this, the OpenTelemetry & Sentry integration may still work, but sample rates set for the Sentry SDK will not be respected. If you use a custom sampler, make sure to use `wrapSamplingDecision`.',\n    );\n  }\n}\n\nfunction getClientOptions(\n  options: NodeOptions,\n  getDefaultIntegrationsImpl: (options: Options) => Integration[],\n): NodeClientOptions {\n  const release = getRelease(options.release);\n  const spotlight =\n    options.spotlight ?? envToBool(process.env.SENTRY_SPOTLIGHT, { strict: true }) ?? process.env.SENTRY_SPOTLIGHT;\n  const tracesSampleRate = getTracesSampleRate(options.tracesSampleRate);\n\n  const mergedOptions = {\n    ...options,\n    dsn: options.dsn ?? process.env.SENTRY_DSN,\n    environment: options.environment ?? process.env.SENTRY_ENVIRONMENT,\n    sendClientReports: options.sendClientReports ?? true,\n    transport: options.transport ?? makeNodeTransport,\n    stackParser: stackParserFromStackParserOptions(options.stackParser || defaultStackParser),\n    release,\n    tracesSampleRate,\n    spotlight,\n    debug: envToBool(options.debug ?? process.env.SENTRY_DEBUG),\n  };\n\n  const integrations = options.integrations;\n  const defaultIntegrations = options.defaultIntegrations ?? getDefaultIntegrationsImpl(mergedOptions);\n\n  return {\n    ...mergedOptions,\n    integrations: getIntegrationsToSetup({\n      defaultIntegrations,\n      integrations,\n    }),\n  };\n}\n\nfunction getRelease(release: NodeOptions['release']): string | undefined {\n  if (release !== undefined) {\n    return release;\n  }\n\n  const detectedRelease = getSentryRelease();\n  if (detectedRelease !== undefined) {\n    return detectedRelease;\n  }\n\n  return undefined;\n}\n\nfunction getTracesSampleRate(tracesSampleRate: NodeOptions['tracesSampleRate']): number | undefined {\n  if (tracesSampleRate !== undefined) {\n    return tracesSampleRate;\n  }\n\n  const sampleRateFromEnv = process.env.SENTRY_TRACES_SAMPLE_RATE;\n  if (!sampleRateFromEnv) {\n    return undefined;\n  }\n\n  const parsed = parseFloat(sampleRateFromEnv);\n  return isFinite(parsed) ? parsed : undefined;\n}\n\n/**\n * Update scope and propagation context based on environmental variables.\n *\n * See https://github.com/getsentry/rfcs/blob/main/text/0071-continue-trace-over-process-boundaries.md\n * for more details.\n */\nfunction updateScopeFromEnvVariables(): void {\n  if (envToBool(process.env.SENTRY_USE_ENVIRONMENT) !== false) {\n    const sentryTraceEnv = process.env.SENTRY_TRACE;\n    const baggageEnv = process.env.SENTRY_BAGGAGE;\n    const propagationContext = propagationContextFromHeaders(sentryTraceEnv, baggageEnv);\n    getCurrentScope().setPropagationContext(propagationContext);\n  }\n}\n"], "names": ["SPOTLIGHT_INTEGRATION_NAME"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA0CA;AACA;AACA;AACO,SAAS,wCAAwC,GAAkB;AAC1E,EAAE,OAAO;AACT;AACA;AACA;AACA,IAAI,yBAAyB,EAAE;AAC/B,IAAI,2BAA2B,EAAE;AACjC,IAAI,uBAAuB,EAAE;AAC7B,IAAI,sBAAsB,EAAE;AAC5B;AACA,IAAI,kBAAkB,EAAE;AACxB,IAAI,eAAe,EAAE;AACrB,IAAI,0BAA0B,EAAE;AAChC;AACA,IAAI,8BAA8B,EAAE;AACpC,IAAI,+BAA+B,EAAE;AACrC;AACA,IAAI,uBAAuB,EAAE;AAC7B,IAAI,yBAAyB,EAAE;AAC/B,IAAI,sBAAsB,EAAE;AAC5B,IAAI,uBAAuB,EAAE;AAC7B,IAAI,yBAAyB,EAAE;AAC/B,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH;;AAEA;AACO,SAAS,sBAAsB,CAAC,OAAO,EAA0B;AACxE,EAAE,OAAO;AACT,IAAI,GAAG,wCAAwC,EAAE;AACjD;AACA;AACA;AACA;AACA,IAAI,IAAI,eAAe,CAAC,OAAO,CAAA,GAAI,8BAA8B,EAAC,GAAI,EAAE,CAAC;AACzE,GAAG;AACH;;AAEA;AACA;AACA;AACO,SAAS,IAAI,CAAC,OAAO,GAA4B,EAAE,EAA0B;AACpF,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,sBAAsB,CAAC;AAC/C;;AAEA;AACA;AACA;AACO,SAAS,8BAA8B,CAAC,OAAO,GAA4B,EAAE,EAAc;AAClG,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;AACjC;;AAEA;AACA;AACA;AACA,SAAS,KAAK;AACd,EAAE,QAAQ,GAA4B,EAAE;AACxC,EAAE,0BAA0B;AAC5B,EAAc;AACd,EAAE,MAAM,UAAU,gBAAgB,CAAC,QAAQ,EAAE,0BAA0B,CAAC;;AAExE,EAAE,IAAI,OAAO,CAAC,KAAM,KAAI,IAAI,EAAE;AAC9B,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,MAAM,CAAC,MAAM,EAAE;AACrB,WAAW;AACX;AACA,MAAM,cAAc,CAAC,MAAM;AAC3B;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC;AACpG,OAAO,CAAC;AACR;AACA;;AAEA,EAAE,IAAI,CAAC,KAAK,EAAC,IAAK,OAAO,CAAC,sBAAA,KAA2B,KAAK,EAAE;AAC5D,IAAI,wBAAwB,EAAE;AAC9B;;AAEA,EAAE,2CAA2C,EAAE;;AAE/C,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE;AACjC,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;;AAEpC,EAAE,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAA,EAAM,KAAK,IAAK,KAAIA,gBAA0B,CAAC,EAAE;AAC1G,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI;AAC7B,MAAM,oBAAoB,CAAC;AAC3B,QAAQ,UAAU,EAAE,OAAO,OAAO,CAAC,SAAA,KAAc,QAAA,GAAW,OAAO,CAAC,SAAA,GAAY,SAAS;AACzF,OAAO,CAAC;AACR,KAAK;AACL;;AAEA,EAAE,MAAM,MAAO,GAAE,IAAI,UAAU,CAAC,OAAO,CAAC;AACxC;AACA,EAAE,eAAe,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;;AAErC,EAAE,MAAM,CAAC,IAAI,EAAE;;AAEf,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,EAAC,GAAI,UAAW,GAAE,KAAK,CAAC,MAAM,CAAC,CAAC;;AAEhE,EAAE,MAAM,CAAC,yBAAyB,EAAE;;AAEpC,EAAE,2BAA2B,EAAE;;AAE/B;AACA;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;AACvC,IAAI,iBAAiB,CAAC,MAAM,EAAE;AAC9B,MAAM,cAAc,EAAE,OAAO,CAAC,2BAA2B;AACzD,KAAK,CAAC;AACN,IAAI,0BAA0B,EAAE;AAChC;;AAEA,EAAE,uCAAuC,CAAC,MAAM,CAAC;AACjD,EAAE,sBAAsB,CAAC,MAAM,CAAC;;AAEhC,EAAE,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACO,SAAS,0BAA0B,GAAS;AACnD,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI;AACJ;;AAEA,EAAE,MAAM,KAAA,GAAQ,uBAAuB,EAAE;;AAEzC,EAAE,MAAM,QAAQ,GAA+C,CAAC,sBAAsB,EAAE,kBAAkB,CAAC;;AAE3G,EAAE,IAAI,eAAe,EAAE,EAAE;AACzB,IAAI,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC;AACxC;;AAEA,EAAE,KAAK,MAAM,CAAE,IAAG,QAAQ,EAAE;AAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAC5B,MAAM,MAAM,CAAC,KAAK;AAClB,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC,8EAA8E,CAAC;AACnH,OAAO;AACP;AACA;;AAEA,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;AACxC,IAAI,MAAM,CAAC,IAAI;AACf,MAAM,iPAAiP;AACvP,KAAK;AACL;AACA;;AAEA,SAAS,gBAAgB;AACzB,EAAE,OAAO;AACT,EAAE,0BAA0B;AAC5B,EAAqB;AACrB,EAAE,MAAM,UAAU,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC;AAC7C,EAAE,MAAM,SAAU;AAClB,IAAI,OAAO,CAAC,SAAU,IAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,IAAA,EAAM,CAAA,IAAK,OAAO,CAAC,GAAG,CAAC,gBAAgB;AAClH,EAAE,MAAM,mBAAmB,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC;;AAExE,EAAE,MAAM,gBAAgB;AACxB,IAAI,GAAG,OAAO;AACd,IAAI,GAAG,EAAE,OAAO,CAAC,GAAA,IAAO,OAAO,CAAC,GAAG,CAAC,UAAU;AAC9C,IAAI,WAAW,EAAE,OAAO,CAAC,WAAA,IAAe,OAAO,CAAC,GAAG,CAAC,kBAAkB;AACtE,IAAI,iBAAiB,EAAE,OAAO,CAAC,iBAAA,IAAqB,IAAI;AACxD,IAAI,SAAS,EAAE,OAAO,CAAC,SAAA,IAAa,iBAAiB;AACrD,IAAI,WAAW,EAAE,iCAAiC,CAAC,OAAO,CAAC,WAAA,IAAe,kBAAkB,CAAC;AAC7F,IAAI,OAAO;AACX,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,KAAM,IAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC/D,GAAG;;AAEH,EAAE,MAAM,YAAA,GAAe,OAAO,CAAC,YAAY;AAC3C,EAAE,MAAM,mBAAoB,GAAE,OAAO,CAAC,uBAAuB,0BAA0B,CAAC,aAAa,CAAC;;AAEtG,EAAE,OAAO;AACT,IAAI,GAAG,aAAa;AACpB,IAAI,YAAY,EAAE,sBAAsB,CAAC;AACzC,MAAM,mBAAmB;AACzB,MAAM,YAAY;AAClB,KAAK,CAAC;AACN,GAAG;AACH;;AAEA,SAAS,UAAU,CAAC,OAAO,EAA8C;AACzE,EAAE,IAAI,OAAQ,KAAI,SAAS,EAAE;AAC7B,IAAI,OAAO,OAAO;AAClB;;AAEA,EAAE,MAAM,eAAA,GAAkB,gBAAgB,EAAE;AAC5C,EAAE,IAAI,eAAgB,KAAI,SAAS,EAAE;AACrC,IAAI,OAAO,eAAe;AAC1B;;AAEA,EAAE,OAAO,SAAS;AAClB;;AAEA,SAAS,mBAAmB,CAAC,gBAAgB,EAAuD;AACpG,EAAE,IAAI,gBAAiB,KAAI,SAAS,EAAE;AACtC,IAAI,OAAO,gBAAgB;AAC3B;;AAEA,EAAE,MAAM,iBAAkB,GAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB;AACjE,EAAE,IAAI,CAAC,iBAAiB,EAAE;AAC1B,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,MAAM,MAAO,GAAE,UAAU,CAAC,iBAAiB,CAAC;AAC9C,EAAE,OAAO,QAAQ,CAAC,MAAM,IAAI,MAAA,GAAS,SAAS;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,2BAA2B,GAAS;AAC7C,EAAE,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAA,KAAM,KAAK,EAAE;AAC/D,IAAI,MAAM,cAAe,GAAE,OAAO,CAAC,GAAG,CAAC,YAAY;AACnD,IAAI,MAAM,UAAW,GAAE,OAAO,CAAC,GAAG,CAAC,cAAc;AACjD,IAAI,MAAM,qBAAqB,6BAA6B,CAAC,cAAc,EAAE,UAAU,CAAC;AACxF,IAAI,eAAe,EAAE,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;AAC/D;AACA;;;;"}