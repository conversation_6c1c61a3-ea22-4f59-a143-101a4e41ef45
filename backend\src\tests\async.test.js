describe('Async Test', () => {
  beforeAll(async () => {
    console.log('Async BeforeAll started');
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log('Async BeforeAll completed');
  });

  it('should handle async', async () => {
    console.log('Async test running');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(true).toBe(true);
    console.log('Async test completed');
  });
});
