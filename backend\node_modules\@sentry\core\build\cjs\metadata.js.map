{"version": 3, "file": "metadata.js", "sources": ["../../src/metadata.ts"], "sourcesContent": ["import type { Event } from './types-hoist/event';\nimport type { StackParser } from './types-hoist/stacktrace';\nimport { GLOBAL_OBJ } from './utils/worldwide';\n\n/** Keys are source filename/url, values are metadata objects. */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst filenameMetadataMap = new Map<string, any>();\n/** Set of stack strings that have already been parsed. */\nconst parsedStacks = new Set<string>();\n\nfunction ensureMetadataStacksAreParsed(parser: StackParser): void {\n  if (!GLOBAL_OBJ._sentryModuleMetadata) {\n    return;\n  }\n\n  for (const stack of Object.keys(GLOBAL_OBJ._sentryModuleMetadata)) {\n    const metadata = GLOBAL_OBJ._sentryModuleMetadata[stack];\n\n    if (parsedStacks.has(stack)) {\n      continue;\n    }\n\n    // Ensure this stack doesn't get parsed again\n    parsedStacks.add(stack);\n\n    const frames = parser(stack);\n\n    // Go through the frames starting from the top of the stack and find the first one with a filename\n    for (const frame of frames.reverse()) {\n      if (frame.filename) {\n        // Save the metadata for this filename\n        filenameMetadataMap.set(frame.filename, metadata);\n        break;\n      }\n    }\n  }\n}\n\n/**\n * Retrieve metadata for a specific JavaScript file URL.\n *\n * Metadata is injected by the Sentry bundler plugins using the `_experiments.moduleMetadata` config option.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function getMetadataForUrl(parser: StackParser, filename: string): any | undefined {\n  ensureMetadataStacksAreParsed(parser);\n  return filenameMetadataMap.get(filename);\n}\n\n/**\n * Adds metadata to stack frames.\n *\n * Metadata is injected by the Sentry bundler plugins using the `_experiments.moduleMetadata` config option.\n */\nexport function addMetadataToStackFrames(parser: StackParser, event: Event): void {\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event.exception!.values!.forEach(exception => {\n      if (!exception.stacktrace) {\n        return;\n      }\n\n      for (const frame of exception.stacktrace.frames || []) {\n        if (!frame.filename || frame.module_metadata) {\n          continue;\n        }\n\n        const metadata = getMetadataForUrl(parser, frame.filename);\n\n        if (metadata) {\n          frame.module_metadata = metadata;\n        }\n      }\n    });\n  } catch (_) {\n    // To save bundle size we're just try catching here instead of checking for the existence of all the different objects.\n  }\n}\n\n/**\n * Strips metadata from stack frames.\n */\nexport function stripMetadataFromStackFrames(event: Event): void {\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event.exception!.values!.forEach(exception => {\n      if (!exception.stacktrace) {\n        return;\n      }\n\n      for (const frame of exception.stacktrace.frames || []) {\n        delete frame.module_metadata;\n      }\n    });\n  } catch (_) {\n    // To save bundle size we're just try catching here instead of checking for the existence of all the different objects.\n  }\n}\n"], "names": ["GLOBAL_OBJ"], "mappings": ";;;;AAIA;AACA;AACA,MAAM,mBAAoB,GAAE,IAAI,GAAG,EAAe;AAClD;AACA,MAAM,YAAa,GAAE,IAAI,GAAG,EAAU;;AAEtC,SAAS,6BAA6B,CAAC,MAAM,EAAqB;AAClE,EAAE,IAAI,CAACA,oBAAU,CAAC,qBAAqB,EAAE;AACzC,IAAI;AACJ;;AAEA,EAAE,KAAK,MAAM,KAAA,IAAS,MAAM,CAAC,IAAI,CAACA,oBAAU,CAAC,qBAAqB,CAAC,EAAE;AACrE,IAAI,MAAM,WAAWA,oBAAU,CAAC,qBAAqB,CAAC,KAAK,CAAC;;AAE5D,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACjC,MAAM;AACN;;AAEA;AACA,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;;AAE3B,IAAI,MAAM,MAAO,GAAE,MAAM,CAAC,KAAK,CAAC;;AAEhC;AACA,IAAI,KAAK,MAAM,KAAM,IAAG,MAAM,CAAC,OAAO,EAAE,EAAE;AAC1C,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC1B;AACA,QAAQ,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACzD,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,MAAM,EAAe,QAAQ,EAA2B;AAC1F,EAAE,6BAA6B,CAAC,MAAM,CAAC;AACvC,EAAE,OAAO,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC,MAAM,EAAe,KAAK,EAAe;AAClF,EAAE,IAAI;AACN;AACA,IAAI,KAAK,CAAC,SAAS,CAAE,MAAM,CAAE,OAAO,CAAC,SAAA,IAAa;AAClD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;AACjC,QAAQ;AACR;;AAEA,MAAM,KAAK,MAAM,KAAA,IAAS,SAAS,CAAC,UAAU,CAAC,MAAA,IAAU,EAAE,EAAE;AAC7D,QAAQ,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,eAAe,EAAE;AACtD,UAAU;AACV;;AAEA,QAAQ,MAAM,QAAS,GAAE,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC;;AAElE,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,KAAK,CAAC,eAAgB,GAAE,QAAQ;AAC1C;AACA;AACA,KAAK,CAAC;AACN,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA;AACA;;AAEA;AACA;AACA;AACO,SAAS,4BAA4B,CAAC,KAAK,EAAe;AACjE,EAAE,IAAI;AACN;AACA,IAAI,KAAK,CAAC,SAAS,CAAE,MAAM,CAAE,OAAO,CAAC,SAAA,IAAa;AAClD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;AACjC,QAAQ;AACR;;AAEA,MAAM,KAAK,MAAM,KAAA,IAAS,SAAS,CAAC,UAAU,CAAC,MAAA,IAAU,EAAE,EAAE;AAC7D,QAAQ,OAAO,KAAK,CAAC,eAAe;AACpC;AACA,KAAK,CAAC;AACN,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA;AACA;;;;;;"}