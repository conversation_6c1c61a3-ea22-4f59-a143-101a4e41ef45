const jwt = require('jsonwebtoken');
const User = require('../models/user.model');
const ApiError = require('../utils/ApiError');

const validateJwtConfig = () => {
  if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
    throw new Error('Invalid JWT configuration - secret must be at least 32 characters');
  }
};

const auth = async (req, res, next) => {
  try {
    validateJwtConfig();
    
    const authHeader = req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new ApiError('Invalid authorization header', 401, 'INVALID_AUTH_HEADER');
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: ['HS256'],
      ignoreExpiration: false
    });

    const user = await User.findOne({
      _id: decoded._id,
      'tokens.token': token
    }).select('+tokens.token');

    if (!user) {
      throw new ApiError('Invalid authentication token', 401, 'INVALID_TOKEN');
    }

    req.token = token;
    req.user = user;
    next();
  } catch (error) {
    const statusCode = error.statusCode || 401;
    const message = error.message || 'Not authenticated';
    const code = error.code || 'AUTH_ERROR';
    
    res.status(statusCode).json({
      success: false,
      message,
      code
    });
  }
};

module.exports = auth;
