{"version": 3, "file": "eventbuilder.js", "sources": ["../../../src/utils/eventbuilder.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport type { Event, EventHint } from '../types-hoist/event';\nimport type { Exception } from '../types-hoist/exception';\nimport type { Extras } from '../types-hoist/extra';\nimport type { Mechanism } from '../types-hoist/mechanism';\nimport type { ParameterizedString } from '../types-hoist/parameterize';\nimport type { SeverityLevel } from '../types-hoist/severity';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { isError, isErrorEvent, isParameterizedString, isPlainObject } from './is';\nimport { addExceptionMechanism, addExceptionTypeValue } from './misc';\nimport { normalizeToSize } from './normalize';\nimport { extractExceptionKeysForMessage } from './object';\n\n/**\n * Extracts stack frames from the error.stack string\n */\nexport function parseStackFrames(stackParser: StackParser, error: Error): StackFrame[] {\n  return stackParser(error.stack || '', 1);\n}\n\n/**\n * Extracts stack frames from the error and builds a Sentry Exception\n */\nexport function exceptionFromError(stackParser: StackParser, error: Error): Exception {\n  const exception: Exception = {\n    type: error.name || error.constructor.name,\n    value: error.message,\n  };\n\n  const frames = parseStackFrames(stackParser, error);\n  if (frames.length) {\n    exception.stacktrace = { frames };\n  }\n\n  return exception;\n}\n\n/** If a plain object has a property that is an `Error`, return this error. */\nfunction getErrorPropertyFromObject(obj: Record<string, unknown>): Error | undefined {\n  for (const prop in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n      const value = obj[prop];\n      if (value instanceof Error) {\n        return value;\n      }\n    }\n  }\n\n  return undefined;\n}\n\nfunction getMessageForObject(exception: Record<string, unknown>): string {\n  if ('name' in exception && typeof exception.name === 'string') {\n    let message = `'${exception.name}' captured as exception`;\n\n    if ('message' in exception && typeof exception.message === 'string') {\n      message += ` with message '${exception.message}'`;\n    }\n\n    return message;\n  } else if ('message' in exception && typeof exception.message === 'string') {\n    return exception.message;\n  }\n\n  const keys = extractExceptionKeysForMessage(exception);\n\n  // Some ErrorEvent instances do not have an `error` property, which is why they are not handled before\n  // We still want to try to get a decent message for these cases\n  if (isErrorEvent(exception)) {\n    return `Event \\`ErrorEvent\\` captured as exception with message \\`${exception.message}\\``;\n  }\n\n  const className = getObjectClassName(exception);\n\n  return `${\n    className && className !== 'Object' ? `'${className}'` : 'Object'\n  } captured as exception with keys: ${keys}`;\n}\n\nfunction getObjectClassName(obj: unknown): string | undefined | void {\n  try {\n    const prototype: unknown | null = Object.getPrototypeOf(obj);\n    return prototype ? prototype.constructor.name : undefined;\n  } catch (e) {\n    // ignore errors here\n  }\n}\n\nfunction getException(\n  client: Client,\n  mechanism: Mechanism,\n  exception: unknown,\n  hint?: EventHint,\n): [Error, Extras | undefined] {\n  if (isError(exception)) {\n    return [exception, undefined];\n  }\n\n  // Mutate this!\n  mechanism.synthetic = true;\n\n  if (isPlainObject(exception)) {\n    const normalizeDepth = client?.getOptions().normalizeDepth;\n    const extras = { ['__serialized__']: normalizeToSize(exception as Record<string, unknown>, normalizeDepth) };\n\n    const errorFromProp = getErrorPropertyFromObject(exception);\n    if (errorFromProp) {\n      return [errorFromProp, extras];\n    }\n\n    const message = getMessageForObject(exception);\n    const ex = hint?.syntheticException || new Error(message);\n    ex.message = message;\n\n    return [ex, extras];\n  }\n\n  // This handles when someone does: `throw \"something awesome\";`\n  // We use synthesized Error here so we can extract a (rough) stack trace.\n  const ex = hint?.syntheticException || new Error(exception as string);\n  ex.message = `${exception}`;\n\n  return [ex, undefined];\n}\n\n/**\n * Builds and Event from a Exception\n * @hidden\n */\nexport function eventFromUnknownInput(\n  client: Client,\n  stackParser: StackParser,\n  exception: unknown,\n  hint?: EventHint,\n): Event {\n  const providedMechanism: Mechanism | undefined = hint?.data && (hint.data as { mechanism: Mechanism }).mechanism;\n  const mechanism: Mechanism = providedMechanism || {\n    handled: true,\n    type: 'generic',\n  };\n\n  const [ex, extras] = getException(client, mechanism, exception, hint);\n\n  const event: Event = {\n    exception: {\n      values: [exceptionFromError(stackParser, ex)],\n    },\n  };\n\n  if (extras) {\n    event.extra = extras;\n  }\n\n  addExceptionTypeValue(event, undefined, undefined);\n  addExceptionMechanism(event, mechanism);\n\n  return {\n    ...event,\n    event_id: hint?.event_id,\n  };\n}\n\n/**\n * Builds and Event from a Message\n * @hidden\n */\nexport function eventFromMessage(\n  stackParser: StackParser,\n  message: ParameterizedString,\n  level: SeverityLevel = 'info',\n  hint?: EventHint,\n  attachStacktrace?: boolean,\n): Event {\n  const event: Event = {\n    event_id: hint?.event_id,\n    level,\n  };\n\n  if (attachStacktrace && hint?.syntheticException) {\n    const frames = parseStackFrames(stackParser, hint.syntheticException);\n    if (frames.length) {\n      event.exception = {\n        values: [\n          {\n            value: message,\n            stacktrace: { frames },\n          },\n        ],\n      };\n      addExceptionMechanism(event, { synthetic: true });\n    }\n  }\n\n  if (isParameterizedString(message)) {\n    const { __sentry_template_string__, __sentry_template_values__ } = message;\n\n    event.logentry = {\n      message: __sentry_template_string__,\n      params: __sentry_template_values__,\n    };\n    return event;\n  }\n\n  event.message = message;\n  return event;\n}\n"], "names": ["extractExceptionKeysForMessage", "isErrorEvent", "isError", "isPlainObject", "normalizeToSize", "addExceptionTypeValue", "addExceptionMechanism", "isParameterizedString"], "mappings": ";;;;;;;AAcA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,WAAW,EAAe,KAAK,EAAuB;AACvF,EAAE,OAAO,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AAC1C;;AAEA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,WAAW,EAAe,KAAK,EAAoB;AACtF,EAAE,MAAM,SAAS,GAAc;AAC/B,IAAI,IAAI,EAAE,KAAK,CAAC,IAAA,IAAQ,KAAK,CAAC,WAAW,CAAC,IAAI;AAC9C,IAAI,KAAK,EAAE,KAAK,CAAC,OAAO;AACxB,GAAG;;AAEH,EAAE,MAAM,SAAS,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC;AACrD,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;AACrB,IAAI,SAAS,CAAC,UAAA,GAAa,EAAE,QAAQ;AACrC;;AAEA,EAAE,OAAO,SAAS;AAClB;;AAEA;AACA,SAAS,0BAA0B,CAAC,GAAG,EAA8C;AACrF,EAAE,KAAK,MAAM,IAAK,IAAG,GAAG,EAAE;AAC1B,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;AACzD,MAAM,MAAM,KAAM,GAAE,GAAG,CAAC,IAAI,CAAC;AAC7B,MAAM,IAAI,KAAM,YAAW,KAAK,EAAE;AAClC,QAAQ,OAAO,KAAK;AACpB;AACA;AACA;;AAEA,EAAE,OAAO,SAAS;AAClB;;AAEA,SAAS,mBAAmB,CAAC,SAAS,EAAmC;AACzE,EAAE,IAAI,MAAO,IAAG,SAAU,IAAG,OAAO,SAAS,CAAC,IAAA,KAAS,QAAQ,EAAE;AACjE,IAAI,IAAI,OAAQ,GAAE,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC;;AAE7D,IAAI,IAAI,SAAU,IAAG,SAAU,IAAG,OAAO,SAAS,CAAC,OAAA,KAAY,QAAQ,EAAE;AACzE,MAAM,OAAA,IAAW,CAAC,eAAe,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AACvD;;AAEA,IAAI,OAAO,OAAO;AAClB,GAAI,MAAK,IAAI,aAAa,SAAA,IAAa,OAAO,SAAS,CAAC,OAAQ,KAAI,QAAQ,EAAE;AAC9E,IAAI,OAAO,SAAS,CAAC,OAAO;AAC5B;;AAEA,EAAE,MAAM,IAAK,GAAEA,qCAA8B,CAAC,SAAS,CAAC;;AAExD;AACA;AACA,EAAE,IAAIC,eAAY,CAAC,SAAS,CAAC,EAAE;AAC/B,IAAI,OAAO,CAAC,0DAA0D,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;AAC7F;;AAEA,EAAE,MAAM,SAAU,GAAE,kBAAkB,CAAC,SAAS,CAAC;;AAEjD,EAAE,OAAO,CAAC;AACV,IAAA,SAAA,IAAA,SAAA,KAAA,QAAA,GAAA,CAAA,CAAA,EAAA,SAAA,CAAA,CAAA,CAAA,GAAA;AACA,GAAA,kCAAA,EAAA,IAAA,CAAA,CAAA;AACA;;AAEA,SAAA,kBAAA,CAAA,GAAA,EAAA;AACA,EAAA,IAAA;AACA,IAAA,MAAA,SAAA,GAAA,MAAA,CAAA,cAAA,CAAA,GAAA,CAAA;AACA,IAAA,OAAA,SAAA,GAAA,SAAA,CAAA,WAAA,CAAA,IAAA,GAAA,SAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA;AACA;;AAEA,SAAA,YAAA;AACA,EAAA,MAAA;AACA,EAAA,SAAA;AACA,EAAA,SAAA;AACA,EAAA,IAAA;AACA,EAAA;AACA,EAAA,IAAAC,UAAA,CAAA,SAAA,CAAA,EAAA;AACA,IAAA,OAAA,CAAA,SAAA,EAAA,SAAA,CAAA;AACA;;AAEA;AACA,EAAA,SAAA,CAAA,SAAA,GAAA,IAAA;;AAEA,EAAA,IAAAC,gBAAA,CAAA,SAAA,CAAA,EAAA;AACA,IAAA,MAAA,cAAA,GAAA,MAAA,EAAA,UAAA,EAAA,CAAA,cAAA;AACA,IAAA,MAAA,MAAA,GAAA,EAAA,CAAA,gBAAA,GAAAC,yBAAA,CAAA,SAAA,GAAA,cAAA,CAAA,EAAA;;AAEA,IAAA,MAAA,aAAA,GAAA,0BAAA,CAAA,SAAA,CAAA;AACA,IAAA,IAAA,aAAA,EAAA;AACA,MAAA,OAAA,CAAA,aAAA,EAAA,MAAA,CAAA;AACA;;AAEA,IAAA,MAAA,OAAA,GAAA,mBAAA,CAAA,SAAA,CAAA;AACA,IAAA,MAAA,EAAA,GAAA,IAAA,EAAA,kBAAA,IAAA,IAAA,KAAA,CAAA,OAAA,CAAA;AACA,IAAA,EAAA,CAAA,OAAA,GAAA,OAAA;;AAEA,IAAA,OAAA,CAAA,EAAA,EAAA,MAAA,CAAA;AACA;;AAEA;AACA;AACA,EAAA,MAAA,EAAA,GAAA,IAAA,EAAA,kBAAA,IAAA,IAAA,KAAA,CAAA,SAAA,EAAA;AACA,EAAA,EAAA,CAAA,OAAA,GAAA,CAAA,EAAA,SAAA,CAAA,CAAA;;AAEA,EAAA,OAAA,CAAA,EAAA,EAAA,SAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,qBAAA;AACA,EAAA,MAAA;AACA,EAAA,WAAA;AACA,EAAA,SAAA;AACA,EAAA,IAAA;AACA,EAAA;AACA,EAAA,MAAA,iBAAA,GAAA,IAAA,EAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,SAAA;AACA,EAAA,MAAA,SAAA,GAAA,iBAAA,IAAA;AACA,IAAA,OAAA,EAAA,IAAA;AACA,IAAA,IAAA,EAAA,SAAA;AACA,GAAA;;AAEA,EAAA,MAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,YAAA,CAAA,MAAA,EAAA,SAAA,EAAA,SAAA,EAAA,IAAA,CAAA;;AAEA,EAAA,MAAA,KAAA,GAAA;AACA,IAAA,SAAA,EAAA;AACA,MAAA,MAAA,EAAA,CAAA,kBAAA,CAAA,WAAA,EAAA,EAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;;AAEA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,KAAA,CAAA,KAAA,GAAA,MAAA;AACA;;AAEA,EAAAC,0BAAA,CAAA,KAAA,EAAA,SAAA,EAAA,SAAA,CAAA;AACA,EAAAC,0BAAA,CAAA,KAAA,EAAA,SAAA,CAAA;;AAEA,EAAA,OAAA;AACA,IAAA,GAAA,KAAA;AACA,IAAA,QAAA,EAAA,IAAA,EAAA,QAAA;AACA,GAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,gBAAA;AACA,EAAA,WAAA;AACA,EAAA,OAAA;AACA,EAAA,KAAA,GAAA,MAAA;AACA,EAAA,IAAA;AACA,EAAA,gBAAA;AACA,EAAA;AACA,EAAA,MAAA,KAAA,GAAA;AACA,IAAA,QAAA,EAAA,IAAA,EAAA,QAAA;AACA,IAAA,KAAA;AACA,GAAA;;AAEA,EAAA,IAAA,gBAAA,IAAA,IAAA,EAAA,kBAAA,EAAA;AACA,IAAA,MAAA,MAAA,GAAA,gBAAA,CAAA,WAAA,EAAA,IAAA,CAAA,kBAAA,CAAA;AACA,IAAA,IAAA,MAAA,CAAA,MAAA,EAAA;AACA,MAAA,KAAA,CAAA,SAAA,GAAA;AACA,QAAA,MAAA,EAAA;AACA,UAAA;AACA,YAAA,KAAA,EAAA,OAAA;AACA,YAAA,UAAA,EAAA,EAAA,MAAA,EAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA;AACA,MAAAA,0BAAA,CAAA,KAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA;AACA;AACA;;AAEA,EAAA,IAAAC,wBAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,MAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,GAAA,OAAA;;AAEA,IAAA,KAAA,CAAA,QAAA,GAAA;AACA,MAAA,OAAA,EAAA,0BAAA;AACA,MAAA,MAAA,EAAA,0BAAA;AACA,KAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,KAAA,CAAA,OAAA,GAAA,OAAA;AACA,EAAA,OAAA,KAAA;AACA;;;;;;;"}