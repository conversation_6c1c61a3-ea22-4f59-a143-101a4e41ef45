/*! @sentry/node 9.34.0 (04a587d) | https://github.com/getsentry/sentry-javascript */
import{Session as e}from"node:inspector/promises";import{workerData as t}from"node:worker_threads";const n="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,o=globalThis,i="9.34.0";const a=["debug","info","warn","error","log","assert","trace"],s={};function c(e){if(!("console"in o))return e();const t=o.console,n={},i=Object.keys(s);i.forEach((e=>{const o=s[e];n[e]=t[e],t[e]=o}));try{return e()}finally{i.forEach((e=>{t[e]=n[e]}))}}!function(e,t,n=o){const a=n.__SENTRY__=n.__SENTRY__||{},s=a[i]=a[i]||{};s[e]||(s[e]=t())}("logger",(function(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return n?a.forEach((n=>{t[n]=(...t)=>{e&&c((()=>{o.console[n](`Sentry Logger [${n}]:`,...t)}))}})):a.forEach((e=>{t[e]=()=>{}})),t}));const r="__SENTRY_ERROR_LOCAL_VARIABLES__";const u=t;function l(...e){u.debug&&c((()=>console.log("[LocalVariables Worker]",...e)))}async function f(e,t,n,o){const i=await e.post("Runtime.getProperties",{objectId:t,ownProperties:!0});o[n]=i.result.filter((e=>"length"!==e.name&&!isNaN(parseInt(e.name,10)))).sort(((e,t)=>parseInt(e.name,10)-parseInt(t.name,10))).map((e=>e.value?.value))}async function g(e,t,n,o){const i=await e.post("Runtime.getProperties",{objectId:t,ownProperties:!0});o[n]=i.result.map((e=>[e.name,e.value?.value])).reduce(((e,[t,n])=>(e[t]=n,e)),{})}function d(e,t){e.value&&("value"in e.value?void 0===e.value.value||null===e.value.value?t[e.name]=`<${e.value.value}>`:t[e.name]=e.value.value:"description"in e.value&&"function"!==e.value.type?t[e.name]=`<${e.value.description}>`:"undefined"===e.value.type&&(t[e.name]="<undefined>"))}async function b(e,t){const n=await e.post("Runtime.getProperties",{objectId:t,ownProperties:!0}),o={};for(const t of n.result)if(t.value?.objectId&&"Array"===t.value.className){const n=t.value.objectId;await f(e,n,t.name,o)}else if(t.value?.objectId&&"Object"===t.value.className){const n=t.value.objectId;await g(e,n,t.name,o)}else t.value&&d(t,o);return o}let p;(async function(){const t=new e;t.connectToMainThread(),l("Connected to main thread");let n=!1;t.on("Debugger.resumed",(()=>{n=!1})),t.on("Debugger.paused",(e=>{n=!0,async function(e,{reason:t,data:{objectId:n},callFrames:o}){if("exception"!==t&&"promiseRejection"!==t)return;if(p?.(),null==n)return;const i=[];for(let t=0;t<o.length;t++){const{scopeChain:n,functionName:a,this:s}=o[t],c=n.find((e=>"local"===e.type)),r="global"!==s.className&&s.className?`${s.className}.${a}`:a;if(void 0===c?.object.objectId)i[t]={function:r};else{const n=await b(e,c.object.objectId);i[t]={function:r,vars:n}}}await e.post("Runtime.callFunctionOn",{functionDeclaration:`function() { this.${r} = this.${r} || ${JSON.stringify(i)}; }`,silent:!0,objectId:n}),await e.post("Runtime.releaseObject",{objectId:n})}(t,e.params).then((async()=>{n&&await t.post("Debugger.resume")}),(async e=>{n&&await t.post("Debugger.resume")}))})),await t.post("Debugger.enable");const o=!1!==u.captureAllExceptions;if(await t.post("Debugger.setPauseOnExceptions",{state:o?"all":"uncaught"}),o){const e=u.maxExceptionsPerSecond||50;p=function(e,t,n){let o=0,i=5,a=0;return setInterval((()=>{0===a?o>e&&(i*=2,n(i),i>86400&&(i=86400),a=i):(a-=1,0===a&&t()),o=0}),1e3).unref(),()=>{o+=1}}(e,(async()=>{l("Rate-limit lifted."),await t.post("Debugger.setPauseOnExceptions",{state:"all"})}),(async e=>{l(`Rate-limit exceeded. Disabling capturing of caught exceptions for ${e} seconds.`),await t.post("Debugger.setPauseOnExceptions",{state:"uncaught"})}))}})().catch((e=>{l("Failed to start debugger",e)})),setInterval((()=>{}),1e4);
