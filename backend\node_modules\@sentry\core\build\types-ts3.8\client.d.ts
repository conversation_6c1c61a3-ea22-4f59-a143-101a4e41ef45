import { IntegrationIndex } from './integration';
import { Scope } from './scope';
import { Breadcrumb, BreadcrumbHint, FetchBreadcrumbHint, XhrBreadcrumbHint } from './types-hoist/breadcrumb';
import { CheckIn, MonitorConfig } from './types-hoist/checkin';
import { EventDropReason, Outcome } from './types-hoist/clientreport';
import { TraceContext } from './types-hoist/context';
import { DataCategory } from './types-hoist/datacategory';
import { DsnComponents } from './types-hoist/dsn';
import { DynamicSamplingContext, Envelope } from './types-hoist/envelope';
import { Event, EventHint } from './types-hoist/event';
import { EventProcessor } from './types-hoist/eventprocessor';
import { FeedbackEvent } from './types-hoist/feedback';
import { Integration } from './types-hoist/integration';
import { Log } from './types-hoist/log';
import { ClientOptions } from './types-hoist/options';
import { ParameterizedString } from './types-hoist/parameterize';
import { SdkMetadata } from './types-hoist/sdkmetadata';
import { Session, SessionAggregates } from './types-hoist/session';
import { SeverityLevel } from './types-hoist/severity';
import { Span, SpanAttributes, SpanContextData } from './types-hoist/span';
import { StartSpanOptions } from './types-hoist/startSpanOptions';
import { Transport, TransportMakeRequestResponse } from './types-hoist/transport';
/**
 * Base implementation for all JavaScript SDK clients.
 *
 * Call the constructor with the corresponding options
 * specific to the client subclass. To access these options later, use
 * {@link Client.getOptions}.
 *
 * If a Dsn is specified in the options, it will be parsed and stored. Use
 * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is
 * invalid, the constructor will throw a {@link SentryException}. Note that
 * without a valid Dsn, the SDK will not send any events to Sentry.
 *
 * Before sending an event, it is passed through
 * {@link Client._prepareEvent} to add SDK information and scope data
 * (breadcrumbs and context). To add more custom information, override this
 * method and extend the resulting prepared event.
 *
 * To issue automatically created events (e.g. via instrumentation), use
 * {@link Client.captureEvent}. It will prepare the event and pass it through
 * the callback lifecycle. To issue auto-breadcrumbs, use
 * {@link Client.addBreadcrumb}.
 *
 * @example
 * class NodeClient extends Client<NodeOptions> {
 *   public constructor(options: NodeOptions) {
 *     super(options);
 *   }
 *
 *   // ...
 * }
 */
export declare abstract class Client<O extends ClientOptions = ClientOptions> {
    /** Options passed to the SDK. */
    protected readonly _options: O;
    /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */
    protected readonly _dsn?: DsnComponents;
    protected readonly _transport?: Transport;
    /** Array of set up integrations. */
    protected _integrations: IntegrationIndex;
    /** Number of calls being processed */
    protected _numProcessing: number;
    protected _eventProcessors: EventProcessor[];
    /** Holds flushable  */
    private _outcomes;
    private _hooks;
    /**
     * Initializes this client instance.
     *
     * @param options Options for the client.
     */
    protected constructor(options: O);
    /**
     * Captures an exception event and sends it to Sentry.
     *
     * Unlike `captureException` exported from every SDK, this method requires that you pass it the current scope.
     */
    captureException(exception: unknown, hint?: EventHint, scope?: Scope): string;
    /**
     * Captures a message event and sends it to Sentry.
     *
     * Unlike `captureMessage` exported from every SDK, this method requires that you pass it the current scope.
     */
    captureMessage(message: ParameterizedString, level?: SeverityLevel, hint?: EventHint, currentScope?: Scope): string;
    /**
     * Captures a manually created event and sends it to Sentry.
     *
     * Unlike `captureEvent` exported from every SDK, this method requires that you pass it the current scope.
     */
    captureEvent(event: Event, hint?: EventHint, currentScope?: Scope): string;
    /**
     * Captures a session.
     */
    captureSession(session: Session): void;
    /**
     * Create a cron monitor check in and send it to Sentry. This method is not available on all clients.
     *
     * @param checkIn An object that describes a check in.
     * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want
     * to create a monitor automatically when sending a check in.
     * @param scope An optional scope containing event metadata.
     * @returns A string representing the id of the check in.
     */
    captureCheckIn?(checkIn: CheckIn, monitorConfig?: MonitorConfig, scope?: Scope): string;
    /**
     * Get the current Dsn.
     */
    getDsn(): DsnComponents | undefined;
    /**
     * Get the current options.
     */
    getOptions(): O;
    /**
     * Get the SDK metadata.
     * @see SdkMetadata
     */
    getSdkMetadata(): SdkMetadata | undefined;
    /**
     * Returns the transport that is used by the client.
     * Please note that the transport gets lazy initialized so it will only be there once the first event has been sent.
     */
    getTransport(): Transport | undefined;
    /**
     * Wait for all events to be sent or the timeout to expire, whichever comes first.
     *
     * @param timeout Maximum time in ms the client should wait for events to be flushed. Omitting this parameter will
     *   cause the client to wait until all events are sent before resolving the promise.
     * @returns A promise that will resolve with `true` if all events are sent before the timeout, or `false` if there are
     * still events in the queue when the timeout is reached.
     */
    flush(timeout?: number): PromiseLike<boolean>;
    /**
     * Flush the event queue and set the client to `enabled = false`. See {@link Client.flush}.
     *
     * @param {number} timeout Maximum time in ms the client should wait before shutting down. Omitting this parameter will cause
     *   the client to wait until all events are sent before disabling itself.
     * @returns {Promise<boolean>} A promise which resolves to `true` if the flush completes successfully before the timeout, or `false` if
     * it doesn't.
     */
    close(timeout?: number): PromiseLike<boolean>;
    /**
     * Get all installed event processors.
     */
    getEventProcessors(): EventProcessor[];
    /**
     * Adds an event processor that applies to any event processed by this client.
     */
    addEventProcessor(eventProcessor: EventProcessor): void;
    /**
     * Initialize this client.
     * Call this after the client was set on a scope.
     */
    init(): void;
    /**
     * Gets an installed integration by its name.
     *
     * @returns {Integration|undefined} The installed integration or `undefined` if no integration with that `name` was installed.
     */
    getIntegrationByName<T extends Integration = Integration>(integrationName: string): T | undefined;
    /**
     * Add an integration to the client.
     * This can be used to e.g. lazy load integrations.
     * In most cases, this should not be necessary,
     * and you're better off just passing the integrations via `integrations: []` at initialization time.
     * However, if you find the need to conditionally load & add an integration, you can use `addIntegration` to do so.
     */
    addIntegration(integration: Integration): void;
    /**
     * Send a fully prepared event to Sentry.
     */
    sendEvent(event: Event, hint?: EventHint): void;
    /**
     * Send a session or session aggregrates to Sentry.
     */
    sendSession(session: Session | SessionAggregates): void;
    /**
     * Record on the client that an event got dropped (ie, an event that will not be sent to Sentry).
     */
    recordDroppedEvent(reason: EventDropReason, category: DataCategory, count?: number): void;
    /**
     * Register a callback for whenever a span is started.
     * Receives the span as argument.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'spanStart', callback: (span: Span) => void): () => void;
    /**
     * Register a callback before span sampling runs. Receives a `samplingDecision` object argument with a `decision`
     * property that can be used to make a sampling decision that will be enforced, before any span sampling runs.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeSampling', callback: (samplingData: {
        spanAttributes: SpanAttributes;
        spanName: string;
        parentSampled?: boolean;
        parentSampleRate?: number;
        parentContext?: SpanContextData;
    }, samplingDecision: {
        decision: boolean;
    }) => void): void;
    /**
     * Register a callback for after a span is ended.
     * NOTE: The span cannot be mutated anymore in this callback.
     * Receives the span as argument.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'spanEnd', callback: (span: Span) => void): () => void;
    /**
     * Register a callback for when an idle span is allowed to auto-finish.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'idleSpanEnableAutoFinish', callback: (span: Span) => void): () => void;
    /**
     * Register a callback for transaction start and finish.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeEnvelope', callback: (envelope: Envelope) => void): () => void;
    /**
     * Register a callback that runs when stack frame metadata should be applied to an event.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'applyFrameMetadata', callback: (event: Event) => void): () => void;
    /**
     * Register a callback for before sending an event.
     * This is called right before an event is sent and should not be used to mutate the event.
     * Receives an Event & EventHint as arguments.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeSendEvent', callback: (event: Event, hint?: EventHint | undefined) => void): () => void;
    /**
     * Register a callback for before sending a session or session aggregrates..
     * Receives the session/aggregate as second argument.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeSendSession', callback: (session: Session | SessionAggregates) => void): () => void;
    /**
     * Register a callback for preprocessing an event,
     * before it is passed to (global) event processors.
     * Receives an Event & EventHint as arguments.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'preprocessEvent', callback: (event: Event, hint?: EventHint | undefined) => void): () => void;
    /**
     * Register a callback for postprocessing an event,
     * after it was passed to (global) event processors, before it is being sent.
     * Receives an Event & EventHint as arguments.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'postprocessEvent', callback: (event: Event, hint?: EventHint | undefined) => void): () => void;
    /**
     * Register a callback for when an event has been sent.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'afterSendEvent', callback: (event: Event, sendResponse: TransportMakeRequestResponse) => void): () => void;
    /**
     * Register a callback before a breadcrumb is added.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeAddBreadcrumb', callback: (breadcrumb: Breadcrumb, hint?: BreadcrumbHint) => void): () => void;
    /**
     * Register a callback when a DSC (Dynamic Sampling Context) is created.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'createDsc', callback: (dsc: DynamicSamplingContext, rootSpan?: Span) => void): () => void;
    /**
     * Register a callback when a Feedback event has been prepared.
     * This should be used to mutate the event. The options argument can hint
     * about what kind of mutation it expects.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeSendFeedback', callback: (feedback: FeedbackEvent, options?: {
        includeReplay?: boolean;
    }) => void): () => void;
    /**
     * Register a callback when the feedback widget is opened in a user's browser
     */
    on(hook: 'openFeedbackWidget', callback: () => void): () => void;
    /**
     * A hook for the browser tracing integrations to trigger a span start for a page load.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'startPageLoadSpan', callback: (options: StartSpanOptions, traceOptions?: {
        sentryTrace?: string | undefined;
        baggage?: string | undefined;
    }) => void): () => void;
    /**
     * A hook for browser tracing integrations to trigger a span for a navigation.
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'startNavigationSpan', callback: (options: StartSpanOptions) => void): () => void;
    /**
     * A hook for GraphQL client integration to enhance a span with request data.
     * @returns A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeOutgoingRequestSpan', callback: (span: Span, hint: XhrBreadcrumbHint | FetchBreadcrumbHint) => void): () => void;
    /**
     * A hook for GraphQL client integration to enhance a breadcrumb with request data.
     * @returns A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeOutgoingRequestBreadcrumb', callback: (breadcrumb: Breadcrumb, hint: XhrBreadcrumbHint | FetchBreadcrumbHint) => void): () => void;
    /**
     * A hook that is called when the client is flushing
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'flush', callback: () => void): () => void;
    /**
     * A hook that is called when the client is closing
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'close', callback: () => void): () => void;
    /**
     * A hook that is called before a log is captured. This hooks runs before `beforeSendLog` is fired.
     *
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'beforeCaptureLog', callback: (log: Log) => void): () => void;
    /**
     * A hook that is called after a log is captured
     *
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'afterCaptureLog', callback: (log: Log) => void): () => void;
    /**
     * A hook that is called when the client is flushing logs
     *
     * @returns {() => void} A function that, when executed, removes the registered callback.
     */
    on(hook: 'flushLogs', callback: () => void): () => void;
    /** Fire a hook whenever a span starts. */
    emit(hook: 'spanStart', span: Span): void;
    /** A hook that is called every time before a span is sampled. */
    emit(hook: 'beforeSampling', samplingData: {
        spanAttributes: SpanAttributes;
        spanName: string;
        parentSampled?: boolean;
        parentSampleRate?: number;
        parentContext?: SpanContextData;
    }, samplingDecision: {
        decision: boolean;
    }): void;
    /** Fire a hook whenever a span ends. */
    emit(hook: 'spanEnd', span: Span): void;
    /**
     * Fire a hook indicating that an idle span is allowed to auto finish.
     */
    emit(hook: 'idleSpanEnableAutoFinish', span: Span): void;
    emit(hook: 'beforeEnvelope', envelope: Envelope): void;
    emit(hook: 'applyFrameMetadata', event: Event): void;
    /**
     * Fire a hook event before sending an event.
     * This is called right before an event is sent and should not be used to mutate the event.
     * Expects to be given an Event & EventHint as the second/third argument.
     */
    emit(hook: 'beforeSendEvent', event: Event, hint?: EventHint): void;
    /**
     * Fire a hook event before sending a session/aggregates.
     * Expects to be given the prepared session/aggregates as second argument.
     */
    emit(hook: 'beforeSendSession', session: Session | SessionAggregates): void;
    /**
     * Fire a hook event to process events before they are passed to (global) event processors.
     * Expects to be given an Event & EventHint as the second/third argument.
     */
    emit(hook: 'preprocessEvent', event: Event, hint?: EventHint): void;
    /**
     * Fire a hook event to process a user on an event before it is sent to Sentry, after all other processors have run.
     * Expects to be given an Event & EventHint as the second/third argument.
     */
    emit(hook: 'postprocessEvent', event: Event, hint?: EventHint): void;
    emit(hook: 'afterSendEvent', event: Event, sendResponse: TransportMakeRequestResponse): void;
    /**
     * Fire a hook for when a breadcrumb is added. Expects the breadcrumb as second argument.
     */
    emit(hook: 'beforeAddBreadcrumb', breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void;
    /**
     * Fire a hook for when a DSC (Dynamic Sampling Context) is created. Expects the DSC as second argument.
     */
    emit(hook: 'createDsc', dsc: DynamicSamplingContext, rootSpan?: Span): void;
    /**
     * Fire a hook event for after preparing a feedback event. Events to be given
     * a feedback event as the second argument, and an optional options object as
     * third argument.
     */
    emit(hook: 'beforeSendFeedback', feedback: FeedbackEvent, options?: {
        includeReplay?: boolean;
    }): void;
    /**
     * Fire a hook event for when the feedback widget is opened in a user's browser
     */
    emit(hook: 'openFeedbackWidget'): void;
    /**
     * Emit a hook event for browser tracing integrations to trigger a span start for a page load.
     */
    emit(hook: 'startPageLoadSpan', options: StartSpanOptions, traceOptions?: {
        sentryTrace?: string | undefined;
        baggage?: string | undefined;
    }): void;
    /**
     * Emit a hook event for browser tracing integrations to trigger a span for a navigation.
     */
    emit(hook: 'startNavigationSpan', options: StartSpanOptions): void;
    /**
     * Emit a hook event for GraphQL client integration to enhance a span with request data.
     */
    emit(hook: 'beforeOutgoingRequestSpan', span: Span, hint: XhrBreadcrumbHint | FetchBreadcrumbHint): void;
    /**
     * Emit a hook event for GraphQL client integration to enhance a breadcrumb with request data.
     */
    emit(hook: 'beforeOutgoingRequestBreadcrumb', breadcrumb: Breadcrumb, hint: XhrBreadcrumbHint | FetchBreadcrumbHint): void;
    /**
     * Emit a hook event for client flush
     */
    emit(hook: 'flush'): void;
    /**
     * Emit a hook event for client close
     */
    emit(hook: 'close'): void;
    /**
     * Emit a hook event for client before capturing a log. This hooks runs before `beforeSendLog` is fired.
     */
    emit(hook: 'beforeCaptureLog', log: Log): void;
    /**
     * Emit a hook event for client after capturing a log.
     */
    emit(hook: 'afterCaptureLog', log: Log): void;
    /**
     * Emit a hook event for client flush logs
     */
    emit(hook: 'flushLogs'): void;
    /**
     * Send an envelope to Sentry.
     */
    sendEnvelope(envelope: Envelope): PromiseLike<TransportMakeRequestResponse>;
    /** Setup integrations for this client. */
    protected _setupIntegrations(): void;
    /** Updates existing session based on the provided event */
    protected _updateSessionFromEvent(session: Session, event: Event): void;
    /**
     * Determine if the client is finished processing. Returns a promise because it will wait `timeout` ms before saying
     * "no" (resolving to `false`) in order to give the client a chance to potentially finish first.
     *
     * @param timeout The time, in ms, after which to resolve to `false` if the client is still busy. Passing `0` (or not
     * passing anything) will make the promise wait as long as it takes for processing to finish before resolving to
     * `true`.
     * @returns A promise which will resolve to `true` if processing is already done or finishes before the timeout, and
     * `false` otherwise
     */
    protected _isClientDoneProcessing(timeout?: number): PromiseLike<boolean>;
    /** Determines whether this SDK is enabled and a transport is present. */
    protected _isEnabled(): boolean;
    /**
     * Adds common information to events.
     *
     * The information includes release and environment from `options`,
     * breadcrumbs and context (extra, tags and user) from the scope.
     *
     * Information that is already present in the event is never overwritten. For
     * nested objects, such as the context, keys are merged.
     *
     * @param event The original event.
     * @param hint May contain additional information about the original exception.
     * @param currentScope A scope containing event metadata.
     * @returns A new event with more information.
     */
    protected _prepareEvent(event: Event, hint: EventHint, currentScope: Scope, isolationScope: Scope): PromiseLike<Event | null>;
    /**
     * Processes the event and logs an error in case of rejection
     * @param event
     * @param hint
     * @param scope
     */
    protected _captureEvent(event: Event, hint?: EventHint, currentScope?: Scope, isolationScope?: Scope): PromiseLike<string | undefined>;
    /**
     * Processes an event (either error or message) and sends it to Sentry.
     *
     * This also adds breadcrumbs and context information to the event. However,
     * platform specific meta data (such as the User's IP address) must be added
     * by the SDK implementor.
     *
     *
     * @param event The event to send to Sentry.
     * @param hint May contain additional information about the original exception.
     * @param currentScope A scope containing event metadata.
     * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.
     */
    protected _processEvent(event: Event, hint: EventHint, currentScope: Scope, isolationScope: Scope): PromiseLike<Event>;
    /**
     * Occupies the client with processing and event
     */
    protected _process<T>(promise: PromiseLike<T>): void;
    /**
     * Clears outcomes on this client and returns them.
     */
    protected _clearOutcomes(): Outcome[];
    /**
     * Sends client reports as an envelope.
     */
    protected _flushOutcomes(): void;
    /**
     * Creates an {@link Event} from all inputs to `captureException` and non-primitive inputs to `captureMessage`.
     */
    abstract eventFromException(_exception: unknown, _hint?: EventHint): PromiseLike<Event>;
    /**
     * Creates an {@link Event} from primitive inputs to `captureMessage`.
     */
    abstract eventFromMessage(_message: ParameterizedString, _level?: SeverityLevel, _hint?: EventHint): PromiseLike<Event>;
}
/**
 * @deprecated Use `Client` instead. This alias may be removed in a future major version.
 */
export type BaseClient = Client;
/**
 * @deprecated Use `Client` instead. This alias may be removed in a future major version.
 */
export declare const BaseClient: typeof Client;
/** Extract trace information from scope */
export declare function _getTraceInfoFromScope(client: Client, scope: Scope | undefined): [
    /*dynamicSamplingContext*/ Partial<DynamicSamplingContext> | undefined,
    /*traceContext*/ TraceContext | undefined
];
//# sourceMappingURL=client.d.ts.map
