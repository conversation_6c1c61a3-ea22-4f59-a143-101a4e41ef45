// Global test setup configuration
require('dotenv').config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret-key-for-testing';
process.env.REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET || 'test-refresh-secret-key';

// For tests, we don't need MONGODB_URI as we use in-memory database
// Remove the requirement check for MONGODB_URI in test environment

console.log('Jest setup: Test environment configured');

module.exports = {};
