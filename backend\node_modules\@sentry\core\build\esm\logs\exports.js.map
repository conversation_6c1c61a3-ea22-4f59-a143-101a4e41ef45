{"version": 3, "file": "exports.js", "sources": ["../../../src/logs/exports.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { _getTraceInfoFromScope } from '../client';\nimport { getClient, getCurrentScope, getGlobalScope, getIsolationScope } from '../currentScopes';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { Scope, ScopeData } from '../scope';\nimport type { Log, SerializedLog, SerializedLogAttributeValue } from '../types-hoist/log';\nimport { mergeScopeData } from '../utils/applyScopeDataToEvent';\nimport { isParameterizedString } from '../utils/is';\nimport { logger } from '../utils/logger';\nimport { _getSpanForScope } from '../utils/spanOnScope';\nimport { timestampInSeconds } from '../utils/time';\nimport { GLOBAL_OBJ } from '../utils/worldwide';\nimport { SEVERITY_TEXT_TO_SEVERITY_NUMBER } from './constants';\nimport { createLogEnvelope } from './envelope';\n\nconst MAX_LOG_BUFFER_SIZE = 100;\n\n// The reference to the Client <> LogBuffer map is stored to ensure it's always the same\nGLOBAL_OBJ._sentryClientToLogBufferMap = new WeakMap<Client, Array<SerializedLog>>();\n\n/**\n * Converts a log attribute to a serialized log attribute.\n *\n * @param key - The key of the log attribute.\n * @param value - The value of the log attribute.\n * @returns The serialized log attribute.\n */\nexport function logAttributeToSerializedLogAttribute(value: unknown): SerializedLogAttributeValue {\n  switch (typeof value) {\n    case 'number':\n      if (Number.isInteger(value)) {\n        return {\n          value,\n          type: 'integer',\n        };\n      }\n      return {\n        value,\n        type: 'double',\n      };\n    case 'boolean':\n      return {\n        value,\n        type: 'boolean',\n      };\n    case 'string':\n      return {\n        value,\n        type: 'string',\n      };\n    default: {\n      let stringValue = '';\n      try {\n        stringValue = JSON.stringify(value) ?? '';\n      } catch {\n        // Do nothing\n      }\n      return {\n        value: stringValue,\n        type: 'string',\n      };\n    }\n  }\n}\n\n/**\n * Sets a log attribute if the value exists and the attribute key is not already present.\n *\n * @param logAttributes - The log attributes object to modify.\n * @param key - The attribute key to set.\n * @param value - The value to set (only sets if truthy and key not present).\n * @param setEvenIfPresent - Whether to set the attribute if it is present. Defaults to true.\n */\nfunction setLogAttribute(\n  logAttributes: Record<string, unknown>,\n  key: string,\n  value: unknown,\n  setEvenIfPresent = true,\n): void {\n  if (value && (!logAttributes[key] || setEvenIfPresent)) {\n    logAttributes[key] = value;\n  }\n}\n\n/**\n * Captures a serialized log event and adds it to the log buffer for the given client.\n *\n * @param client - A client. Uses the current client if not provided.\n * @param serializedLog - The serialized log event to capture.\n *\n * @experimental This method will experience breaking changes. This is not yet part of\n * the stable Sentry SDK API and can be changed or removed without warning.\n */\nexport function _INTERNAL_captureSerializedLog(client: Client, serializedLog: SerializedLog): void {\n  const logBuffer = _INTERNAL_getLogBuffer(client);\n  if (logBuffer === undefined) {\n    GLOBAL_OBJ._sentryClientToLogBufferMap?.set(client, [serializedLog]);\n  } else {\n    GLOBAL_OBJ._sentryClientToLogBufferMap?.set(client, [...logBuffer, serializedLog]);\n    if (logBuffer.length >= MAX_LOG_BUFFER_SIZE) {\n      _INTERNAL_flushLogsBuffer(client, logBuffer);\n    }\n  }\n}\n\n/**\n * Captures a log event and sends it to Sentry.\n *\n * @param log - The log event to capture.\n * @param scope - A scope. Uses the current scope if not provided.\n * @param client - A client. Uses the current client if not provided.\n * @param captureSerializedLog - A function to capture the serialized log.\n *\n * @experimental This method will experience breaking changes. This is not yet part of\n * the stable Sentry SDK API and can be changed or removed without warning.\n */\nexport function _INTERNAL_captureLog(\n  beforeLog: Log,\n  client: Client | undefined = getClient(),\n  currentScope = getCurrentScope(),\n  captureSerializedLog: (client: Client, log: SerializedLog) => void = _INTERNAL_captureSerializedLog,\n): void {\n  if (!client) {\n    DEBUG_BUILD && logger.warn('No client available to capture log.');\n    return;\n  }\n\n  const { _experiments, release, environment } = client.getOptions();\n  const { enableLogs = false, beforeSendLog } = _experiments ?? {};\n  if (!enableLogs) {\n    DEBUG_BUILD && logger.warn('logging option not enabled, log will not be captured.');\n    return;\n  }\n\n  const [, traceContext] = _getTraceInfoFromScope(client, currentScope);\n\n  const processedLogAttributes = {\n    ...beforeLog.attributes,\n  };\n\n  const {\n    user: { id, email, username },\n  } = getMergedScopeData(currentScope);\n  setLogAttribute(processedLogAttributes, 'user.id', id, false);\n  setLogAttribute(processedLogAttributes, 'user.email', email, false);\n  setLogAttribute(processedLogAttributes, 'user.name', username, false);\n\n  setLogAttribute(processedLogAttributes, 'sentry.release', release);\n  setLogAttribute(processedLogAttributes, 'sentry.environment', environment);\n\n  const { name, version } = client.getSdkMetadata()?.sdk ?? {};\n  setLogAttribute(processedLogAttributes, 'sentry.sdk.name', name);\n  setLogAttribute(processedLogAttributes, 'sentry.sdk.version', version);\n\n  const beforeLogMessage = beforeLog.message;\n  if (isParameterizedString(beforeLogMessage)) {\n    const { __sentry_template_string__, __sentry_template_values__ = [] } = beforeLogMessage;\n    processedLogAttributes['sentry.message.template'] = __sentry_template_string__;\n    __sentry_template_values__.forEach((param, index) => {\n      processedLogAttributes[`sentry.message.parameter.${index}`] = param;\n    });\n  }\n\n  const span = _getSpanForScope(currentScope);\n  // Add the parent span ID to the log attributes for trace context\n  setLogAttribute(processedLogAttributes, 'sentry.trace.parent_span_id', span?.spanContext().spanId);\n\n  const processedLog = { ...beforeLog, attributes: processedLogAttributes };\n\n  client.emit('beforeCaptureLog', processedLog);\n\n  const log = beforeSendLog ? beforeSendLog(processedLog) : processedLog;\n  if (!log) {\n    client.recordDroppedEvent('before_send', 'log_item', 1);\n    DEBUG_BUILD && logger.warn('beforeSendLog returned null, log will not be captured.');\n    return;\n  }\n\n  const { level, message, attributes = {}, severityNumber } = log;\n\n  const serializedLog: SerializedLog = {\n    timestamp: timestampInSeconds(),\n    level,\n    body: message,\n    trace_id: traceContext?.trace_id,\n    severity_number: severityNumber ?? SEVERITY_TEXT_TO_SEVERITY_NUMBER[level],\n    attributes: Object.keys(attributes).reduce(\n      (acc, key) => {\n        acc[key] = logAttributeToSerializedLogAttribute(attributes[key]);\n        return acc;\n      },\n      {} as Record<string, SerializedLogAttributeValue>,\n    ),\n  };\n\n  captureSerializedLog(client, serializedLog);\n\n  client.emit('afterCaptureLog', log);\n}\n\n/**\n * Flushes the logs buffer to Sentry.\n *\n * @param client - A client.\n * @param maybeLogBuffer - A log buffer. Uses the log buffer for the given client if not provided.\n *\n * @experimental This method will experience breaking changes. This is not yet part of\n * the stable Sentry SDK API and can be changed or removed without warning.\n */\nexport function _INTERNAL_flushLogsBuffer(client: Client, maybeLogBuffer?: Array<SerializedLog>): void {\n  const logBuffer = maybeLogBuffer ?? _INTERNAL_getLogBuffer(client) ?? [];\n  if (logBuffer.length === 0) {\n    return;\n  }\n\n  const clientOptions = client.getOptions();\n  const envelope = createLogEnvelope(logBuffer, clientOptions._metadata, clientOptions.tunnel, client.getDsn());\n\n  // Clear the log buffer after envelopes have been constructed.\n  GLOBAL_OBJ._sentryClientToLogBufferMap?.set(client, []);\n\n  client.emit('flushLogs');\n\n  // sendEnvelope should not throw\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  client.sendEnvelope(envelope);\n}\n\n/**\n * Returns the log buffer for a given client.\n *\n * Exported for testing purposes.\n *\n * @param client - The client to get the log buffer for.\n * @returns The log buffer for the given client.\n */\nexport function _INTERNAL_getLogBuffer(client: Client): Array<SerializedLog> | undefined {\n  return GLOBAL_OBJ._sentryClientToLogBufferMap?.get(client);\n}\n\n/**\n * Get the scope data for the current scope after merging with the\n * global scope and isolation scope.\n *\n * @param currentScope - The current scope.\n * @returns The scope data.\n */\nfunction getMergedScopeData(currentScope: Scope): ScopeData {\n  const scopeData = getGlobalScope().getScopeData();\n  mergeScopeData(scopeData, getIsolationScope().getScopeData());\n  mergeScopeData(scopeData, currentScope.getScopeData());\n  return scopeData;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAeA,MAAM,mBAAA,GAAsB,GAAG;;AAE/B;AACA,UAAU,CAAC,2BAA4B,GAAE,IAAI,OAAO,EAAgC;;AAEpF;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oCAAoC,CAAC,KAAK,EAAwC;AAClG,EAAE,QAAQ,OAAO,KAAK;AACtB,IAAI,KAAK,QAAQ;AACjB,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AACnC,QAAQ,OAAO;AACf,UAAU,KAAK;AACf,UAAU,IAAI,EAAE,SAAS;AACzB,SAAS;AACT;AACA,MAAM,OAAO;AACb,QAAQ,KAAK;AACb,QAAQ,IAAI,EAAE,QAAQ;AACtB,OAAO;AACP,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO;AACb,QAAQ,KAAK;AACb,QAAQ,IAAI,EAAE,SAAS;AACvB,OAAO;AACP,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO;AACb,QAAQ,KAAK;AACb,QAAQ,IAAI,EAAE,QAAQ;AACtB,OAAO;AACP,IAAI,SAAS;AACb,MAAM,IAAI,WAAY,GAAE,EAAE;AAC1B,MAAM,IAAI;AACV,QAAQ,WAAA,GAAc,IAAI,CAAC,SAAS,CAAC,KAAK,CAAE,IAAG,EAAE;AACjD,QAAQ,MAAM;AACd;AACA;AACA,MAAM,OAAO;AACb,QAAQ,KAAK,EAAE,WAAW;AAC1B,QAAQ,IAAI,EAAE,QAAQ;AACtB,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe;AACxB,EAAE,aAAa;AACf,EAAE,GAAG;AACL,EAAE,KAAK;AACP,EAAE,gBAAA,GAAmB,IAAI;AACzB,EAAQ;AACR,EAAE,IAAI,KAAM,KAAI,CAAC,aAAa,CAAC,GAAG,CAAA,IAAK,gBAAgB,CAAC,EAAE;AAC1D,IAAI,aAAa,CAAC,GAAG,CAAA,GAAI,KAAK;AAC9B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,8BAA8B,CAAC,MAAM,EAAU,aAAa,EAAuB;AACnG,EAAE,MAAM,SAAU,GAAE,sBAAsB,CAAC,MAAM,CAAC;AAClD,EAAE,IAAI,SAAU,KAAI,SAAS,EAAE;AAC/B,IAAI,UAAU,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,aAAa,CAAC,CAAC;AACxE,SAAS;AACT,IAAI,UAAU,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,EAAE,aAAa,CAAC,CAAC;AACtF,IAAI,IAAI,SAAS,CAAC,MAAO,IAAG,mBAAmB,EAAE;AACjD,MAAM,yBAAyB,CAAC,MAAM,EAAE,SAAS,CAAC;AAClD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB;AACpC,EAAE,SAAS;AACX,EAAE,MAAM,GAAuB,SAAS,EAAE;AAC1C,EAAE,YAAa,GAAE,eAAe,EAAE;AAClC,EAAE,oBAAoB,GAAiD,8BAA8B;AACrG,EAAQ;AACR,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC;AACrE,IAAI;AACJ;;AAEA,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,WAAY,EAAA,GAAI,MAAM,CAAC,UAAU,EAAE;AACpE,EAAE,MAAM,EAAE,UAAW,GAAE,KAAK,EAAE,aAAc,EAAA,GAAI,YAAA,IAAgB,EAAE;AAClE,EAAE,IAAI,CAAC,UAAU,EAAE;AACnB,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC;AACvF,IAAI;AACJ;;AAEA,EAAE,MAAM,GAAG,YAAY,CAAA,GAAI,sBAAsB,CAAC,MAAM,EAAE,YAAY,CAAC;;AAEvE,EAAE,MAAM,yBAAyB;AACjC,IAAI,GAAG,SAAS,CAAC,UAAU;AAC3B,GAAG;;AAEH,EAAE,MAAM;AACR,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,UAAU;AACjC,GAAI,GAAE,kBAAkB,CAAC,YAAY,CAAC;AACtC,EAAE,eAAe,CAAC,sBAAsB,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC;AAC/D,EAAE,eAAe,CAAC,sBAAsB,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC;AACrE,EAAE,eAAe,CAAC,sBAAsB,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAEvE,EAAE,eAAe,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,OAAO,CAAC;AACpE,EAAE,eAAe,CAAC,sBAAsB,EAAE,oBAAoB,EAAE,WAAW,CAAC;;AAE5E,EAAE,MAAM,EAAE,IAAI,EAAE,OAAA,KAAY,MAAM,CAAC,cAAc,EAAE,EAAE,GAAI,IAAG,EAAE;AAC9D,EAAE,eAAe,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,IAAI,CAAC;AAClE,EAAE,eAAe,CAAC,sBAAsB,EAAE,oBAAoB,EAAE,OAAO,CAAC;;AAExE,EAAE,MAAM,gBAAA,GAAmB,SAAS,CAAC,OAAO;AAC5C,EAAE,IAAI,qBAAqB,CAAC,gBAAgB,CAAC,EAAE;AAC/C,IAAI,MAAM,EAAE,0BAA0B,EAAE,0BAAA,GAA6B,EAAC,EAAI,GAAE,gBAAgB;AAC5F,IAAI,sBAAsB,CAAC,yBAAyB,CAAA,GAAI,0BAA0B;AAClF,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;AACzD,MAAM,sBAAsB,CAAC,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA,CAAA,GAAA,KAAA;AACA,KAAA,CAAA;AACA;;AAEA,EAAA,MAAA,IAAA,GAAA,gBAAA,CAAA,YAAA,CAAA;AACA;AACA,EAAA,eAAA,CAAA,sBAAA,EAAA,6BAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA,MAAA,CAAA;;AAEA,EAAA,MAAA,YAAA,GAAA,EAAA,GAAA,SAAA,EAAA,UAAA,EAAA,sBAAA,EAAA;;AAEA,EAAA,MAAA,CAAA,IAAA,CAAA,kBAAA,EAAA,YAAA,CAAA;;AAEA,EAAA,MAAA,GAAA,GAAA,aAAA,GAAA,aAAA,CAAA,YAAA,CAAA,GAAA,YAAA;AACA,EAAA,IAAA,CAAA,GAAA,EAAA;AACA,IAAA,MAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,UAAA,EAAA,CAAA,CAAA;AACA,IAAA,WAAA,IAAA,MAAA,CAAA,IAAA,CAAA,wDAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,GAAA,EAAA,EAAA,cAAA,EAAA,GAAA,GAAA;;AAEA,EAAA,MAAA,aAAA,GAAA;AACA,IAAA,SAAA,EAAA,kBAAA,EAAA;AACA,IAAA,KAAA;AACA,IAAA,IAAA,EAAA,OAAA;AACA,IAAA,QAAA,EAAA,YAAA,EAAA,QAAA;AACA,IAAA,eAAA,EAAA,cAAA,IAAA,gCAAA,CAAA,KAAA,CAAA;AACA,IAAA,UAAA,EAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,MAAA;AACA,MAAA,CAAA,GAAA,EAAA,GAAA,KAAA;AACA,QAAA,GAAA,CAAA,GAAA,CAAA,GAAA,oCAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA;AACA,QAAA,OAAA,GAAA;AACA,OAAA;AACA,MAAA,EAAA;AACA,KAAA;AACA,GAAA;;AAEA,EAAA,oBAAA,CAAA,MAAA,EAAA,aAAA,CAAA;;AAEA,EAAA,MAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,GAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,yBAAA,CAAA,MAAA,EAAA,cAAA,EAAA;AACA,EAAA,MAAA,SAAA,GAAA,cAAA,IAAA,sBAAA,CAAA,MAAA,CAAA,IAAA,EAAA;AACA,EAAA,IAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,MAAA,aAAA,GAAA,MAAA,CAAA,UAAA,EAAA;AACA,EAAA,MAAA,QAAA,GAAA,iBAAA,CAAA,SAAA,EAAA,aAAA,CAAA,SAAA,EAAA,aAAA,CAAA,MAAA,EAAA,MAAA,CAAA,MAAA,EAAA,CAAA;;AAEA;AACA,EAAA,UAAA,CAAA,2BAAA,EAAA,GAAA,CAAA,MAAA,EAAA,EAAA,CAAA;;AAEA,EAAA,MAAA,CAAA,IAAA,CAAA,WAAA,CAAA;;AAEA;AACA;AACA,EAAA,MAAA,CAAA,YAAA,CAAA,QAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,sBAAA,CAAA,MAAA,EAAA;AACA,EAAA,OAAA,UAAA,CAAA,2BAAA,EAAA,GAAA,CAAA,MAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,kBAAA,CAAA,YAAA,EAAA;AACA,EAAA,MAAA,SAAA,GAAA,cAAA,EAAA,CAAA,YAAA,EAAA;AACA,EAAA,cAAA,CAAA,SAAA,EAAA,iBAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AACA,EAAA,cAAA,CAAA,SAAA,EAAA,YAAA,CAAA,YAAA,EAAA,CAAA;AACA,EAAA,OAAA,SAAA;AACA;;;;"}