{"version": 3, "file": "console.js", "sources": ["../../../src/integrations/console.ts"], "sourcesContent": ["import { addBreadcrumb } from '../breadcrumbs';\nimport { getClient } from '../currentScopes';\nimport { addConsoleInstrumentationHandler } from '../instrument/console';\nimport { defineIntegration } from '../integration';\nimport type { ConsoleLevel } from '../types-hoist/instrument';\nimport { CONSOLE_LEVELS } from '../utils/logger';\nimport { severityLevelFromString } from '../utils/severity';\nimport { safeJoin } from '../utils/string';\nimport { GLOBAL_OBJ } from '../utils/worldwide';\n\ninterface ConsoleIntegrationOptions {\n  levels: ConsoleLevel[];\n}\n\ntype GlobalObjectWithUtil = typeof GLOBAL_OBJ & {\n  util: {\n    format: (...args: unknown[]) => string;\n  };\n};\n\nconst INTEGRATION_NAME = 'Console';\n\n/**\n * Captures calls to the `console` API as breadcrumbs in Sentry.\n *\n * By default the integration instruments `console.debug`, `console.info`, `console.warn`, `console.error`,\n * `console.log`, `console.trace`, and `console.assert`. You can use the `levels` option to customize which\n * levels are captured.\n *\n * @example\n *\n * ```js\n * Sentry.init({\n *   integrations: [Sentry.consoleIntegration({ levels: ['error', 'warn'] })],\n * });\n * ```\n */\nexport const consoleIntegration = defineIntegration((options: Partial<ConsoleIntegrationOptions> = {}) => {\n  const levels = new Set(options.levels || CONSOLE_LEVELS);\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      addConsoleInstrumentationHandler(({ args, level }) => {\n        if (getClient() !== client || !levels.has(level)) {\n          return;\n        }\n\n        addConsoleBreadcrumb(level, args);\n      });\n    },\n  };\n});\n\n/**\n * Capture a console breadcrumb.\n *\n * Exported just for tests.\n */\nexport function addConsoleBreadcrumb(level: ConsoleLevel, args: unknown[]): void {\n  const breadcrumb = {\n    category: 'console',\n    data: {\n      arguments: args,\n      logger: 'console',\n    },\n    level: severityLevelFromString(level),\n    message: formatConsoleArgs(args),\n  };\n\n  if (level === 'assert') {\n    if (args[0] === false) {\n      const assertionArgs = args.slice(1);\n      breadcrumb.message =\n        assertionArgs.length > 0 ? `Assertion failed: ${formatConsoleArgs(assertionArgs)}` : 'Assertion failed';\n      breadcrumb.data.arguments = assertionArgs;\n    } else {\n      // Don't capture a breadcrumb for passed assertions\n      return;\n    }\n  }\n\n  addBreadcrumb(breadcrumb, {\n    input: args,\n    level,\n  });\n}\n\nfunction formatConsoleArgs(values: unknown[]): string {\n  return 'util' in GLOBAL_OBJ && typeof (GLOBAL_OBJ as GlobalObjectWithUtil).util.format === 'function'\n    ? (GLOBAL_OBJ as GlobalObjectWithUtil).util.format(...values)\n    : safeJoin(values, ' ');\n}\n"], "names": [], "mappings": ";;;;;;;;;AAoBA,MAAM,gBAAA,GAAmB,SAAS;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa,MAAA,kBAAA,GAAqB,iBAAiB,CAAC,CAAC,OAAO,GAAuC,EAAE,KAAK;AAC1G,EAAE,MAAM,MAAO,GAAE,IAAI,GAAG,CAAC,OAAO,CAAC,MAAA,IAAU,cAAc,CAAC;;AAE1D,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,gCAAgC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAA,EAAO,KAAK;AAC5D,QAAQ,IAAI,SAAS,OAAO,MAAA,IAAU,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1D,UAAU;AACV;;AAEA,QAAQ,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC;AACzC,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB,CAAC,KAAK,EAAgB,IAAI,EAAmB;AACjF,EAAE,MAAM,aAAa;AACrB,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,KAAK;AACL,IAAI,KAAK,EAAE,uBAAuB,CAAC,KAAK,CAAC;AACzC,IAAI,OAAO,EAAE,iBAAiB,CAAC,IAAI,CAAC;AACpC,GAAG;;AAEH,EAAE,IAAI,KAAM,KAAI,QAAQ,EAAE;AAC1B,IAAI,IAAI,IAAI,CAAC,CAAC,CAAE,KAAI,KAAK,EAAE;AAC3B,MAAM,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,MAAM,UAAU,CAAC,OAAQ;AACzB,QAAQ,aAAa,CAAC,MAAO,GAAE,IAAI,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAA,GAAA,kBAAA;AACA,MAAA,UAAA,CAAA,IAAA,CAAA,SAAA,GAAA,aAAA;AACA,KAAA,MAAA;AACA;AACA,MAAA;AACA;AACA;;AAEA,EAAA,aAAA,CAAA,UAAA,EAAA;AACA,IAAA,KAAA,EAAA,IAAA;AACA,IAAA,KAAA;AACA,GAAA,CAAA;AACA;;AAEA,SAAA,iBAAA,CAAA,MAAA,EAAA;AACA,EAAA,OAAA,MAAA,IAAA,UAAA,IAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,MAAA,KAAA;AACA,MAAA,CAAA,UAAA,GAAA,IAAA,CAAA,MAAA,CAAA,GAAA,MAAA;AACA,MAAA,QAAA,CAAA,MAAA,EAAA,GAAA,CAAA;AACA;;;;"}