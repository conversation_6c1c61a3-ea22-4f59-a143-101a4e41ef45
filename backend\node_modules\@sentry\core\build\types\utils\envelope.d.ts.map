{"version": 3, "file": "envelope.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/envelope.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AAC5D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACxD,OAAO,KAAK,EACV,cAAc,EAGd,QAAQ,EACR,gBAAgB,EAChB,oBAAoB,EACpB,QAAQ,EACT,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAC9D,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAKpD;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,CAAC,SAAS,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAE,CAAC,CAAC,CAAC,CAAM,GAAG,CAAC,CAErF;AAED;;;;GAIG;AACH,wBAAgB,iBAAiB,CAAC,CAAC,SAAS,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAG3F;AAED;;;;;GAKG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,QAAQ,EACpD,QAAQ,EAAE,QAAQ,EAClB,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,OAAO,GAAG,IAAI,GAClG,OAAO,CAaT;AAED;;GAEG;AACH,wBAAgB,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAE/F;AAkBD;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,GAAG,MAAM,GAAG,UAAU,CAmCzE;AAeD;;GAEG;AACH,wBAAgB,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,UAAU,GAAG,QAAQ,CAgChE;AAED;;GAEG;AACH,wBAAgB,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAM5E;AAED;;GAEG;AACH,wBAAgB,4BAA4B,CAAC,UAAU,EAAE,UAAU,GAAG,cAAc,CAanF;AAqBD;;GAEG;AACH,wBAAgB,8BAA8B,CAAC,IAAI,EAAE,gBAAgB,GAAG,YAAY,CAEnF;AAED,mEAAmE;AACnE,wBAAgB,+BAA+B,CAAC,eAAe,CAAC,EAAE,WAAW,GAAG,KAAK,GAAG,OAAO,GAAG,SAAS,CAM1G;AAED;;;GAGG;AACH,wBAAgB,0BAA0B,CACxC,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE,OAAO,GAAG,SAAS,EAC5B,MAAM,EAAE,MAAM,GAAG,SAAS,EAC1B,GAAG,CAAC,EAAE,aAAa,GAClB,oBAAoB,CAWtB"}