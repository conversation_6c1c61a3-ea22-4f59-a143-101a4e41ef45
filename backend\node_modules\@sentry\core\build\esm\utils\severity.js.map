{"version": 3, "file": "severity.js", "sources": ["../../../src/utils/severity.ts"], "sourcesContent": ["import type { SeverityLevel } from '../types-hoist/severity';\n\n/**\n * Converts a string-based level into a `SeverityLevel`, normalizing it along the way.\n *\n * @param level String representation of desired `SeverityLevel`.\n * @returns The `SeverityLevel` corresponding to the given string, or 'log' if the string isn't a valid level.\n */\nexport function severityLevelFromString(level: SeverityLevel | string): SeverityLevel {\n  return (\n    level === 'warn' ? 'warning' : ['fatal', 'error', 'warning', 'log', 'info', 'debug'].includes(level) ? level : 'log'\n  ) as SeverityLevel;\n}\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,KAAK,EAAyC;AACtF,EAAE;AACF,IAAI,KAAA,KAAU,MAAA,GAAS,SAAA,GAAY,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAA,GAAI,KAAA,GAAQ;AACnH;AACA;;;;"}