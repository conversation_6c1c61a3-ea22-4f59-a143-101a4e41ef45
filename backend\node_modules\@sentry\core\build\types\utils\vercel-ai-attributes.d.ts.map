{"version": 3, "file": "vercel-ai-attributes.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/vercel-ai-attributes.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAMH;;;GAGG;AACH,eAAO,MAAM,wBAAwB,mBAAmB,CAAC;AAEzD;;;GAGG;AACH,eAAO,MAAM,yBAAyB,mBAAmB,CAAC;AAM1D;;;;;;;GAOG;AACH,eAAO,MAAM,mBAAmB,cAAc,CAAC;AAE/C;;;;;;;GAOG;AACH,eAAO,MAAM,mBAAmB,cAAc,CAAC;AAE/C;;;;;;;GAOG;AACH,eAAO,MAAM,wBAAwB,mBAAmB,CAAC;AAEzD;;;;;;;GAOG;AACH,eAAO,MAAM,+BAA+B,0BAA0B,CAAC;AAEvE;;;;;;;GAOG;AACH,eAAO,MAAM,4BAA4B,uBAAuB,CAAC;AAEjE;;;;;;;GAOG;AACH,eAAO,MAAM,0BAA0B,qBAAqB,CAAC;AAE7D;;;;;;;GAOG;AACH,eAAO,MAAM,4BAA4B,uBAAuB,CAAC;AAEjE;;;;;;;GAOG;AACH,eAAO,MAAM,mBAAmB,cAAc,CAAC;AAE/C;;;;;;;GAOG;AACH,eAAO,MAAM,uBAAuB,kBAAkB,CAAC;AAMvD;;;;;GAKG;AACH,eAAO,MAAM,0BAA0B,qBAAqB,CAAC;AAE7D;;;;;GAKG;AACH,eAAO,MAAM,gCAAgC,0BAA0B,CAAC;AAExE;;;;;GAKG;AACH,eAAO,MAAM,mCAAmC,6BAA6B,CAAC;AAE9E;;;;;GAKG;AACH,eAAO,MAAM,+BAA+B,yBAAyB,CAAC;AAEtE;;;;;GAKG;AACH,eAAO,MAAM,0BAA0B,qBAAqB,CAAC;AAE7D;;;;;GAKG;AACH,eAAO,MAAM,4BAA4B,uBAAuB,CAAC;AAEjE;;;;;GAKG;AACH,eAAO,MAAM,yBAAyB,oBAAoB,CAAC;AAE3D;;;;;GAKG;AACH,eAAO,MAAM,+BAA+B,yBAAyB,CAAC;AAMtE;;;;;GAKG;AACH,eAAO,MAAM,uCAAuC,+BAA+B,CAAC;AAEpF;;;;;GAKG;AACH,eAAO,MAAM,kCAAkC,2BAA2B,CAAC;AAE3E;;;;;GAKG;AACH,eAAO,MAAM,sDAAsD,6CAA6C,CAAC;AAMjH;;;;;GAKG;AACH,eAAO,MAAM,kBAAkB,aAAa,CAAC;AAE7C;;;;;GAKG;AACH,eAAO,MAAM,sBAAsB,iBAAiB,CAAC;AAMrD;;;;;;GAMG;AACH,eAAO,MAAM,uBAAuB,kBAAkB,CAAC;AAEvD;;;;;;GAMG;AACH,eAAO,MAAM,qBAAqB,gBAAgB,CAAC;AAEnD;;;;;;GAMG;AACH,eAAO,MAAM,2BAA2B,sBAAsB,CAAC;AAE/D;;;;;;GAMG;AACH,eAAO,MAAM,4BAA4B,uBAAuB,CAAC;AAEjE;;;;;;GAMG;AACH,eAAO,MAAM,iCAAiC,2BAA2B,CAAC;AAE1E;;;;;;GAMG;AACH,eAAO,MAAM,kCAAkC,4BAA4B,CAAC;AAE5E;;;;;;GAMG;AACH,eAAO,MAAM,+BAA+B,0BAA0B,CAAC;AAEvE;;;;;;GAMG;AACH,eAAO,MAAM,oCAAoC,8BAA8B,CAAC;AAEhF;;;;;;GAMG;AACH,eAAO,MAAM,gCAAgC,0BAA0B,CAAC;AAMxE;;;;;;GAMG;AACH,eAAO,MAAM,2BAA2B,sBAAsB,CAAC;AAE/D;;;;;;GAMG;AACH,eAAO,MAAM,wBAAwB,mBAAmB,CAAC;AAEzD;;;;;;GAMG;AACH,eAAO,MAAM,+BAA+B,0BAA0B,CAAC;AAMvE;;;;;;GAMG;AACH,eAAO,MAAM,uBAAuB,kBAAkB,CAAC;AAEvD;;;;;;GAMG;AACH,eAAO,MAAM,8BAA8B,yBAAyB,CAAC;AAErE;;;;;;GAMG;AACH,eAAO,MAAM,oCAAoC,+BAA+B,CAAC;AAEjF;;;;;;GAMG;AACH,eAAO,MAAM,mCAAmC,8BAA8B,CAAC;AAE/E;;;;;;GAMG;AACH,eAAO,MAAM,0CAA0C,qCAAqC,CAAC;AAE7F;;;;;;GAMG;AACH,eAAO,MAAM,yCAAyC,oCAAoC,CAAC;AAE3F;;;;;;GAMG;AACH,eAAO,MAAM,8BAA8B,yBAAyB,CAAC;AAErE;;;;;;GAMG;AACH,eAAO,MAAM,8BAA8B,yBAAyB,CAAC;AAErE;;;;;;GAMG;AACH,eAAO,MAAM,uCAAuC,kCAAkC,CAAC;AAEvF;;;;;;GAMG;AACH,eAAO,MAAM,wCAAwC,mCAAmC,CAAC;AAEzF;;;;;;GAMG;AACH,eAAO,MAAM,+BAA+B,0BAA0B,CAAC;AAEvE;;;;;;GAMG;AACH,eAAO,MAAM,4BAA4B,uBAAuB,CAAC;AAEjE;;;;;;GAMG;AACH,eAAO,MAAM,mCAAmC,8BAA8B,CAAC;AAE/E;;;;;;GAMG;AACH,eAAO,MAAM,oCAAoC,+BAA+B,CAAC;AAMjF;;;;;;GAMG;AACH,eAAO,MAAM,yBAAyB,oBAAoB,CAAC;AAM3D;;;;;;GAMG;AACH,eAAO,MAAM,2BAA2B,qBAAqB,CAAC;AAE9D;;;;;;GAMG;AACH,eAAO,MAAM,yBAAyB,mBAAmB,CAAC;AAE1D;;;;;;GAMG;AACH,eAAO,MAAM,2BAA2B,qBAAqB,CAAC;AAE9D;;;;;;GAMG;AACH,eAAO,MAAM,6BAA6B,uBAAuB,CAAC;AAMlE;;;GAGG;AACH,eAAO,MAAM,gCAAgC;;;;;;;;;;;;;;;;;CAkBnC,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,4CAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoC/C,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,8BAA8B;;;;;;;;;;;;;CAcjC,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,wCAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmC3C,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,kCAAkC;;;;;;;;;;;;;;;;;;CAmBrC,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,gCAAgC;;;;;;;;;;;;;;;;;;CAmBnC,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,wBAAwB;;;;;;;;;;;;;CAe3B,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,iCAAiC;;;;;;;;;;;;;CAepC,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,6BAA6B;;;;;;;;;;;;;CAehC,CAAC;AAEX;;;GAGG;AACH,eAAO,MAAM,4BAA4B;;;;;;;;;;;;;;CAe/B,CAAC"}