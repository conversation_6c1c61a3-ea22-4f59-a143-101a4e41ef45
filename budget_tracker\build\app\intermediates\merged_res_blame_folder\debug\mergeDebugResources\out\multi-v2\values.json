{"logs": [{"outputFile": "com.example.budget_tracker.app-mergeDebugResources-49:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "405,454", "startColumns": "4,4", "startOffsets": "23339,27269", "endColumns": "67,166", "endOffsets": "23402,27431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "34,37,43,51,62,74,80,86,87,88,89,90,355,2286,2292,3612,3620,3635", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,1329,1502,1721,2094,2408,2596,2783,2836,2896,2948,2993,20743,145981,146176,191406,191688,192302", "endLines": "34,42,50,58,73,79,85,86,87,88,89,90,355,2291,2296,3619,3634,3650", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "1208,1497,1716,1935,2403,2591,2778,2831,2891,2943,2988,3027,20798,146171,146329,191683,192297,192951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3656,3728,3800,3873,3930,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,55,56,57,58,59,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3723,3795,3868,3925,3983,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "36,59,60,91,92,93,94,96,97,98,99,100,101,102,105,106,107,108,110,111,112,113,114,115,120,121,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,158,159,160,161,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,265,266,271,272,273,274,275,276,277,307,308,309,310,311,312,313,314,350,351,352,353,359,367,368,373,395,401,402,404,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,486,491,492,493,494,495,496,504,505,509,513,517,522,528,535,539,543,548,552,556,560,564,568,572,578,582,588,592,598,602,607,611,614,618,624,628,634,638,644,647,651,655,659,663,667,668,669,670,673,676,679,682,686,687,688,689,690,693,695,697,699,704,705,709,715,719,720,722,733,734,738,744,748,749,750,754,781,785,786,790,818,987,1013,1182,1208,1239,1247,1253,1267,1289,1294,1299,1309,1318,1327,1331,1338,1346,1353,1354,1363,1366,1369,1373,1377,1381,1384,1385,1390,1395,1405,1410,1417,1423,1424,1427,1431,1436,1438,1440,1443,1446,1448,1452,1455,1462,1465,1468,1472,1474,1478,1480,1482,1484,1488,1496,1504,1516,1522,1531,1534,1545,1548,1549,1554,1555,1584,1653,1723,1724,1734,1743,1895,1897,1901,1904,1907,1910,1913,1916,1919,1922,1926,1929,1932,1935,1939,1942,1946,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1972,1974,1975,1976,1977,1978,1979,1980,1981,1983,1984,1986,1987,1989,1991,1992,1994,1995,1996,1997,1998,1999,2001,2002,2003,2004,2005,2022,2024,2026,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2042,2043,2044,2045,2046,2047,2048,2050,2054,2070,2071,2072,2073,2074,2075,2079,2080,2081,2082,2084,2086,2088,2090,2092,2093,2094,2095,2097,2099,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2115,2116,2117,2118,2120,2122,2123,2125,2126,2128,2130,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2145,2146,2147,2148,2150,2151,2152,2153,2154,2156,2158,2160,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2182,2257,2260,2263,2266,2280,2297,2339,2368,2395,2404,2466,2830,2861,2999,3125,3149,3155,3184,3205,3329,3357,3363,3507,3533,3600,3671,3771,3791,3846,3858,3884", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1274,1940,1985,3032,3073,3128,3187,3322,3386,3456,3517,3592,3668,3745,3983,4068,4150,4226,4358,4435,4513,4619,4725,4804,5133,5190,6050,6124,6199,6264,6330,6390,6451,6523,6596,6663,6731,6790,6849,6908,6967,7026,7080,7134,7187,7241,7295,7349,7693,7767,7846,7919,8064,8136,8208,8281,8338,8469,8543,8617,8692,8764,8837,8907,8978,9038,9099,9168,9237,9307,9381,9457,9521,9598,9674,9751,9816,9885,9962,10037,10106,10174,10251,10317,10378,10475,10540,10609,10708,10779,10838,10896,10953,11012,11076,11147,11219,11291,11363,11435,11502,11570,11638,11697,11760,11824,11914,12005,12065,12131,12198,12264,12334,12398,12451,12518,12579,12646,12759,12817,12880,12945,13010,13085,13158,13230,13279,13340,13401,13462,13524,13588,13652,13716,13781,13844,13904,13965,14031,14090,14150,14212,14283,14343,15042,15128,15431,15521,15608,15696,15778,15861,15951,17888,17940,17998,18043,18109,18173,18230,18287,20464,20521,20569,20618,20947,21317,21364,21622,22793,23096,23160,23282,23603,23677,23747,23825,23879,23949,24034,24082,24128,24189,24252,24318,24382,24453,24516,24581,24645,24706,24767,24819,24892,24966,25035,25110,25184,25258,25399,30207,30568,30646,30736,30824,30920,31010,31592,31681,31928,32209,32461,32746,33139,33616,33838,34060,34336,34563,34793,35023,35253,35483,35710,36129,36355,36780,37010,37438,37657,37940,38148,38279,38506,38932,39157,39584,39805,40230,40350,40626,40927,41251,41542,41856,41993,42124,42229,42471,42638,42842,43050,43321,43433,43545,43650,43767,43981,44127,44267,44353,44701,44789,45035,45453,45702,45784,45882,46474,46574,46826,47250,47505,47599,47688,47925,49949,50191,50293,50546,52702,63143,64659,75198,76726,78483,79109,79529,80590,81855,82111,82347,82894,83388,83993,84191,84771,85335,85710,85828,86366,86523,86719,86992,87248,87418,87559,87623,87988,88355,89031,89295,89633,89986,90080,90266,90572,90834,90959,91086,91325,91536,91655,91848,92025,92480,92661,92783,93042,93155,93342,93444,93551,93680,93955,94463,94959,95836,96130,96700,96849,97581,97753,97837,98173,98265,100331,105577,110966,111028,111606,112190,120137,120250,120479,120639,120791,120962,121128,121297,121464,121627,121870,122040,122213,122384,122658,122857,123062,123392,123476,123572,123668,123766,123866,123968,124070,124172,124274,124376,124476,124572,124684,124813,124936,125067,125198,125296,125410,125504,125644,125778,125874,125986,126086,126202,126298,126410,126510,126650,126786,126950,127080,127238,127388,127529,127673,127808,127920,128070,128198,128326,128462,128594,128724,128854,128966,130246,130392,130536,130674,130740,130830,130906,131010,131100,131202,131310,131418,131518,131598,131690,131788,131898,131950,132028,132134,132226,132330,132440,132562,132725,133515,133595,133695,133785,133895,133985,134226,134320,134426,134518,134618,134730,134844,134960,135076,135170,135284,135396,135498,135618,135740,135822,135926,136046,136172,136270,136364,136452,136564,136680,136802,136914,137089,137205,137291,137383,137495,137619,137686,137812,137880,138008,138152,138280,138349,138444,138559,138672,138771,138880,138991,139102,139203,139308,139408,139538,139629,139752,139846,139958,140044,140148,140244,140332,140450,140554,140658,140784,140872,140980,141080,141170,141280,141364,141466,141550,141604,141668,141774,141860,141970,142054,142458,145074,145192,145307,145387,145748,146334,147738,149082,150443,150831,153606,163695,164735,171548,175924,176675,176937,177784,178163,182441,183295,183524,188132,189142,191094,193494,197618,198362,200493,200833,202144", "endLines": "36,59,60,91,92,93,94,96,97,98,99,100,101,102,105,106,107,108,110,111,112,113,114,115,120,121,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,158,159,160,161,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,265,266,271,272,273,274,275,276,277,307,308,309,310,311,312,313,314,350,351,352,353,359,367,368,373,395,401,402,404,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,486,491,492,493,494,495,503,504,508,512,516,521,527,534,538,542,547,551,555,559,563,567,571,577,581,587,591,597,601,606,610,613,617,623,627,633,637,643,646,650,654,658,662,666,667,668,669,672,675,678,681,685,686,687,688,689,692,694,696,698,703,704,708,714,718,719,721,732,733,737,743,747,748,749,753,780,784,785,789,817,986,1012,1181,1207,1238,1246,1252,1266,1288,1293,1298,1308,1317,1326,1330,1337,1345,1352,1353,1362,1365,1368,1372,1376,1380,1383,1384,1389,1394,1404,1409,1416,1422,1423,1426,1430,1435,1437,1439,1442,1445,1447,1451,1454,1461,1464,1467,1471,1473,1477,1479,1481,1483,1487,1495,1503,1515,1521,1530,1533,1544,1547,1548,1553,1554,1559,1652,1722,1723,1733,1742,1743,1896,1900,1903,1906,1909,1912,1915,1918,1921,1925,1928,1931,1934,1938,1941,1945,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1971,1973,1974,1975,1976,1977,1978,1979,1980,1982,1983,1985,1986,1988,1990,1991,1993,1994,1995,1996,1997,1998,2000,2001,2002,2003,2004,2005,2023,2025,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2041,2042,2043,2044,2045,2046,2047,2049,2053,2057,2070,2071,2072,2073,2074,2078,2079,2080,2081,2083,2085,2087,2089,2091,2092,2093,2094,2096,2098,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2114,2115,2116,2117,2119,2121,2122,2124,2125,2127,2129,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2144,2145,2146,2147,2149,2150,2151,2152,2153,2155,2157,2159,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2256,2259,2262,2265,2279,2285,2306,2367,2394,2403,2465,2824,2833,2888,3016,3148,3154,3160,3204,3328,3348,3362,3366,3512,3567,3611,3736,3790,3845,3857,3883,3890", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1324,1980,2029,3068,3123,3182,3244,3381,3451,3512,3587,3663,3740,3818,4063,4145,4221,4297,4430,4508,4614,4720,4799,4879,5185,5243,6119,6194,6259,6325,6385,6446,6518,6591,6658,6726,6785,6844,6903,6962,7021,7075,7129,7182,7236,7290,7344,7398,7762,7841,7914,7988,8131,8203,8276,8333,8391,8538,8612,8687,8759,8832,8902,8973,9033,9094,9163,9232,9302,9376,9452,9516,9593,9669,9746,9811,9880,9957,10032,10101,10169,10246,10312,10373,10470,10535,10604,10703,10774,10833,10891,10948,11007,11071,11142,11214,11286,11358,11430,11497,11565,11633,11692,11755,11819,11909,12000,12060,12126,12193,12259,12329,12393,12446,12513,12574,12641,12754,12812,12875,12940,13005,13080,13153,13225,13274,13335,13396,13457,13519,13583,13647,13711,13776,13839,13899,13960,14026,14085,14145,14207,14278,14338,14406,15123,15210,15516,15603,15691,15773,15856,15946,16037,17935,17993,18038,18104,18168,18225,18282,18336,20516,20564,20613,20664,20976,21359,21408,21663,22820,23155,23217,23334,23672,23742,23820,23874,23944,24029,24077,24123,24184,24247,24313,24377,24448,24511,24576,24640,24701,24762,24814,24887,24961,25030,25105,25179,25253,25394,25464,30255,30641,30731,30819,30915,31005,31587,31676,31923,32204,32456,32741,33134,33611,33833,34055,34331,34558,34788,35018,35248,35478,35705,36124,36350,36775,37005,37433,37652,37935,38143,38274,38501,38927,39152,39579,39800,40225,40345,40621,40922,41246,41537,41851,41988,42119,42224,42466,42633,42837,43045,43316,43428,43540,43645,43762,43976,44122,44262,44348,44696,44784,45030,45448,45697,45779,45877,46469,46569,46821,47245,47500,47594,47683,47920,49944,50186,50288,50541,52697,63138,64654,75193,76721,78478,79104,79524,80585,81850,82106,82342,82889,83383,83988,84186,84766,85330,85705,85823,86361,86518,86714,86987,87243,87413,87554,87618,87983,88350,89026,89290,89628,89981,90075,90261,90567,90829,90954,91081,91320,91531,91650,91843,92020,92475,92656,92778,93037,93150,93337,93439,93546,93675,93950,94458,94954,95831,96125,96695,96844,97576,97748,97832,98168,98260,98538,105572,110961,111023,111601,112185,112276,120245,120474,120634,120786,120957,121123,121292,121459,121622,121865,122035,122208,122379,122653,122852,123057,123387,123471,123567,123663,123761,123861,123963,124065,124167,124269,124371,124471,124567,124679,124808,124931,125062,125193,125291,125405,125499,125639,125773,125869,125981,126081,126197,126293,126405,126505,126645,126781,126945,127075,127233,127383,127524,127668,127803,127915,128065,128193,128321,128457,128589,128719,128849,128961,129101,130387,130531,130669,130735,130825,130901,131005,131095,131197,131305,131413,131513,131593,131685,131783,131893,131945,132023,132129,132221,132325,132435,132557,132720,132877,133590,133690,133780,133890,133980,134221,134315,134421,134513,134613,134725,134839,134955,135071,135165,135279,135391,135493,135613,135735,135817,135921,136041,136167,136265,136359,136447,136559,136675,136797,136909,137084,137200,137286,137378,137490,137614,137681,137807,137875,138003,138147,138275,138344,138439,138554,138667,138766,138875,138986,139097,139198,139303,139403,139533,139624,139747,139841,139953,140039,140143,140239,140327,140445,140549,140653,140779,140867,140975,141075,141165,141275,141359,141461,141545,141599,141663,141769,141855,141965,142049,142169,145069,145187,145302,145382,145743,145976,146846,149077,150438,150826,153601,163505,163825,166087,172115,176670,176932,177132,178158,182436,183042,183519,183670,188342,190220,191401,196515,198357,200488,200828,202139,202342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e1f6d2e0b1aa38467964f5b59b4f29f9\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "124,125,126,127,128,129,130,131,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,3171,3581", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5379,5469,5549,5639,5729,5809,5890,5970,26229,26334,26515,26640,26747,26927,27050,27166,27436,27624,27729,27910,28035,28210,28358,28421,28483,177469,190677", "endLines": "124,125,126,127,128,129,130,131,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,3183,3599", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5464,5544,5634,5724,5804,5885,5965,6045,26329,26510,26635,26742,26922,27045,27161,27264,27619,27724,27905,28030,28205,28353,28416,28478,28557,177779,191089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "356,372,400,3077,3082", "startColumns": "4,4,4,4,4", "startOffsets": "20803,21557,23032,174756,174926", "endLines": "356,372,400,3081,3085", "endColumns": "56,64,63,24,24", "endOffsets": "20855,21617,23091,174921,175070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f87704cc6ac259b753f491455f413615\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "357,358,363,370,371,390,391,392,393,394", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20860,20900,21117,21455,21510,22527,22581,22633,22682,22743", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20895,20942,21155,21505,21552,22576,22628,22677,22738,22788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b94913d5ef63be7d6118067409894863\\transformed\\jetified-credentials-play-services-auth-1.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2058", "startColumns": "4", "startOffsets": "132882", "endLines": "2061", "endColumns": "12", "endOffsets": "133100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "95,157,296,297,298,299,300,301,302,364,365,366,406,407,465,468,482,483,488,489,490,1560,1744,1747,1753,1759,1762,1768,1772,1775,1782,1788,1791,1797,1802,1807,1814,1816,1822,1828,1836,1841,1848,1853,1859,1863,1870,1874,1880,1886,1889,1893,1894,2825,2840,2979,3017,3161,3349,3367,3431,3441,3451,3458,3464,3568,3737,3754", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3249,7624,17243,17307,17362,17430,17497,17562,17619,21160,21208,21256,23407,23470,28638,28804,30023,30067,30331,30470,30520,98543,112281,112386,112631,112969,113115,113455,113667,113830,114237,114575,114698,115037,115276,115533,115904,115964,116302,116588,117037,117329,117717,118022,118366,118611,118941,119148,119416,119689,119833,120034,120081,163510,164033,170819,172120,177137,183047,183675,185600,185882,186187,186449,186709,190225,196520,197050", "endLines": "95,157,296,297,298,299,300,301,302,364,365,366,406,407,465,468,482,485,488,489,490,1576,1746,1752,1758,1761,1767,1771,1774,1781,1787,1790,1796,1801,1806,1813,1815,1821,1827,1835,1840,1847,1852,1858,1862,1869,1873,1879,1885,1888,1892,1893,1894,2829,2850,2998,3020,3170,3356,3430,3440,3450,3457,3463,3506,3580,3753,3770", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "3317,7688,17302,17357,17425,17492,17557,17614,17671,21203,21251,21312,23465,23528,28671,28856,30062,30202,30465,30515,30563,99976,112381,112626,112964,113110,113450,113662,113825,114232,114570,114693,115032,115271,115528,115899,115959,116297,116583,117032,117324,117712,118017,118361,118606,118936,119143,119411,119684,119828,120029,120076,120132,163690,164429,171543,172264,177464,183290,185595,185877,186182,186444,186704,188127,190672,197045,197613"}}, {"source": "E:\\BUDGET TRACKER\\budget_tracker\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1577,1581", "startColumns": "4,4", "startOffsets": "99981,100162", "endLines": "1580,1583", "endColumns": "12,12", "endOffsets": "100157,100326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\79275990ee9dddfd68bc7c9d7157e0cd\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "267,268,269,278,279,280,360,3513", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "15215,15274,15322,16042,16117,16193,20981,188347", "endLines": "267,268,269,278,279,280,360,3532", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "15269,15317,15373,16112,16188,16260,21042,189137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,109,270,464,467,472,473,474,475,476,477,478,479,480,481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,244,327,438,573,4302,15378,28562,28744,29076,29165,29264,29372,29469,29557,29657,29727,29824,29934", "endLines": "5,7,10,14,33,109,270,464,467,472,473,474,475,476,477,478,479,480,481", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "239,322,433,568,1149,4353,15426,28633,28799,29160,29259,29367,29464,29552,29652,29722,29819,29929,30018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "116,117,118,119,256,257,466,469,470,471", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4884,4942,5008,5071,14411,14482,28676,28861,28928,29007", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4937,5003,5066,5128,14477,14549,28739,28923,29002,29071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28f988f0d4c2cc22199e4c3cefdd595e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "35,2179,2889,2895", "startColumns": "4,4,4,4", "startOffsets": "1213,142313,166092,166303", "endLines": "35,2181,2894,2978", "endColumns": "60,12,24,24", "endOffsets": "1269,142453,166298,170814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f84db7003533a22de0405c5251ecb704\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "156,162,168,303,304,305,306,403,2011,2013,2014,2019,2021", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7535,7993,8396,17676,17729,17782,17835,23222,129424,129600,129722,129984,130179", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "7619,8059,8464,17724,17777,17830,17883,23277,129485,129717,129778,130045,130241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\93eeca70efd8419049cd49df8af72af1\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "369,397", "startColumns": "4,4", "startOffsets": "21413,22868", "endColumns": "41,59", "endOffsets": "21450,22923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\006877986797b3b6be5cf579d190afc8\\transformed\\jetified-credentials-1.5.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,211", "endColumns": "73,81,83", "endOffsets": "124,206,290"}, "to": {"startLines": "354,436,437", "startColumns": "4,4,4", "startOffsets": "20669,25469,25551", "endColumns": "73,81,83", "endOffsets": "20738,25546,25630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "61,103,104,122,123,154,155,258,259,260,261,262,263,264,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,361,362,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,408,439,440,441,442,443,444,445,487,2006,2007,2012,2015,2020,2177,2178,2834,2851,3021,3056,3086,3119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2034,3823,3895,5248,5313,7403,7472,14554,14624,14692,14764,14834,14895,14969,16265,16326,16387,16449,16513,16575,16636,16704,16804,16864,16930,17003,17072,17129,17181,18341,18413,18489,18554,18613,18672,18732,18792,18852,18912,18972,19032,19092,19152,19212,19272,19331,19391,19451,19511,19571,19631,19691,19751,19811,19871,19931,19990,20050,20110,20169,20228,20287,20346,20405,21047,21082,21668,21723,21786,21841,21899,21957,22018,22081,22138,22189,22239,22300,22357,22423,22457,22492,23533,25718,25785,25857,25926,25995,26069,26141,30260,129106,129223,129490,129783,130050,142174,142246,163830,164434,172269,174075,175075,175757", "endLines": "61,103,104,122,123,154,155,258,259,260,261,262,263,264,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,361,362,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,408,439,440,441,442,443,444,445,487,2006,2010,2012,2018,2020,2177,2178,2839,2860,3055,3076,3118,3124", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "2089,3890,3978,5308,5374,7467,7530,14619,14687,14759,14829,14890,14964,15037,16321,16382,16444,16508,16570,16631,16699,16799,16859,16925,16998,17067,17124,17176,17238,18408,18484,18549,18608,18667,18727,18787,18847,18907,18967,19027,19087,19147,19207,19267,19326,19386,19446,19506,19566,19626,19686,19746,19806,19866,19926,19985,20045,20105,20164,20223,20282,20341,20400,20459,21077,21112,21718,21781,21836,21894,21952,22013,22076,22133,22184,22234,22295,22352,22418,22452,22487,22522,23598,25780,25852,25921,25990,26064,26136,26224,30326,129218,129419,129595,129979,130174,142241,142308,164028,164730,174070,174751,175752,175919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "399", "startColumns": "4", "startOffsets": "22982", "endColumns": "49", "endOffsets": "23027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "438", "startColumns": "4", "startOffsets": "25635", "endColumns": "82", "endOffsets": "25713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "398", "startColumns": "4", "startOffsets": "22928", "endColumns": "53", "endOffsets": "22977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\185f2479ab24942c0bba65b9ff947d79\\transformed\\jetified-appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2307,2323,2329,3651,3667", "startColumns": "4,4,4,4,4", "startOffsets": "146851,147276,147454,192956,193367", "endLines": "2322,2328,2338,3666,3670", "endColumns": "24,24,24,24,24", "endOffsets": "147271,147449,147733,193362,193489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5dd3740d4798cd744da95fbad85bd5d6\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2062", "startColumns": "4", "startOffsets": "133105", "endLines": "2069", "endColumns": "8", "endOffsets": "133510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "396", "startColumns": "4", "startOffsets": "22825", "endColumns": "42", "endOffsets": "22863"}}]}]}