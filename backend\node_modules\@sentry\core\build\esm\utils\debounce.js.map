{"version": 3, "file": "debounce.js", "sources": ["../../../src/utils/debounce.ts"], "sourcesContent": ["type DebouncedCallback = {\n  (): void | unknown;\n  flush: () => void | unknown;\n  cancel: () => void;\n};\ntype CallbackFunction = () => unknown;\ntype DebounceOptions = {\n  /** The max. time in ms to wait for the callback to be invoked. */\n  maxWait?: number;\n  /** This can be overwritten to use a different setTimeout implementation, e.g. to avoid triggering change detection in Angular  */\n  setTimeoutImpl?: typeof setTimeout;\n};\n\n/**\n * Heavily simplified debounce function based on lodash.debounce.\n *\n * This function takes a callback function (@param fun) and delays its invocation\n * by @param wait milliseconds. Optionally, a maxWait can be specified in @param options,\n * which ensures that the callback is invoked at least once after the specified max. wait time.\n *\n * @param func the function whose invocation is to be debounced\n * @param wait the minimum time until the function is invoked after it was called once\n * @param options the options object, which can contain the `maxWait` property\n *\n * @returns the debounced version of the function, which needs to be called at least once to start the\n *          debouncing process. Subsequent calls will reset the debouncing timer and, in case @paramfunc\n *          was already invoked in the meantime, return @param func's return value.\n *          The debounced function has two additional properties:\n *          - `flush`: Invokes the debounced function immediately and returns its return value\n *          - `cancel`: Cancels the debouncing process and resets the debouncing timer\n */\nexport function debounce(func: CallbackFunction, wait: number, options?: DebounceOptions): DebouncedCallback {\n  let callbackReturnValue: unknown;\n\n  let timerId: ReturnType<typeof setTimeout> | undefined;\n  let maxTimerId: ReturnType<typeof setTimeout> | undefined;\n\n  const maxWait = options?.maxWait ? Math.max(options.maxWait, wait) : 0;\n  const setTimeoutImpl = options?.setTimeoutImpl || setTimeout;\n\n  function invokeFunc(): unknown {\n    cancelTimers();\n    callbackReturnValue = func();\n    return callbackReturnValue;\n  }\n\n  function cancelTimers(): void {\n    timerId !== undefined && clearTimeout(timerId);\n    maxTimerId !== undefined && clearTimeout(maxTimerId);\n    timerId = maxTimerId = undefined;\n  }\n\n  function flush(): unknown {\n    if (timerId !== undefined || maxTimerId !== undefined) {\n      return invokeFunc();\n    }\n    return callbackReturnValue;\n  }\n\n  function debounced(): unknown {\n    if (timerId) {\n      clearTimeout(timerId);\n    }\n    timerId = setTimeoutImpl(invokeFunc, wait);\n\n    if (maxWait && maxTimerId === undefined) {\n      maxTimerId = setTimeoutImpl(invokeFunc, maxWait);\n    }\n\n    return callbackReturnValue;\n  }\n\n  debounced.cancel = cancelTimers;\n  debounced.flush = flush;\n  return debounced;\n}\n"], "names": [], "mappings": "AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAoB,IAAI,EAAU,OAAO,EAAuC;AAC7G,EAAE,IAAI,mBAAmB;;AAEzB,EAAE,IAAI,OAAO;AACb,EAAE,IAAI,UAAU;;AAEhB,EAAE,MAAM,OAAQ,GAAE,OAAO,EAAE,OAAA,GAAU,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAA,GAAI,CAAC;AACxE,EAAE,MAAM,cAAe,GAAE,OAAO,EAAE,cAAA,IAAkB,UAAU;;AAE9D,EAAE,SAAS,UAAU,GAAY;AACjC,IAAI,YAAY,EAAE;AAClB,IAAI,mBAAoB,GAAE,IAAI,EAAE;AAChC,IAAI,OAAO,mBAAmB;AAC9B;;AAEA,EAAE,SAAS,YAAY,GAAS;AAChC,IAAI,YAAY,SAAA,IAAa,YAAY,CAAC,OAAO,CAAC;AAClD,IAAI,eAAe,SAAA,IAAa,YAAY,CAAC,UAAU,CAAC;AACxD,IAAI,OAAQ,GAAE,UAAW,GAAE,SAAS;AACpC;;AAEA,EAAE,SAAS,KAAK,GAAY;AAC5B,IAAI,IAAI,OAAQ,KAAI,aAAa,UAAA,KAAe,SAAS,EAAE;AAC3D,MAAM,OAAO,UAAU,EAAE;AACzB;AACA,IAAI,OAAO,mBAAmB;AAC9B;;AAEA,EAAE,SAAS,SAAS,GAAY;AAChC,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,YAAY,CAAC,OAAO,CAAC;AAC3B;AACA,IAAI,UAAU,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC;;AAE9C,IAAI,IAAI,OAAA,IAAW,UAAW,KAAI,SAAS,EAAE;AAC7C,MAAM,aAAa,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC;AACtD;;AAEA,IAAI,OAAO,mBAAmB;AAC9B;;AAEA,EAAE,SAAS,CAAC,MAAO,GAAE,YAAY;AACjC,EAAE,SAAS,CAAC,KAAM,GAAE,KAAK;AACzB,EAAE,OAAO,SAAS;AAClB;;;;"}