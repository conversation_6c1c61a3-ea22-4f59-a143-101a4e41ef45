{"buildFiles": ["E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\BUDGET TRACKER\\budget_tracker\\build\\.cxx\\Debug\\735r0386\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\BUDGET TRACKER\\budget_tracker\\build\\.cxx\\Debug\\735r0386\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}