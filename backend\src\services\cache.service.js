const redis = require('redis');
const { promisify } = require('util');

class CacheService {
  constructor() {
    this.client = redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    this.getAsync = promisify(this.client.get).bind(this.client);
    this.setAsync = promisify(this.client.set).bind(this.client);
    this.delAsync = promisify(this.client.del).bind(this.client);
    
    this.client.on('error', (err) => {
      console.error('Redis error:', err);
    });
  }
  
  async get(key) {
    const start = Date.now();
    try {
      const value = await this.getAsync(key);
      const duration = Date.now() - start;
      console.log(`Cache ${value ? 'hit' : 'miss'} for key ${key} (${duration}ms)`);
      return value ? JSON.parse(value) : null;
    } catch (err) {
      console.error('Cache get error:', err);
      return null;
    }
  }
  
  async set(key, value, ttl = 3600) {
    try {
      await this.setAsync(key, JSON.stringify(value), 'EX', ttl);
    } catch (err) {
      console.error('Cache set error:', err);
    }
  }
  
  async invalidate(key) {
    try {
      await this.delAsync(key);
    } catch (err) {
      console.error('Cache invalidation error:', err);
    }
  }
}

module.exports = new CacheService();
