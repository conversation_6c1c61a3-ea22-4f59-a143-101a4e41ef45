Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const errors = require('./tracing/errors.js');
const utils = require('./tracing/utils.js');
const idleSpan = require('./tracing/idleSpan.js');
const sentrySpan = require('./tracing/sentrySpan.js');
const sentryNonRecordingSpan = require('./tracing/sentryNonRecordingSpan.js');
const spanstatus = require('./tracing/spanstatus.js');
const trace = require('./tracing/trace.js');
const dynamicSamplingContext = require('./tracing/dynamicSamplingContext.js');
const measurement = require('./tracing/measurement.js');
const sampling = require('./tracing/sampling.js');
const logSpans = require('./tracing/logSpans.js');
const semanticAttributes = require('./semanticAttributes.js');
const envelope = require('./envelope.js');
const exports$1 = require('./exports.js');
const currentScopes = require('./currentScopes.js');
const defaultScopes = require('./defaultScopes.js');
const index = require('./asyncContext/index.js');
const carrier = require('./carrier.js');
const session = require('./session.js');
const scope = require('./scope.js');
const eventProcessors = require('./eventProcessors.js');
const api = require('./api.js');
const client = require('./client.js');
const serverRuntimeClient = require('./server-runtime-client.js');
const sdk = require('./sdk.js');
const base = require('./transports/base.js');
const offline = require('./transports/offline.js');
const multiplexed = require('./transports/multiplexed.js');
const integration = require('./integration.js');
const applyScopeDataToEvent = require('./utils/applyScopeDataToEvent.js');
const prepareEvent = require('./utils/prepareEvent.js');
const checkin = require('./checkin.js');
const hasSpansEnabled = require('./utils/hasSpansEnabled.js');
const isSentryRequestUrl = require('./utils/isSentryRequestUrl.js');
const handleCallbackErrors = require('./utils/handleCallbackErrors.js');
const parameterize = require('./utils/parameterize.js');
const ipAddress = require('./utils/ipAddress.js');
const spanUtils = require('./utils/spanUtils.js');
const parseSampleRate = require('./utils/parseSampleRate.js');
const sdkMetadata = require('./utils/sdkMetadata.js');
const traceData = require('./utils/traceData.js');
const meta = require('./utils/meta.js');
const debounce = require('./utils/debounce.js');
const request = require('./utils/request.js');
const constants = require('./constants.js');
const breadcrumbs = require('./breadcrumbs.js');
const functiontostring = require('./integrations/functiontostring.js');
const eventFilters = require('./integrations/eventFilters.js');
const linkederrors = require('./integrations/linkederrors.js');
const metadata = require('./integrations/metadata.js');
const requestdata = require('./integrations/requestdata.js');
const captureconsole = require('./integrations/captureconsole.js');
const dedupe = require('./integrations/dedupe.js');
const extraerrordata = require('./integrations/extraerrordata.js');
const rewriteframes = require('./integrations/rewriteframes.js');
const supabase = require('./integrations/supabase.js');
const zoderrors = require('./integrations/zoderrors.js');
const thirdPartyErrorsFilter = require('./integrations/third-party-errors-filter.js');
const console = require('./integrations/console.js');
const featureFlagsIntegration = require('./integrations/featureFlags/featureFlagsIntegration.js');
const profiling = require('./profiling.js');
const fetch = require('./fetch.js');
const trpc = require('./trpc.js');
const mcpServer = require('./mcp-server.js');
const feedback = require('./feedback.js');
const exports$2 = require('./logs/exports.js');
const consoleIntegration = require('./logs/console-integration.js');
const vercelAi = require('./utils/vercel-ai.js');
const featureFlags = require('./utils/featureFlags.js');
const aggregateErrors = require('./utils/aggregate-errors.js');
const breadcrumbLogLevel = require('./utils/breadcrumb-log-level.js');
const browser = require('./utils/browser.js');
const dsn = require('./utils/dsn.js');
const error = require('./utils/error.js');
const worldwide = require('./utils/worldwide.js');
const console$1 = require('./instrument/console.js');
const fetch$1 = require('./instrument/fetch.js');
const globalError = require('./instrument/globalError.js');
const globalUnhandledRejection = require('./instrument/globalUnhandledRejection.js');
const handlers = require('./instrument/handlers.js');
const is = require('./utils/is.js');
const isBrowser = require('./utils/isBrowser.js');
const logger = require('./utils/logger.js');
const misc = require('./utils/misc.js');
const node = require('./utils/node.js');
const normalize = require('./utils/normalize.js');
const object = require('./utils/object.js');
const path = require('./utils/path.js');
const promisebuffer = require('./utils/promisebuffer.js');
const severity = require('./utils/severity.js');
const stacktrace = require('./utils/stacktrace.js');
const nodeStackTrace = require('./utils/node-stack-trace.js');
const string = require('./utils/string.js');
const supports = require('./utils/supports.js');
const syncpromise = require('./utils/syncpromise.js');
const time = require('./utils/time.js');
const tracing = require('./utils/tracing.js');
const env = require('./utils/env.js');
const envelope$1 = require('./utils/envelope.js');
const clientreport = require('./utils/clientreport.js');
const ratelimit = require('./utils/ratelimit.js');
const baggage = require('./utils/baggage.js');
const url = require('./utils/url.js');
const eventbuilder = require('./utils/eventbuilder.js');
const anr = require('./utils/anr.js');
const lru = require('./utils/lru.js');
const propagationContext = require('./utils/propagationContext.js');
const vercelWaitUntil = require('./utils/vercelWaitUntil.js');
const version = require('./utils/version.js');
const debugIds = require('./utils/debug-ids.js');
const escapeStringForRegex = require('./vendor/escapeStringForRegex.js');



exports.registerSpanErrorInstrumentation = errors.registerSpanErrorInstrumentation;
exports.getCapturedScopesOnSpan = utils.getCapturedScopesOnSpan;
exports.setCapturedScopesOnSpan = utils.setCapturedScopesOnSpan;
exports.TRACING_DEFAULTS = idleSpan.TRACING_DEFAULTS;
exports.startIdleSpan = idleSpan.startIdleSpan;
exports.SentrySpan = sentrySpan.SentrySpan;
exports.SentryNonRecordingSpan = sentryNonRecordingSpan.SentryNonRecordingSpan;
exports.SPAN_STATUS_ERROR = spanstatus.SPAN_STATUS_ERROR;
exports.SPAN_STATUS_OK = spanstatus.SPAN_STATUS_OK;
exports.SPAN_STATUS_UNSET = spanstatus.SPAN_STATUS_UNSET;
exports.getSpanStatusFromHttpCode = spanstatus.getSpanStatusFromHttpCode;
exports.setHttpStatus = spanstatus.setHttpStatus;
exports.continueTrace = trace.continueTrace;
exports.startInactiveSpan = trace.startInactiveSpan;
exports.startNewTrace = trace.startNewTrace;
exports.startSpan = trace.startSpan;
exports.startSpanManual = trace.startSpanManual;
exports.suppressTracing = trace.suppressTracing;
exports.withActiveSpan = trace.withActiveSpan;
exports.getDynamicSamplingContextFromClient = dynamicSamplingContext.getDynamicSamplingContextFromClient;
exports.getDynamicSamplingContextFromScope = dynamicSamplingContext.getDynamicSamplingContextFromScope;
exports.getDynamicSamplingContextFromSpan = dynamicSamplingContext.getDynamicSamplingContextFromSpan;
exports.spanToBaggageHeader = dynamicSamplingContext.spanToBaggageHeader;
exports.setMeasurement = measurement.setMeasurement;
exports.timedEventsToMeasurements = measurement.timedEventsToMeasurements;
exports.sampleSpan = sampling.sampleSpan;
exports.logSpanEnd = logSpans.logSpanEnd;
exports.logSpanStart = logSpans.logSpanStart;
exports.SEMANTIC_ATTRIBUTE_CACHE_HIT = semanticAttributes.SEMANTIC_ATTRIBUTE_CACHE_HIT;
exports.SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE = semanticAttributes.SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE;
exports.SEMANTIC_ATTRIBUTE_CACHE_KEY = semanticAttributes.SEMANTIC_ATTRIBUTE_CACHE_KEY;
exports.SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME = semanticAttributes.SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME;
exports.SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD = semanticAttributes.SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD;
exports.SEMANTIC_ATTRIBUTE_PROFILE_ID = semanticAttributes.SEMANTIC_ATTRIBUTE_PROFILE_ID;
exports.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME;
exports.SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON;
exports.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT;
exports.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE;
exports.SEMANTIC_ATTRIBUTE_SENTRY_OP = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_OP;
exports.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;
exports.SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = semanticAttributes.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;
exports.SEMANTIC_ATTRIBUTE_URL_FULL = semanticAttributes.SEMANTIC_ATTRIBUTE_URL_FULL;
exports.SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE = semanticAttributes.SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE;
exports.createEventEnvelope = envelope.createEventEnvelope;
exports.createSessionEnvelope = envelope.createSessionEnvelope;
exports.createSpanEnvelope = envelope.createSpanEnvelope;
exports.addEventProcessor = exports$1.addEventProcessor;
exports.captureCheckIn = exports$1.captureCheckIn;
exports.captureEvent = exports$1.captureEvent;
exports.captureException = exports$1.captureException;
exports.captureMessage = exports$1.captureMessage;
exports.captureSession = exports$1.captureSession;
exports.close = exports$1.close;
exports.endSession = exports$1.endSession;
exports.flush = exports$1.flush;
exports.isEnabled = exports$1.isEnabled;
exports.isInitialized = exports$1.isInitialized;
exports.lastEventId = exports$1.lastEventId;
exports.setContext = exports$1.setContext;
exports.setExtra = exports$1.setExtra;
exports.setExtras = exports$1.setExtras;
exports.setTag = exports$1.setTag;
exports.setTags = exports$1.setTags;
exports.setUser = exports$1.setUser;
exports.startSession = exports$1.startSession;
exports.withMonitor = exports$1.withMonitor;
exports.getClient = currentScopes.getClient;
exports.getCurrentScope = currentScopes.getCurrentScope;
exports.getGlobalScope = currentScopes.getGlobalScope;
exports.getIsolationScope = currentScopes.getIsolationScope;
exports.getTraceContextFromScope = currentScopes.getTraceContextFromScope;
exports.withIsolationScope = currentScopes.withIsolationScope;
exports.withScope = currentScopes.withScope;
exports.getDefaultCurrentScope = defaultScopes.getDefaultCurrentScope;
exports.getDefaultIsolationScope = defaultScopes.getDefaultIsolationScope;
exports.setAsyncContextStrategy = index.setAsyncContextStrategy;
exports.getGlobalSingleton = carrier.getGlobalSingleton;
exports.getMainCarrier = carrier.getMainCarrier;
exports.closeSession = session.closeSession;
exports.makeSession = session.makeSession;
exports.updateSession = session.updateSession;
exports.Scope = scope.Scope;
exports.notifyEventProcessors = eventProcessors.notifyEventProcessors;
exports.getEnvelopeEndpointWithUrlEncodedAuth = api.getEnvelopeEndpointWithUrlEncodedAuth;
exports.getReportDialogEndpoint = api.getReportDialogEndpoint;
exports.BaseClient = client.BaseClient;
exports.Client = client.Client;
exports.ServerRuntimeClient = serverRuntimeClient.ServerRuntimeClient;
exports.initAndBind = sdk.initAndBind;
exports.setCurrentClient = sdk.setCurrentClient;
exports.createTransport = base.createTransport;
exports.makeOfflineTransport = offline.makeOfflineTransport;
exports.makeMultiplexedTransport = multiplexed.makeMultiplexedTransport;
exports.addIntegration = integration.addIntegration;
exports.defineIntegration = integration.defineIntegration;
exports.getIntegrationsToSetup = integration.getIntegrationsToSetup;
exports.applyScopeDataToEvent = applyScopeDataToEvent.applyScopeDataToEvent;
exports.mergeScopeData = applyScopeDataToEvent.mergeScopeData;
exports.prepareEvent = prepareEvent.prepareEvent;
exports.createCheckInEnvelope = checkin.createCheckInEnvelope;
exports.hasSpansEnabled = hasSpansEnabled.hasSpansEnabled;
exports.hasTracingEnabled = hasSpansEnabled.hasTracingEnabled;
exports.isSentryRequestUrl = isSentryRequestUrl.isSentryRequestUrl;
exports.handleCallbackErrors = handleCallbackErrors.handleCallbackErrors;
exports.fmt = parameterize.fmt;
exports.parameterize = parameterize.parameterize;
exports.addAutoIpAddressToSession = ipAddress.addAutoIpAddressToSession;
exports.addAutoIpAddressToUser = ipAddress.addAutoIpAddressToUser;
exports.addChildSpanToSpan = spanUtils.addChildSpanToSpan;
exports.convertSpanLinksForEnvelope = spanUtils.convertSpanLinksForEnvelope;
exports.getActiveSpan = spanUtils.getActiveSpan;
exports.getRootSpan = spanUtils.getRootSpan;
exports.getSpanDescendants = spanUtils.getSpanDescendants;
exports.getStatusMessage = spanUtils.getStatusMessage;
exports.spanIsSampled = spanUtils.spanIsSampled;
exports.spanTimeInputToSeconds = spanUtils.spanTimeInputToSeconds;
exports.spanToJSON = spanUtils.spanToJSON;
exports.spanToTraceContext = spanUtils.spanToTraceContext;
exports.spanToTraceHeader = spanUtils.spanToTraceHeader;
exports.updateSpanName = spanUtils.updateSpanName;
exports.parseSampleRate = parseSampleRate.parseSampleRate;
exports.applySdkMetadata = sdkMetadata.applySdkMetadata;
exports.getTraceData = traceData.getTraceData;
exports.getTraceMetaTags = meta.getTraceMetaTags;
exports.debounce = debounce.debounce;
exports.extractQueryParamsFromUrl = request.extractQueryParamsFromUrl;
exports.headersToDict = request.headersToDict;
exports.httpRequestToRequestData = request.httpRequestToRequestData;
exports.winterCGHeadersToDict = request.winterCGHeadersToDict;
exports.winterCGRequestToRequestData = request.winterCGRequestToRequestData;
exports.DEFAULT_ENVIRONMENT = constants.DEFAULT_ENVIRONMENT;
exports.addBreadcrumb = breadcrumbs.addBreadcrumb;
exports.functionToStringIntegration = functiontostring.functionToStringIntegration;
exports.eventFiltersIntegration = eventFilters.eventFiltersIntegration;
exports.inboundFiltersIntegration = eventFilters.inboundFiltersIntegration;
exports.linkedErrorsIntegration = linkederrors.linkedErrorsIntegration;
exports.moduleMetadataIntegration = metadata.moduleMetadataIntegration;
exports.requestDataIntegration = requestdata.requestDataIntegration;
exports.captureConsoleIntegration = captureconsole.captureConsoleIntegration;
exports.dedupeIntegration = dedupe.dedupeIntegration;
exports.extraErrorDataIntegration = extraerrordata.extraErrorDataIntegration;
exports.rewriteFramesIntegration = rewriteframes.rewriteFramesIntegration;
exports.instrumentSupabaseClient = supabase.instrumentSupabaseClient;
exports.supabaseIntegration = supabase.supabaseIntegration;
exports.zodErrorsIntegration = zoderrors.zodErrorsIntegration;
exports.thirdPartyErrorFilterIntegration = thirdPartyErrorsFilter.thirdPartyErrorFilterIntegration;
exports.consoleIntegration = console.consoleIntegration;
exports.featureFlagsIntegration = featureFlagsIntegration.featureFlagsIntegration;
exports.profiler = profiling.profiler;
exports.instrumentFetchRequest = fetch.instrumentFetchRequest;
exports.trpcMiddleware = trpc.trpcMiddleware;
exports.wrapMcpServerWithSentry = mcpServer.wrapMcpServerWithSentry;
exports.captureFeedback = feedback.captureFeedback;
exports._INTERNAL_captureLog = exports$2._INTERNAL_captureLog;
exports._INTERNAL_captureSerializedLog = exports$2._INTERNAL_captureSerializedLog;
exports._INTERNAL_flushLogsBuffer = exports$2._INTERNAL_flushLogsBuffer;
exports.consoleLoggingIntegration = consoleIntegration.consoleLoggingIntegration;
exports.addVercelAiProcessors = vercelAi.addVercelAiProcessors;
exports._INTERNAL_FLAG_BUFFER_SIZE = featureFlags._INTERNAL_FLAG_BUFFER_SIZE;
exports._INTERNAL_MAX_FLAGS_PER_SPAN = featureFlags._INTERNAL_MAX_FLAGS_PER_SPAN;
exports._INTERNAL_addFeatureFlagToActiveSpan = featureFlags._INTERNAL_addFeatureFlagToActiveSpan;
exports._INTERNAL_copyFlagsFromScopeToEvent = featureFlags._INTERNAL_copyFlagsFromScopeToEvent;
exports._INTERNAL_insertFlagToScope = featureFlags._INTERNAL_insertFlagToScope;
exports.applyAggregateErrorsToEvent = aggregateErrors.applyAggregateErrorsToEvent;
exports.getBreadcrumbLogLevelFromHttpStatusCode = breadcrumbLogLevel.getBreadcrumbLogLevelFromHttpStatusCode;
exports.getComponentName = browser.getComponentName;
exports.getLocationHref = browser.getLocationHref;
exports.htmlTreeAsString = browser.htmlTreeAsString;
exports.dsnFromString = dsn.dsnFromString;
exports.dsnToString = dsn.dsnToString;
exports.makeDsn = dsn.makeDsn;
exports.SentryError = error.SentryError;
exports.GLOBAL_OBJ = worldwide.GLOBAL_OBJ;
exports.addConsoleInstrumentationHandler = console$1.addConsoleInstrumentationHandler;
exports.addFetchEndInstrumentationHandler = fetch$1.addFetchEndInstrumentationHandler;
exports.addFetchInstrumentationHandler = fetch$1.addFetchInstrumentationHandler;
exports.addGlobalErrorInstrumentationHandler = globalError.addGlobalErrorInstrumentationHandler;
exports.addGlobalUnhandledRejectionInstrumentationHandler = globalUnhandledRejection.addGlobalUnhandledRejectionInstrumentationHandler;
exports.addHandler = handlers.addHandler;
exports.maybeInstrument = handlers.maybeInstrument;
exports.resetInstrumentationHandlers = handlers.resetInstrumentationHandlers;
exports.triggerHandlers = handlers.triggerHandlers;
exports.isDOMError = is.isDOMError;
exports.isDOMException = is.isDOMException;
exports.isElement = is.isElement;
exports.isError = is.isError;
exports.isErrorEvent = is.isErrorEvent;
exports.isEvent = is.isEvent;
exports.isInstanceOf = is.isInstanceOf;
exports.isParameterizedString = is.isParameterizedString;
exports.isPlainObject = is.isPlainObject;
exports.isPrimitive = is.isPrimitive;
exports.isRegExp = is.isRegExp;
exports.isString = is.isString;
exports.isSyntheticEvent = is.isSyntheticEvent;
exports.isThenable = is.isThenable;
exports.isVueViewModel = is.isVueViewModel;
exports.isBrowser = isBrowser.isBrowser;
exports.CONSOLE_LEVELS = logger.CONSOLE_LEVELS;
exports.consoleSandbox = logger.consoleSandbox;
exports.logger = logger.logger;
exports.originalConsoleMethods = logger.originalConsoleMethods;
exports.addContextToFrame = misc.addContextToFrame;
exports.addExceptionMechanism = misc.addExceptionMechanism;
exports.addExceptionTypeValue = misc.addExceptionTypeValue;
exports.checkOrSetAlreadyCaught = misc.checkOrSetAlreadyCaught;
exports.getEventDescription = misc.getEventDescription;
exports.parseSemver = misc.parseSemver;
exports.uuid4 = misc.uuid4;
exports.isNodeEnv = node.isNodeEnv;
exports.loadModule = node.loadModule;
exports.normalize = normalize.normalize;
exports.normalizeToSize = normalize.normalizeToSize;
exports.normalizeUrlToBase = normalize.normalizeUrlToBase;
exports.addNonEnumerableProperty = object.addNonEnumerableProperty;
exports.convertToPlainObject = object.convertToPlainObject;
exports.dropUndefinedKeys = object.dropUndefinedKeys;
exports.extractExceptionKeysForMessage = object.extractExceptionKeysForMessage;
exports.fill = object.fill;
exports.getOriginalFunction = object.getOriginalFunction;
exports.markFunctionWrapped = object.markFunctionWrapped;
exports.objectify = object.objectify;
exports.basename = path.basename;
exports.dirname = path.dirname;
exports.isAbsolute = path.isAbsolute;
exports.join = path.join;
exports.normalizePath = path.normalizePath;
exports.relative = path.relative;
exports.resolve = path.resolve;
exports.SENTRY_BUFFER_FULL_ERROR = promisebuffer.SENTRY_BUFFER_FULL_ERROR;
exports.makePromiseBuffer = promisebuffer.makePromiseBuffer;
exports.severityLevelFromString = severity.severityLevelFromString;
exports.UNKNOWN_FUNCTION = stacktrace.UNKNOWN_FUNCTION;
exports.createStackParser = stacktrace.createStackParser;
exports.getFramesFromEvent = stacktrace.getFramesFromEvent;
exports.getFunctionName = stacktrace.getFunctionName;
exports.stackParserFromStackParserOptions = stacktrace.stackParserFromStackParserOptions;
exports.stripSentryFramesAndReverse = stacktrace.stripSentryFramesAndReverse;
exports.filenameIsInApp = nodeStackTrace.filenameIsInApp;
exports.node = nodeStackTrace.node;
exports.nodeStackLineParser = nodeStackTrace.nodeStackLineParser;
exports.isMatchingPattern = string.isMatchingPattern;
exports.safeJoin = string.safeJoin;
exports.snipLine = string.snipLine;
exports.stringMatchesSomePattern = string.stringMatchesSomePattern;
exports.truncate = string.truncate;
exports.isNativeFunction = supports.isNativeFunction;
exports.supportsDOMError = supports.supportsDOMError;
exports.supportsDOMException = supports.supportsDOMException;
exports.supportsErrorEvent = supports.supportsErrorEvent;
exports.supportsFetch = supports.supportsFetch;
exports.supportsHistory = supports.supportsHistory;
exports.supportsNativeFetch = supports.supportsNativeFetch;
exports.supportsReferrerPolicy = supports.supportsReferrerPolicy;
exports.supportsReportingObserver = supports.supportsReportingObserver;
exports.SyncPromise = syncpromise.SyncPromise;
exports.rejectedSyncPromise = syncpromise.rejectedSyncPromise;
exports.resolvedSyncPromise = syncpromise.resolvedSyncPromise;
exports.browserPerformanceTimeOrigin = time.browserPerformanceTimeOrigin;
exports.dateTimestampInSeconds = time.dateTimestampInSeconds;
exports.timestampInSeconds = time.timestampInSeconds;
exports.TRACEPARENT_REGEXP = tracing.TRACEPARENT_REGEXP;
exports.extractTraceparentData = tracing.extractTraceparentData;
exports.generateSentryTraceHeader = tracing.generateSentryTraceHeader;
exports.propagationContextFromHeaders = tracing.propagationContextFromHeaders;
exports.getSDKSource = env.getSDKSource;
exports.isBrowserBundle = env.isBrowserBundle;
exports.addItemToEnvelope = envelope$1.addItemToEnvelope;
exports.createAttachmentEnvelopeItem = envelope$1.createAttachmentEnvelopeItem;
exports.createEnvelope = envelope$1.createEnvelope;
exports.createEventEnvelopeHeaders = envelope$1.createEventEnvelopeHeaders;
exports.createSpanEnvelopeItem = envelope$1.createSpanEnvelopeItem;
exports.envelopeContainsItemType = envelope$1.envelopeContainsItemType;
exports.envelopeItemTypeToDataCategory = envelope$1.envelopeItemTypeToDataCategory;
exports.forEachEnvelopeItem = envelope$1.forEachEnvelopeItem;
exports.getSdkMetadataForEnvelopeHeader = envelope$1.getSdkMetadataForEnvelopeHeader;
exports.parseEnvelope = envelope$1.parseEnvelope;
exports.serializeEnvelope = envelope$1.serializeEnvelope;
exports.createClientReportEnvelope = clientreport.createClientReportEnvelope;
exports.DEFAULT_RETRY_AFTER = ratelimit.DEFAULT_RETRY_AFTER;
exports.disabledUntil = ratelimit.disabledUntil;
exports.isRateLimited = ratelimit.isRateLimited;
exports.parseRetryAfterHeader = ratelimit.parseRetryAfterHeader;
exports.updateRateLimits = ratelimit.updateRateLimits;
exports.MAX_BAGGAGE_STRING_LENGTH = baggage.MAX_BAGGAGE_STRING_LENGTH;
exports.SENTRY_BAGGAGE_KEY_PREFIX = baggage.SENTRY_BAGGAGE_KEY_PREFIX;
exports.SENTRY_BAGGAGE_KEY_PREFIX_REGEX = baggage.SENTRY_BAGGAGE_KEY_PREFIX_REGEX;
exports.baggageHeaderToDynamicSamplingContext = baggage.baggageHeaderToDynamicSamplingContext;
exports.dynamicSamplingContextToSentryBaggageHeader = baggage.dynamicSamplingContextToSentryBaggageHeader;
exports.objectToBaggageHeader = baggage.objectToBaggageHeader;
exports.parseBaggageHeader = baggage.parseBaggageHeader;
exports.getHttpSpanDetailsFromUrlObject = url.getHttpSpanDetailsFromUrlObject;
exports.getSanitizedUrlString = url.getSanitizedUrlString;
exports.getSanitizedUrlStringFromUrlObject = url.getSanitizedUrlStringFromUrlObject;
exports.isURLObjectRelative = url.isURLObjectRelative;
exports.parseStringToURLObject = url.parseStringToURLObject;
exports.parseUrl = url.parseUrl;
exports.stripUrlQueryAndFragment = url.stripUrlQueryAndFragment;
exports.eventFromMessage = eventbuilder.eventFromMessage;
exports.eventFromUnknownInput = eventbuilder.eventFromUnknownInput;
exports.exceptionFromError = eventbuilder.exceptionFromError;
exports.parseStackFrames = eventbuilder.parseStackFrames;
exports.callFrameToStackFrame = anr.callFrameToStackFrame;
exports.watchdogTimer = anr.watchdogTimer;
exports.LRUMap = lru.LRUMap;
exports.generateSpanId = propagationContext.generateSpanId;
exports.generateTraceId = propagationContext.generateTraceId;
exports.vercelWaitUntil = vercelWaitUntil.vercelWaitUntil;
exports.SDK_VERSION = version.SDK_VERSION;
exports.getDebugImagesForResources = debugIds.getDebugImagesForResources;
exports.getFilenameToDebugIdMap = debugIds.getFilenameToDebugIdMap;
exports.escapeStringForRegex = escapeStringForRegex.escapeStringForRegex;
//# sourceMappingURL=index.js.map
