{"version": 3, "file": "globalUnhandledRejection.js", "sources": ["../../../src/instrument/globalUnhandledRejection.ts"], "sourcesContent": ["import type { HandlerDataUnhandledRejection } from '../types-hoist/instrument';\nimport { GLOBAL_OBJ } from '../utils/worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './handlers';\n\nlet _oldOnUnhandledRejectionHandler: (typeof GLOBAL_OBJ)['onunhandledrejection'] | null = null;\n\n/**\n * Add an instrumentation handler for when an unhandled promise rejection is captured.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addGlobalUnhandledRejectionInstrumentationHandler(\n  handler: (data: HandlerDataUnhandledRejection) => void,\n): void {\n  const type = 'unhandledrejection';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentUnhandledRejection);\n}\n\nfunction instrumentUnhandledRejection(): void {\n  _oldOnUnhandledRejectionHandler = GLOBAL_OBJ.onunhandledrejection;\n\n  // Note: The reason we are doing window.onunhandledrejection instead of window.addEventListener('unhandledrejection')\n  // is that we are using this handler in the Loader Script, to handle buffered rejections consistently\n  GLOBAL_OBJ.onunhandledrejection = function (e: unknown): boolean {\n    const handlerData: HandlerDataUnhandledRejection = e;\n    triggerHandlers('unhandledrejection', handlerData);\n\n    if (_oldOnUnhandledRejectionHandler) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnUnhandledRejectionHandler.apply(this, arguments);\n    }\n\n    return true;\n  };\n\n  GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__ = true;\n}\n"], "names": [], "mappings": ";;;AAIA,IAAI,+BAA+B,GAAuD,IAAI;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iDAAiD;AACjE,EAAE,OAAO;AACT,EAAQ;AACR,EAAE,MAAM,IAAK,GAAE,oBAAoB;AACnC,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;AAC3B,EAAE,eAAe,CAAC,IAAI,EAAE,4BAA4B,CAAC;AACrD;;AAEA,SAAS,4BAA4B,GAAS;AAC9C,EAAE,+BAAgC,GAAE,UAAU,CAAC,oBAAoB;;AAEnE;AACA;AACA,EAAE,UAAU,CAAC,oBAAqB,GAAE,UAAU,CAAC,EAAoB;AACnE,IAAI,MAAM,WAAW,GAAkC,CAAC;AACxD,IAAI,eAAe,CAAC,oBAAoB,EAAE,WAAW,CAAC;;AAEtD,IAAI,IAAI,+BAA+B,EAAE;AACzC;AACA,MAAM,OAAO,+BAA+B,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AACnE;;AAEA,IAAI,OAAO,IAAI;AACf,GAAG;;AAEH,EAAE,UAAU,CAAC,oBAAoB,CAAC,uBAAA,GAA0B,IAAI;AAChE;;;;"}