const Category = require('../models/category.model');
const Transaction = require('../models/transaction.model');

// @desc    Create a new category
// @route   POST /api/categories
// @access  Private
exports.createCategory = async (req, res) => {
  try {
    const { name, type, color, icon } = req.body;

    // Check if category with the same name already exists for this user
    const existingCategory = await Category.findOne({
      user: req.user.id,
      name: { $regex: new RegExp(`^${name}$`, 'i') }, // Case-insensitive check
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Category with this name already exists',
      });
    }

    // Create category
    const category = await Category.create({
      name,
      type,
      color: color || '#000000',
      icon: icon || 'default',
      user: req.user.id,
    });

    res.status(201).json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('Error in createCategory:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get all categories for a user
// @route   GET /api/categories
// @access  Private
exports.getCategories = async (req, res) => {
  try {
    const { type } = req.query;

    // Build query
    const query = { user: req.user.id };

    // Add type filter if provided
    if (type) query.type = type;

    // Execute query
    const categories = await Category.find(query).sort({ name: 1 });

    res.status(200).json({
      success: true,
      count: categories.length,
      data: categories,
    });
  } catch (error) {
    console.error('Error in getCategories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get a single category
// @route   GET /api/categories/:id
// @access  Private
exports.getCategory = async (req, res) => {
  try {
    const category = await Category.findOne({
      _id: req.params.id,
      user: req.user.id,
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found',
      });
    }

    res.status(200).json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('Error in getCategory:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update a category
// @route   PUT /api/categories/:id
// @access  Private
exports.updateCategory = async (req, res) => {
  try {
    // Make sure category exists and belongs to user
    let category = await Category.findOne({
      _id: req.params.id,
      user: req.user.id,
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found',
      });
    }

    // If name is being updated, check if it already exists
    if (req.body.name && req.body.name !== category.name) {
      const existingCategory = await Category.findOne({
        user: req.user.id,
        name: { $regex: new RegExp(`^${req.body.name}$`, 'i') },
        _id: { $ne: req.params.id },
      });

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: 'Category with this name already exists',
        });
      }
    }

    // Update category
    category = await Category.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('Error in updateCategory:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete a category
// @route   DELETE /api/categories/:id
// @access  Private
exports.deleteCategory = async (req, res) => {
  try {
    // Check if category exists and belongs to user
    const category = await Category.findOne({
      _id: req.params.id,
      user: req.user.id,
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found',
      });
    }

    // Check if any transactions use this category
    const transactionCount = await Transaction.countDocuments({
      category: req.params.id,
    });

    if (transactionCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete category because it's used by ${transactionCount} transactions`,
      });
    }

    await category.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Error in deleteCategory:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get default categories
// @route   GET /api/categories/defaults
// @access  Private
exports.getDefaultCategories = async (req, res) => {
  try {
    // Define default expense categories
    const defaultExpenseCategories = [
      { name: 'Food & Dining', type: 'expense', color: '#FF5733', icon: 'restaurant' },
      { name: 'Transportation', type: 'expense', color: '#33A1FF', icon: 'directions_car' },
      { name: 'Shopping', type: 'expense', color: '#FF33A8', icon: 'shopping_bag' },
      { name: 'Entertainment', type: 'expense', color: '#A833FF', icon: 'movie' },
      { name: 'Bills & Utilities', type: 'expense', color: '#33FF57', icon: 'receipt' },
      { name: 'Housing', type: 'expense', color: '#FF8333', icon: 'home' },
      { name: 'Health', type: 'expense', color: '#3362FF', icon: 'healing' },
      { name: 'Education', type: 'expense', color: '#33FFE0', icon: 'school' },
    ];

    // Define default income categories
    const defaultIncomeCategories = [
      { name: 'Salary', type: 'income', color: '#33FF57', icon: 'work' },
      { name: 'Investments', type: 'income', color: '#C4FF33', icon: 'trending_up' },
      { name: 'Gifts', type: 'income', color: '#FF33A8', icon: 'card_giftcard' },
      { name: 'Other Income', type: 'income', color: '#33A1FF', icon: 'attach_money' },
    ];

    // Combine all default categories
    const defaultCategories = [...defaultExpenseCategories, ...defaultIncomeCategories];

    res.status(200).json({
      success: true,
      count: defaultCategories.length,
      data: defaultCategories,
    });
  } catch (error) {
    console.error('Error in getDefaultCategories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
