{"version": 3, "file": "dsn.js", "sources": ["../../../src/utils/dsn.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport type { DsnCom<PERSON>, DsnLike, DsnProtocol } from '../types-hoist/dsn';\nimport { consoleSandbox, logger } from './logger';\n\n/** Regular expression used to extract org ID from a DSN host. */\nconst ORG_ID_REGEX = /^o(\\d+)\\./;\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+)?)?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\nfunction isValidProtocol(protocol?: string): protocol is DsnProtocol {\n  return protocol === 'http' || protocol === 'https';\n}\n\n/**\n * Renders the string representation of this Dsn.\n *\n * By default, this will render the public representation without the password\n * component. To get the deprecated private representation, set `withPassword`\n * to true.\n *\n * @param withPassword When set to true, the password will be included.\n */\nexport function dsnToString(dsn: DsnComponents, withPassword: boolean = false): string {\n  const { host, path, pass, port, projectId, protocol, publicKey } = dsn;\n  return (\n    `${protocol}://${publicKey}${withPassword && pass ? `:${pass}` : ''}` +\n    `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n  );\n}\n\n/**\n * Parses a Dsn from a given string.\n *\n * @param str A Dsn as string\n * @returns Dsn as DsnComponents or undefined if @param str is not a valid DSN string\n */\nexport function dsnFromString(str: string): DsnComponents | undefined {\n  const match = DSN_REGEX.exec(str);\n\n  if (!match) {\n    // This should be logged to the console\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.error(`Invalid Sentry Dsn: ${str}`);\n    });\n    return undefined;\n  }\n\n  const [protocol, publicKey, pass = '', host = '', port = '', lastPath = ''] = match.slice(1);\n  let path = '';\n  let projectId = lastPath;\n\n  const split = projectId.split('/');\n  if (split.length > 1) {\n    path = split.slice(0, -1).join('/');\n    projectId = split.pop() as string;\n  }\n\n  if (projectId) {\n    const projectMatch = projectId.match(/^\\d+/);\n    if (projectMatch) {\n      projectId = projectMatch[0];\n    }\n  }\n\n  return dsnFromComponents({ host, pass, path, projectId, port, protocol: protocol as DsnProtocol, publicKey });\n}\n\nfunction dsnFromComponents(components: DsnComponents): DsnComponents {\n  return {\n    protocol: components.protocol,\n    publicKey: components.publicKey || '',\n    pass: components.pass || '',\n    host: components.host,\n    port: components.port || '',\n    path: components.path || '',\n    projectId: components.projectId,\n  };\n}\n\nfunction validateDsn(dsn: DsnComponents): boolean {\n  if (!DEBUG_BUILD) {\n    return true;\n  }\n\n  const { port, projectId, protocol } = dsn;\n\n  const requiredComponents: ReadonlyArray<keyof DsnComponents> = ['protocol', 'publicKey', 'host', 'projectId'];\n  const hasMissingRequiredComponent = requiredComponents.find(component => {\n    if (!dsn[component]) {\n      logger.error(`Invalid Sentry Dsn: ${component} missing`);\n      return true;\n    }\n    return false;\n  });\n\n  if (hasMissingRequiredComponent) {\n    return false;\n  }\n\n  if (!projectId.match(/^\\d+$/)) {\n    logger.error(`Invalid Sentry Dsn: Invalid projectId ${projectId}`);\n    return false;\n  }\n\n  if (!isValidProtocol(protocol)) {\n    logger.error(`Invalid Sentry Dsn: Invalid protocol ${protocol}`);\n    return false;\n  }\n\n  if (port && isNaN(parseInt(port, 10))) {\n    logger.error(`Invalid Sentry Dsn: Invalid port ${port}`);\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Extract the org ID from a DSN host.\n *\n * @param host The host from a DSN\n * @returns The org ID if found, undefined otherwise\n */\nexport function extractOrgIdFromDsnHost(host: string): string | undefined {\n  const match = host.match(ORG_ID_REGEX);\n\n  return match?.[1];\n}\n\n/**\n * Creates a valid Sentry Dsn object, identifying a Sentry instance and project.\n * @returns a valid DsnComponents object or `undefined` if @param from is an invalid DSN source\n */\nexport function makeDsn(from: DsnLike): DsnComponents | undefined {\n  const components = typeof from === 'string' ? dsnFromString(from) : dsnFromComponents(from);\n  if (!components || !validateDsn(components)) {\n    return undefined;\n  }\n  return components;\n}\n"], "names": [], "mappings": ";;;AAIA;AACA,MAAM,YAAA,GAAe,WAAW;;AAEhC;AACA,MAAM,SAAA,GAAY,iEAAiE;;AAEnF,SAAS,eAAe,CAAC,QAAQ,EAAoC;AACrE,EAAE,OAAO,QAAS,KAAI,UAAU,QAAA,KAAa,OAAO;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,GAAG,EAAiB,YAAY,GAAY,KAAK,EAAU;AACvF,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAU,EAAA,GAAI,GAAG;AACxE,EAAE;AACF,IAAI,CAAC,EAAA,QAAA,CAAA,GAAA,EAAA,SAAA,CAAA,EAAA,YAAA,IAAA,IAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AACA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,EAAA,SAAA,CAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,aAAA,CAAA,GAAA,EAAA;AACA,EAAA,MAAA,KAAA,GAAA,SAAA,CAAA,IAAA,CAAA,GAAA,CAAA;;AAEA,EAAA,IAAA,CAAA,KAAA,EAAA;AACA;AACA,IAAA,cAAA,CAAA,MAAA;AACA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA;AACA,IAAA,OAAA,SAAA;AACA;;AAEA,EAAA,MAAA,CAAA,QAAA,EAAA,SAAA,EAAA,IAAA,GAAA,EAAA,EAAA,IAAA,GAAA,EAAA,EAAA,IAAA,GAAA,EAAA,EAAA,QAAA,GAAA,EAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,EAAA,IAAA,IAAA,GAAA,EAAA;AACA,EAAA,IAAA,SAAA,GAAA,QAAA;;AAEA,EAAA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA,EAAA,IAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,IAAA,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,IAAA,SAAA,GAAA,KAAA,CAAA,GAAA,EAAA;AACA;;AAEA,EAAA,IAAA,SAAA,EAAA;AACA,IAAA,MAAA,YAAA,GAAA,SAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACA,IAAA,IAAA,YAAA,EAAA;AACA,MAAA,SAAA,GAAA,YAAA,CAAA,CAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,iBAAA,CAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,SAAA,EAAA,IAAA,EAAA,QAAA,EAAA,QAAA,GAAA,SAAA,EAAA,CAAA;AACA;;AAEA,SAAA,iBAAA,CAAA,UAAA,EAAA;AACA,EAAA,OAAA;AACA,IAAA,QAAA,EAAA,UAAA,CAAA,QAAA;AACA,IAAA,SAAA,EAAA,UAAA,CAAA,SAAA,IAAA,EAAA;AACA,IAAA,IAAA,EAAA,UAAA,CAAA,IAAA,IAAA,EAAA;AACA,IAAA,IAAA,EAAA,UAAA,CAAA,IAAA;AACA,IAAA,IAAA,EAAA,UAAA,CAAA,IAAA,IAAA,EAAA;AACA,IAAA,IAAA,EAAA,UAAA,CAAA,IAAA,IAAA,EAAA;AACA,IAAA,SAAA,EAAA,UAAA,CAAA,SAAA;AACA,GAAA;AACA;;AAEA,SAAA,WAAA,CAAA,GAAA,EAAA;AACA,EAAA,IAAA,CAAA,WAAA,EAAA;AACA,IAAA,OAAA,IAAA;AACA;;AAEA,EAAA,MAAA,EAAA,IAAA,EAAA,SAAA,EAAA,QAAA,EAAA,GAAA,GAAA;;AAEA,EAAA,MAAA,kBAAA,GAAA,CAAA,UAAA,EAAA,WAAA,EAAA,MAAA,EAAA,WAAA,CAAA;AACA,EAAA,MAAA,2BAAA,GAAA,kBAAA,CAAA,IAAA,CAAA,SAAA,IAAA;AACA,IAAA,IAAA,CAAA,GAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAA,MAAA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,SAAA,CAAA,QAAA,CAAA,CAAA;AACA,MAAA,OAAA,IAAA;AACA;AACA,IAAA,OAAA,KAAA;AACA,GAAA,CAAA;;AAEA,EAAA,IAAA,2BAAA,EAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,sCAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,IAAA,CAAA,eAAA,CAAA,QAAA,CAAA,EAAA;AACA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,qCAAA,EAAA,QAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,IAAA,IAAA,IAAA,KAAA,CAAA,QAAA,CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,EAAA;AACA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,iCAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,KAAA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,uBAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA;;AAEA,EAAA,OAAA,KAAA,GAAA,CAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,OAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,UAAA,GAAA,OAAA,IAAA,KAAA,QAAA,GAAA,aAAA,CAAA,IAAA,CAAA,GAAA,iBAAA,CAAA,IAAA,CAAA;AACA,EAAA,IAAA,CAAA,UAAA,IAAA,CAAA,WAAA,CAAA,UAAA,CAAA,EAAA;AACA,IAAA,OAAA,SAAA;AACA;AACA,EAAA,OAAA,UAAA;AACA;;;;"}