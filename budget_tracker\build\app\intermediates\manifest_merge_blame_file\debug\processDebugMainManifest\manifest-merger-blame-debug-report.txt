1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.budget_tracker"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\BUDGET TRACKER\budget_tracker\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->E:\BUDGET TRACKER\budget_tracker\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:41:13-72
25-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:42:13-50
27-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
31-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="android.permission.VIBRATE" />
32-->[:flutter_local_notifications] E:\BUDGET TRACKER\budget_tracker\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
32-->[:flutter_local_notifications] E:\BUDGET TRACKER\budget_tracker\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
33    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
33-->[:flutter_local_notifications] E:\BUDGET TRACKER\budget_tracker\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
33-->[:flutter_local_notifications] E:\BUDGET TRACKER\budget_tracker\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
34    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
34-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
34-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
35    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
35-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
35-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
36    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
36-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\331c8ce949ed2ed477c31f092838cdcc\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
36-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\331c8ce949ed2ed477c31f092838cdcc\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
37
38    <permission
38-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
39        android:name="com.example.budget_tracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.example.budget_tracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
43
44    <application
45        android:name="android.app.Application"
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:icon="@mipmap/ic_launcher"
50        android:label="budget_tracker" >
51        <activity
52            android:name="com.example.budget_tracker.MainActivity"
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
54            android:exported="true"
55            android:hardwareAccelerated="true"
56            android:launchMode="singleTop"
57            android:taskAffinity=""
58            android:theme="@style/LaunchTheme"
59            android:windowSoftInputMode="adjustResize" >
60
61            <!--
62                 Specifies an Android theme to apply to this Activity as soon as
63                 the Android process has started. This theme is visible to the user
64                 while the Flutter UI initializes. After that, this theme continues
65                 to determine the Window background behind the Flutter UI.
66            -->
67            <meta-data
68                android:name="io.flutter.embedding.android.NormalTheme"
69                android:resource="@style/NormalTheme" />
70
71            <intent-filter>
72                <action android:name="android.intent.action.MAIN" />
73
74                <category android:name="android.intent.category.LAUNCHER" />
75            </intent-filter>
76        </activity>
77        <!--
78             Don't delete the meta-data below.
79             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
80        -->
81        <meta-data
82            android:name="flutterEmbedding"
83            android:value="2" />
84
85        <service
85-->[:firebase_auth] E:\BUDGET TRACKER\budget_tracker\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
86            android:name="com.google.firebase.components.ComponentDiscoveryService"
86-->[:firebase_auth] E:\BUDGET TRACKER\budget_tracker\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
87            android:directBootAware="true"
87-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
88            android:exported="false" >
88-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
89            <meta-data
89-->[:firebase_auth] E:\BUDGET TRACKER\budget_tracker\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
90                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
90-->[:firebase_auth] E:\BUDGET TRACKER\budget_tracker\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
91                android:value="com.google.firebase.components.ComponentRegistrar" />
91-->[:firebase_auth] E:\BUDGET TRACKER\budget_tracker\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
92            <meta-data
92-->[:firebase_core] E:\BUDGET TRACKER\budget_tracker\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
93                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
93-->[:firebase_core] E:\BUDGET TRACKER\budget_tracker\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[:firebase_core] E:\BUDGET TRACKER\budget_tracker\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
95            <meta-data
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
96                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
98            <meta-data
98-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
99                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
99-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a061838a5592a55f9a915626b6a31f6d\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
101            <meta-data
101-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
102                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
102-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
104        </service>
105
106        <activity
106-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
107            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
107-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
108            android:excludeFromRecents="true"
108-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
109            android:exported="true"
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
110            android:launchMode="singleTask"
110-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
111            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
111-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
112            <intent-filter>
112-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
113                <action android:name="android.intent.action.VIEW" />
113-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
113-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
114
115                <category android:name="android.intent.category.DEFAULT" />
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
116                <category android:name="android.intent.category.BROWSABLE" />
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
117
118                <data
118-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:42:13-50
119                    android:host="firebase.auth"
120                    android:path="/"
121                    android:scheme="genericidp" />
122            </intent-filter>
123        </activity>
124        <activity
124-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
125            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
125-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
126            android:excludeFromRecents="true"
126-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
127            android:exported="true"
127-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
128            android:launchMode="singleTask"
128-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
129            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
129-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
130            <intent-filter>
130-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
131                <action android:name="android.intent.action.VIEW" />
131-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
131-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
132
133                <category android:name="android.intent.category.DEFAULT" />
133-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
133-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
134                <category android:name="android.intent.category.BROWSABLE" />
134-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
134-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f3f82d80d77fece28fd45a2f26f93c2\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
135
136                <data
136-->E:\BUDGET TRACKER\budget_tracker\android\app\src\main\AndroidManifest.xml:42:13-50
137                    android:host="firebase.auth"
138                    android:path="/"
139                    android:scheme="recaptcha" />
140            </intent-filter>
141        </activity>
142
143        <service
143-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
144            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
144-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
145            android:enabled="true"
145-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
146            android:exported="false" >
146-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
147            <meta-data
147-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
148                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
148-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
149                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
149-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
150        </service>
151
152        <activity
152-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
153            android:name="androidx.credentials.playservices.HiddenActivity"
153-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
154            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
154-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
155            android:enabled="true"
155-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
156            android:exported="false"
156-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
157            android:fitsSystemWindows="true"
157-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
158            android:theme="@style/Theme.Hidden" >
158-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
159        </activity>
160        <activity
160-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
161            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
161-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
162            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
162-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
163            android:enabled="true"
163-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
164            android:exported="false"
164-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
165            android:fitsSystemWindows="true"
165-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
166            android:theme="@style/Theme.Hidden" >
166-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b94913d5ef63be7d6118067409894863\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
167        </activity>
168
169        <provider
169-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
170            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
170-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
171            android:authorities="com.example.budget_tracker.flutter.image_provider"
171-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
172            android:exported="false"
172-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
173            android:grantUriPermissions="true" >
173-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
174            <meta-data
174-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
175                android:name="android.support.FILE_PROVIDER_PATHS"
175-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
176                android:resource="@xml/flutter_image_picker_file_paths" />
176-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
177        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
178        <service
178-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
179            android:name="com.google.android.gms.metadata.ModuleDependencies"
179-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
180            android:enabled="false"
180-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
181            android:exported="false" >
181-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
182            <intent-filter>
182-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
183                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
183-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
183-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
184            </intent-filter>
185
186            <meta-data
186-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
187                android:name="photopicker_activity:0:required"
187-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
188                android:value="" />
188-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
189        </service>
190
191        <provider
191-->[:printing] E:\BUDGET TRACKER\budget_tracker\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
192            android:name="net.nfet.flutter.printing.PrintFileProvider"
192-->[:printing] E:\BUDGET TRACKER\budget_tracker\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-71
193            android:authorities="com.example.budget_tracker.flutter.printing"
193-->[:printing] E:\BUDGET TRACKER\budget_tracker\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-68
194            android:exported="false"
194-->[:printing] E:\BUDGET TRACKER\budget_tracker\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
195            android:grantUriPermissions="true" >
195-->[:printing] E:\BUDGET TRACKER\budget_tracker\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
196            <meta-data
196-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
197                android:name="android.support.FILE_PROVIDER_PATHS"
197-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
198                android:resource="@xml/flutter_printing_file_paths" />
198-->[:image_picker_android] E:\BUDGET TRACKER\budget_tracker\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
199        </provider>
200
201        <activity
201-->[:url_launcher_android] E:\BUDGET TRACKER\budget_tracker\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
202            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
202-->[:url_launcher_android] E:\BUDGET TRACKER\budget_tracker\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
203            android:exported="false"
203-->[:url_launcher_android] E:\BUDGET TRACKER\budget_tracker\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
204            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
204-->[:url_launcher_android] E:\BUDGET TRACKER\budget_tracker\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
205        <activity
205-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
206            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
206-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
207            android:excludeFromRecents="true"
207-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
208            android:exported="false"
208-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
209            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
209-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
210        <!--
211            Service handling Google Sign-In user revocation. For apps that do not integrate with
212            Google Sign-In, this service will never be started.
213        -->
214        <service
214-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
215            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
215-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
216            android:exported="true"
216-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
217            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
217-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
218            android:visibleToInstantApps="true" />
218-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\34ff107eec8aa5c57045627fde78b309\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
219
220        <activity
220-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
221            android:name="com.google.android.gms.common.api.GoogleApiActivity"
221-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
222            android:exported="false"
222-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
223            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
223-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1f6d2e0b1aa38467964f5b59b4f29f9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
224
225        <provider
225-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
226            android:name="com.google.firebase.provider.FirebaseInitProvider"
226-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
227            android:authorities="com.example.budget_tracker.firebaseinitprovider"
227-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
228            android:directBootAware="true"
228-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
229            android:exported="false"
229-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
230            android:initOrder="100" />
230-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da272753831444f8032e334cfd31d8b\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
231
232        <uses-library
232-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
233            android:name="androidx.window.extensions"
233-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
234            android:required="false" />
234-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
235        <uses-library
235-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
236            android:name="androidx.window.sidecar"
236-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
237            android:required="false" />
237-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
238
239        <provider
239-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
240            android:name="androidx.startup.InitializationProvider"
240-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
241            android:authorities="com.example.budget_tracker.androidx-startup"
241-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
242            android:exported="false" >
242-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
243            <meta-data
243-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
244                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
244-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
245                android:value="androidx.startup" />
245-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
246            <meta-data
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
247                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
248                android:value="androidx.startup" />
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
249        </provider>
250
251        <meta-data
251-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
252            android:name="com.google.android.gms.version"
252-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
253            android:value="@integer/google_play_services_version" />
253-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
254
255        <receiver
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
256            android:name="androidx.profileinstaller.ProfileInstallReceiver"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
257            android:directBootAware="false"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
258            android:enabled="true"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
259            android:exported="true"
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
260            android:permission="android.permission.DUMP" >
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
261            <intent-filter>
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
262                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
263            </intent-filter>
264            <intent-filter>
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
265                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
266            </intent-filter>
267            <intent-filter>
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
268                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
269            </intent-filter>
270            <intent-filter>
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
271                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
272            </intent-filter>
273        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
274        <activity
274-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5dd3740d4798cd744da95fbad85bd5d6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
275            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
275-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5dd3740d4798cd744da95fbad85bd5d6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
276            android:exported="false"
276-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5dd3740d4798cd744da95fbad85bd5d6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
277            android:stateNotNeeded="true"
277-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5dd3740d4798cd744da95fbad85bd5d6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
278            android:theme="@style/Theme.PlayCore.Transparent" />
278-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\5dd3740d4798cd744da95fbad85bd5d6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
279    </application>
280
281</manifest>
