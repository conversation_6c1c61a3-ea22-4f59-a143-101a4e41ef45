const mongoose = require('mongoose');
require('dotenv').config();

async function testConnection() {
  try {
    console.log('Attempting to connect to MongoDB with URI:', process.env.MONGODB_URI);
    
    const connection = await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 10000
    });
    
    console.log('Successfully connected to MongoDB');
    console.log('Connection details:', {
      host: connection.connection.host,
      port: connection.connection.port,
      name: connection.connection.name
    });
    
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    console.error('Full error details:', JSON.stringify(err, null, 2));
    process.exit(1);
  }
}

testConnection();
