{"version": 3, "file": "console.js", "sources": ["../../../src/instrument/console.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\nimport type { ConsoleLevel, HandlerDataConsole } from '../types-hoist/instrument';\nimport { CONSOLE_LEVELS, originalConsoleMethods } from '../utils/logger';\nimport { fill } from '../utils/object';\nimport { GLOBAL_OBJ } from '../utils/worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './handlers';\n\n/**\n * Add an instrumentation handler for when a console.xxx method is called.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addConsoleInstrumentationHandler(handler: (data: HandlerDataConsole) => void): void {\n  const type = 'console';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentConsole);\n}\n\nfunction instrumentConsole(): void {\n  if (!('console' in GLOBAL_OBJ)) {\n    return;\n  }\n\n  CONSOLE_LEVELS.forEach(function (level: ConsoleLevel): void {\n    if (!(level in GLOBAL_OBJ.console)) {\n      return;\n    }\n\n    fill(GLOBAL_OBJ.console, level, function (originalConsoleMethod: () => any): Function {\n      originalConsoleMethods[level] = originalConsoleMethod;\n\n      return function (...args: any[]): void {\n        const handlerData: HandlerDataConsole = { args, level };\n        triggerHandlers('console', handlerData);\n\n        const log = originalConsoleMethods[level];\n        log?.apply(GLOBAL_OBJ.console, args);\n      };\n    });\n  });\n}\n"], "names": ["add<PERSON><PERSON><PERSON>", "maybeInstrument", "GLOBAL_OBJ", "CONSOLE_LEVELS", "fill", "originalConsoleMethods", "triggerHandlers"], "mappings": ";;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gCAAgC,CAAC,OAAO,EAA4C;AACpG,EAAE,MAAM,IAAK,GAAE,SAAS;AACxB,EAAEA,mBAAU,CAAC,IAAI,EAAE,OAAO,CAAC;AAC3B,EAAEC,wBAAe,CAAC,IAAI,EAAE,iBAAiB,CAAC;AAC1C;;AAEA,SAAS,iBAAiB,GAAS;AACnC,EAAE,IAAI,EAAE,aAAaC,oBAAU,CAAC,EAAE;AAClC,IAAI;AACJ;;AAEA,EAAEC,qBAAc,CAAC,OAAO,CAAC,UAAU,KAAK,EAAsB;AAC9D,IAAI,IAAI,EAAE,KAAA,IAASD,oBAAU,CAAC,OAAO,CAAC,EAAE;AACxC,MAAM;AACN;;AAEA,IAAIE,WAAI,CAACF,oBAAU,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,qBAAqB,EAAuB;AAC1F,MAAMG,6BAAsB,CAAC,KAAK,CAAA,GAAI,qBAAqB;;AAE3D,MAAM,OAAO,UAAU,GAAG,IAAI,EAAe;AAC7C,QAAQ,MAAM,WAAW,GAAuB,EAAE,IAAI,EAAE,OAAO;AAC/D,QAAQC,wBAAe,CAAC,SAAS,EAAE,WAAW,CAAC;;AAE/C,QAAQ,MAAM,GAAI,GAAED,6BAAsB,CAAC,KAAK,CAAC;AACjD,QAAQ,GAAG,EAAE,KAAK,CAACH,oBAAU,CAAC,OAAO,EAAE,IAAI,CAAC;AAC5C,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;;;"}