{"version": 3, "file": "sampling.js", "sources": ["../../../src/tracing/sampling.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport type { Options } from '../types-hoist/options';\nimport type { SamplingContext } from '../types-hoist/samplingcontext';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled';\nimport { logger } from '../utils/logger';\nimport { parseSampleRate } from '../utils/parseSampleRate';\n\n/**\n * Makes a sampling decision for the given options.\n *\n * Called every time a root span is created. Only root spans which emerge with a `sampled` value of `true` will be\n * sent to Sentry.\n */\nexport function sampleSpan(\n  options: Pick<Options, 'tracesSampleRate' | 'tracesSampler'>,\n  samplingContext: SamplingContext,\n  sampleRand: number,\n): [sampled: boolean, sampleRate?: number, localSampleRateWasApplied?: boolean] {\n  // nothing to do if span recording is not enabled\n  if (!hasSpansEnabled(options)) {\n    return [false];\n  }\n\n  let localSampleRateWasApplied = undefined;\n\n  // we would have bailed already if neither `tracesSampler` nor `tracesSampleRate` were defined, so one of these should\n  // work; prefer the hook if so\n  let sampleRate;\n  if (typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler({\n      ...samplingContext,\n      inheritOrSampleWith: fallbackSampleRate => {\n        // If we have an incoming parent sample rate, we'll just use that one.\n        // The sampling decision will be inherited because of the sample_rand that was generated when the trace reached the incoming boundaries of the SDK.\n        if (typeof samplingContext.parentSampleRate === 'number') {\n          return samplingContext.parentSampleRate;\n        }\n\n        // Fallback if parent sample rate is not on the incoming trace (e.g. if there is no baggage)\n        // This is to provide backwards compatibility if there are incoming traces from older SDKs that don't send a parent sample rate or a sample rand. In these cases we just want to force either a sampling decision on the downstream traces via the sample rate.\n        if (typeof samplingContext.parentSampled === 'boolean') {\n          return Number(samplingContext.parentSampled);\n        }\n\n        return fallbackSampleRate;\n      },\n    });\n    localSampleRateWasApplied = true;\n  } else if (samplingContext.parentSampled !== undefined) {\n    sampleRate = samplingContext.parentSampled;\n  } else if (typeof options.tracesSampleRate !== 'undefined') {\n    sampleRate = options.tracesSampleRate;\n    localSampleRateWasApplied = true;\n  }\n\n  // Since this is coming from the user (or from a function provided by the user), who knows what we might get.\n  // (The only valid values are booleans or numbers between 0 and 1.)\n  const parsedSampleRate = parseSampleRate(sampleRate);\n\n  if (parsedSampleRate === undefined) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(\n          sampleRate,\n        )} of type ${JSON.stringify(typeof sampleRate)}.`,\n      );\n    return [false];\n  }\n\n  // if the function returned 0 (or false), or if `tracesSampleRate` is 0, it's a sign the transaction should be dropped\n  if (!parsedSampleRate) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because ${\n          typeof options.tracesSampler === 'function'\n            ? 'tracesSampler returned 0 or false'\n            : 'a negative sampling decision was inherited or tracesSampleRate is set to 0'\n        }`,\n      );\n    return [false, parsedSampleRate, localSampleRateWasApplied];\n  }\n\n  // We always compare the sample rand for the current execution context against the chosen sample rate.\n  // Read more: https://develop.sentry.dev/sdk/telemetry/traces/#propagated-random-value\n  const shouldSample = sampleRand < parsedSampleRate;\n\n  // if we're not going to keep it, we're done\n  if (!shouldSample) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(\n          sampleRate,\n        )})`,\n      );\n  }\n\n  return [shouldSample, parsedSampleRate, localSampleRateWasApplied];\n}\n"], "names": ["hasSpansEnabled", "parseSampleRate", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU;AAC1B,EAAE,OAAO;AACT,EAAE,eAAe;AACjB,EAAE,UAAU;AACZ,EAAgF;AAChF;AACA,EAAE,IAAI,CAACA,+BAAe,CAAC,OAAO,CAAC,EAAE;AACjC,IAAI,OAAO,CAAC,KAAK,CAAC;AAClB;;AAEA,EAAE,IAAI,yBAA0B,GAAE,SAAS;;AAE3C;AACA;AACA,EAAE,IAAI,UAAU;AAChB,EAAE,IAAI,OAAO,OAAO,CAAC,aAAc,KAAI,UAAU,EAAE;AACnD,IAAI,UAAW,GAAE,OAAO,CAAC,aAAa,CAAC;AACvC,MAAM,GAAG,eAAe;AACxB,MAAM,mBAAmB,EAAE,kBAAA,IAAsB;AACjD;AACA;AACA,QAAQ,IAAI,OAAO,eAAe,CAAC,gBAAiB,KAAI,QAAQ,EAAE;AAClE,UAAU,OAAO,eAAe,CAAC,gBAAgB;AACjD;;AAEA;AACA;AACA,QAAQ,IAAI,OAAO,eAAe,CAAC,aAAc,KAAI,SAAS,EAAE;AAChE,UAAU,OAAO,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC;AACtD;;AAEA,QAAQ,OAAO,kBAAkB;AACjC,OAAO;AACP,KAAK,CAAC;AACN,IAAI,yBAAA,GAA4B,IAAI;AACpC,GAAE,MAAO,IAAI,eAAe,CAAC,aAAA,KAAkB,SAAS,EAAE;AAC1D,IAAI,UAAW,GAAE,eAAe,CAAC,aAAa;AAC9C,GAAE,MAAO,IAAI,OAAO,OAAO,CAAC,gBAAA,KAAqB,WAAW,EAAE;AAC9D,IAAI,UAAW,GAAE,OAAO,CAAC,gBAAgB;AACzC,IAAI,yBAAA,GAA4B,IAAI;AACpC;;AAEA;AACA;AACA,EAAE,MAAM,gBAAiB,GAAEC,+BAAe,CAAC,UAAU,CAAC;;AAEtD,EAAE,IAAI,gBAAiB,KAAI,SAAS,EAAE;AACtC,IAAIC,sBAAY;AAChB,MAAMC,aAAM,CAAC,IAAI;AACjB,QAAQ,CAAC,8HAA8H,EAAE,IAAI,CAAC,SAAS;AACvJ,UAAU,UAAU;AACpB,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;AACzD,OAAO;AACP,IAAI,OAAO,CAAC,KAAK,CAAC;AAClB;;AAEA;AACA,EAAE,IAAI,CAAC,gBAAgB,EAAE;AACzB,IAAID,sBAAY;AAChB,MAAMC,aAAM,CAAC,GAAG;AAChB,QAAQ,CAAC,yCAAyC;AAClD,UAAU,OAAO,OAAO,CAAC,aAAA,KAAkB;AAC3C,cAAc;AACd,cAAc;AACd,SAAS,CAAA;AACA,OAAA;AACA,IAAA,OAAA,CAAA,KAAA,EAAA,gBAAA,EAAA,yBAAA,CAAA;AACA;;AAEA;AACA;AACA,EAAA,MAAA,YAAA,GAAA,UAAA,GAAA,gBAAA;;AAEA;AACA,EAAA,IAAA,CAAA,YAAA,EAAA;AACA,IAAAD,sBAAA;AACA,MAAAC,aAAA,CAAA,GAAA;AACA,QAAA,CAAA,iGAAA,EAAA,MAAA;AACA,UAAA,UAAA;AACA,SAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA;;AAEA,EAAA,OAAA,CAAA,YAAA,EAAA,gBAAA,EAAA,yBAAA,CAAA;AACA;;;;"}