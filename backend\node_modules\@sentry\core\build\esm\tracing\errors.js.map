{"version": 3, "file": "errors.js", "sources": ["../../../src/tracing/errors.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport { addGlobalErrorInstrumentationHandler } from '../instrument/globalError';\nimport { addGlobalUnhandledRejectionInstrumentationHandler } from '../instrument/globalUnhandledRejection';\nimport { logger } from '../utils/logger';\nimport { getActiveSpan, getRootSpan } from '../utils/spanUtils';\nimport { SPAN_STATUS_ERROR } from './spanstatus';\n\nlet errorsInstrumented = false;\n\n/**  Only exposed for testing */\nexport function _resetErrorsInstrumented(): void {\n  errorsInstrumented = false;\n}\n\n/**\n * Ensure that global errors automatically set the active span status.\n */\nexport function registerSpanErrorInstrumentation(): void {\n  if (errorsInstrumented) {\n    return;\n  }\n\n  errorsInstrumented = true;\n  addGlobalErrorInstrumentationHandler(errorCallback);\n  addGlobalUnhandledRejectionInstrumentationHandler(errorCallback);\n}\n\n/**\n * If an error or unhandled promise occurs, we mark the active root span as failed\n */\nfunction errorCallback(): void {\n  const activeSpan = getActiveSpan();\n  const rootSpan = activeSpan && getRootSpan(activeSpan);\n  if (rootSpan) {\n    const message = 'internal_error';\n    DEBUG_BUILD && logger.log(`[Tracing] Root span: ${message} -> Global error occurred`);\n    rootSpan.setStatus({ code: SPAN_STATUS_ERROR, message });\n  }\n}\n\n// The function name will be lost when bundling but we need to be able to identify this listener later to maintain the\n// node.js default exit behaviour\nerrorCallback.tag = 'sentry_tracingErrorCallback';\n"], "names": [], "mappings": ";;;;;;;AAOA,IAAI,kBAAA,GAAqB,KAAK;;AAO9B;AACA;AACA;AACO,SAAS,gCAAgC,GAAS;AACzD,EAAE,IAAI,kBAAkB,EAAE;AAC1B,IAAI;AACJ;;AAEA,EAAE,kBAAA,GAAqB,IAAI;AAC3B,EAAE,oCAAoC,CAAC,aAAa,CAAC;AACrD,EAAE,iDAAiD,CAAC,aAAa,CAAC;AAClE;;AAEA;AACA;AACA;AACA,SAAS,aAAa,GAAS;AAC/B,EAAE,MAAM,UAAA,GAAa,aAAa,EAAE;AACpC,EAAE,MAAM,WAAW,UAAA,IAAc,WAAW,CAAC,UAAU,CAAC;AACxD,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,MAAM,OAAQ,GAAE,gBAAgB;AACpC,IAAI,WAAY,IAAG,MAAM,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;AACzF,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAQ,EAAC,CAAC;AAC5D;AACA;;AAEA;AACA;AACA,aAAa,CAAC,GAAI,GAAE,6BAA6B;;;;"}