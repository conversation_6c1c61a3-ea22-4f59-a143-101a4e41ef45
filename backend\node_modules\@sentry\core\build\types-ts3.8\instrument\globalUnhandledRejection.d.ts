import { HandlerDataUnhandledRejection } from '../types-hoist/instrument';
/**
 * Add an instrumentation handler for when an unhandled promise rejection is captured.
 *
 * Use at your own risk, this might break without changelog notice, only used internally.
 * @hidden
 */
export declare function addGlobalUnhandledRejectionInstrumentationHandler(handler: (data: HandlerDataUnhandledRejection) => void): void;
//# sourceMappingURL=globalUnhandledRejection.d.ts.map
