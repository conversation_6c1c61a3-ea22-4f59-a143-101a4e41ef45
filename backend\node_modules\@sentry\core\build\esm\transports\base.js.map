{"version": 3, "file": "base.js", "sources": ["../../../src/transports/base.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport type { EventDropReason } from '../types-hoist/clientreport';\nimport type { Envelope, EnvelopeItem } from '../types-hoist/envelope';\nimport type {\n  InternalBaseTransportOptions,\n  Transport,\n  TransportMakeRequestResponse,\n  TransportRequestExecutor,\n} from '../types-hoist/transport';\nimport {\n  createEnvelope,\n  envelopeItemTypeToDataCategory,\n  forEachEnvelopeItem,\n  serializeEnvelope,\n} from '../utils/envelope';\nimport { logger } from '../utils/logger';\nimport { type PromiseBuffer, makePromiseBuffer, SENTRY_BUFFER_FULL_ERROR } from '../utils/promisebuffer';\nimport { type RateLimits, isRateLimited, updateRateLimits } from '../utils/ratelimit';\nimport { resolvedSyncPromise } from '../utils/syncpromise';\n\nexport const DEFAULT_TRANSPORT_BUFFER_SIZE = 64;\n\n/**\n * Creates an instance of a Sentry `Transport`\n *\n * @param options\n * @param makeRequest\n */\nexport function createTransport(\n  options: InternalBaseTransportOptions,\n  makeRequest: TransportRequestExecutor,\n  buffer: PromiseBuffer<TransportMakeRequestResponse> = makePromiseBuffer(\n    options.bufferSize || DEFAULT_TRANSPORT_BUFFER_SIZE,\n  ),\n): Transport {\n  let rateLimits: RateLimits = {};\n  const flush = (timeout?: number): PromiseLike<boolean> => buffer.drain(timeout);\n\n  function send(envelope: Envelope): PromiseLike<TransportMakeRequestResponse> {\n    const filteredEnvelopeItems: EnvelopeItem[] = [];\n\n    // Drop rate limited items from envelope\n    forEachEnvelopeItem(envelope, (item, type) => {\n      const dataCategory = envelopeItemTypeToDataCategory(type);\n      if (isRateLimited(rateLimits, dataCategory)) {\n        options.recordDroppedEvent('ratelimit_backoff', dataCategory);\n      } else {\n        filteredEnvelopeItems.push(item);\n      }\n    });\n\n    // Skip sending if envelope is empty after filtering out rate limited events\n    if (filteredEnvelopeItems.length === 0) {\n      return resolvedSyncPromise({});\n    }\n\n    const filteredEnvelope: Envelope = createEnvelope(envelope[0], filteredEnvelopeItems as (typeof envelope)[1]);\n\n    // Creates client report for each item in an envelope\n    const recordEnvelopeLoss = (reason: EventDropReason): void => {\n      forEachEnvelopeItem(filteredEnvelope, (item, type) => {\n        options.recordDroppedEvent(reason, envelopeItemTypeToDataCategory(type));\n      });\n    };\n\n    const requestTask = (): PromiseLike<TransportMakeRequestResponse> =>\n      makeRequest({ body: serializeEnvelope(filteredEnvelope) }).then(\n        response => {\n          // We don't want to throw on NOK responses, but we want to at least log them\n          if (response.statusCode !== undefined && (response.statusCode < 200 || response.statusCode >= 300)) {\n            DEBUG_BUILD && logger.warn(`Sentry responded with status code ${response.statusCode} to sent event.`);\n          }\n\n          rateLimits = updateRateLimits(rateLimits, response);\n          return response;\n        },\n        error => {\n          recordEnvelopeLoss('network_error');\n          DEBUG_BUILD && logger.error('Encountered error running transport request:', error);\n          throw error;\n        },\n      );\n\n    return buffer.add(requestTask).then(\n      result => result,\n      error => {\n        if (error === SENTRY_BUFFER_FULL_ERROR) {\n          DEBUG_BUILD && logger.error('Skipped sending event because buffer is full.');\n          recordEnvelopeLoss('queue_overflow');\n          return resolvedSyncPromise({});\n        } else {\n          throw error;\n        }\n      },\n    );\n  }\n\n  return {\n    send,\n    flush,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAoBO,MAAM,6BAA8B,GAAE;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,EAAE,OAAO;AACT,EAAE,WAAW;AACb,EAAE,MAAM,GAAgD,iBAAiB;AACzE,IAAI,OAAO,CAAC,UAAW,IAAG,6BAA6B;AACvD,GAAG;AACH,EAAa;AACb,EAAE,IAAI,UAAU,GAAe,EAAE;AACjC,EAAE,MAAM,KAAM,GAAE,CAAC,OAAO,KAAoC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;;AAEjF,EAAE,SAAS,IAAI,CAAC,QAAQ,EAAuD;AAC/E,IAAI,MAAM,qBAAqB,GAAmB,EAAE;;AAEpD;AACA,IAAI,mBAAmB,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK;AAClD,MAAM,MAAM,YAAa,GAAE,8BAA8B,CAAC,IAAI,CAAC;AAC/D,MAAM,IAAI,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE;AACnD,QAAQ,OAAO,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,YAAY,CAAC;AACrE,aAAa;AACb,QAAQ,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;AACxC;AACA,KAAK,CAAC;;AAEN;AACA,IAAI,IAAI,qBAAqB,CAAC,MAAO,KAAI,CAAC,EAAE;AAC5C,MAAM,OAAO,mBAAmB,CAAC,EAAE,CAAC;AACpC;;AAEA,IAAI,MAAM,gBAAgB,GAAa,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,qBAAA,EAA8C;;AAEjH;AACA,IAAI,MAAM,kBAAA,GAAqB,CAAC,MAAM,KAA4B;AAClE,MAAM,mBAAmB,CAAC,gBAAgB,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK;AAC5D,QAAQ,OAAO,CAAC,kBAAkB,CAAC,MAAM,EAAE,8BAA8B,CAAC,IAAI,CAAC,CAAC;AAChF,OAAO,CAAC;AACR,KAAK;;AAEL,IAAI,MAAM,WAAY,GAAE;AACxB,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC,gBAAgB,CAAE,EAAC,CAAC,CAAC,IAAI;AACrE,QAAQ,YAAY;AACpB;AACA,UAAU,IAAI,QAAQ,CAAC,eAAe,SAAA,KAAc,QAAQ,CAAC,UAAW,GAAE,OAAO,QAAQ,CAAC,UAAW,IAAG,GAAG,CAAC,EAAE;AAC9G,YAAY,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;AACjH;;AAEA,UAAU,aAAa,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC7D,UAAU,OAAO,QAAQ;AACzB,SAAS;AACT,QAAQ,SAAS;AACjB,UAAU,kBAAkB,CAAC,eAAe,CAAC;AAC7C,UAAU,WAAA,IAAe,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC;AAC5F,UAAU,MAAM,KAAK;AACrB,SAAS;AACT,OAAO;;AAEP,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI;AACvC,MAAM,MAAA,IAAU,MAAM;AACtB,MAAM,SAAS;AACf,QAAQ,IAAI,KAAM,KAAI,wBAAwB,EAAE;AAChD,UAAU,eAAe,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC;AACtF,UAAU,kBAAkB,CAAC,gBAAgB,CAAC;AAC9C,UAAU,OAAO,mBAAmB,CAAC,EAAE,CAAC;AACxC,eAAe;AACf,UAAU,MAAM,KAAK;AACrB;AACA,OAAO;AACP,KAAK;AACL;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,KAAK;AACT,GAAG;AACH;;;;"}