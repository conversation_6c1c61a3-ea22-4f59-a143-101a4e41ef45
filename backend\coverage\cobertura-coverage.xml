<?xml version="1.0" ?>
<!DOCTYPE coverage SYSTEM "http://cobertura.sourceforge.net/xml/coverage-04.dtd">
<coverage lines-valid="598" lines-covered="198" line-rate="0.3311" branches-valid="241" branches-covered="29" branch-rate="0.12029999999999999" timestamp="1751964989899" complexity="0" version="0.1">
  <sources>
    <source>E:\BUDGET TRACKER\backend</source>
  </sources>
  <packages>
    <package name="config" line-rate="0.7333" branch-rate="0.5">
      <classes>
        <class name="logger.js" filename="src\config\logger.js" line-rate="0.875" branch-rate="0.5">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="8" hits="1" branch="false"/>
            <line number="20" hits="1" branch="true" condition-coverage="50% (1/2)"/>
            <line number="21" hits="1" branch="false"/>
            <line number="26" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="sentry.js" filename="src\config\sentry.js" line-rate="0.5714" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="16" hits="0" branch="false"/>
            <line number="19" hits="0" branch="false"/>
            <line number="22" hits="1" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="controllers" line-rate="0.2441" branch-rate="0.1263">
      <classes>
        <class name="auth.controller.js" filename="src\controllers\auth.controller.js" line-rate="0.525" branch-rate="0.5347999999999999">
          <methods>
            <method name="(anonymous_0)" hits="16" signature="()V">
              <lines>
                <line number="7" hits="16"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="16" signature="()V">
              <lines>
                <line number="15" hits="16"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="21" signature="()V">
              <lines>
                <line number="51" hits="21"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="12" signature="()V">
              <lines>
                <line number="134" hits="12"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="2" signature="()V">
              <lines>
                <line number="195" hits="2"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="229" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_6)" hits="0" signature="()V">
              <lines>
                <line number="261" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_7)" hits="0" signature="()V">
              <lines>
                <line number="291" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="2" branch="false"/>
            <line number="2" hits="2" branch="false"/>
            <line number="3" hits="2" branch="false"/>
            <line number="4" hits="2" branch="false"/>
            <line number="7" hits="2" branch="false"/>
            <line number="8" hits="16" branch="true" condition-coverage="83.33333333333334% (5/6)"/>
            <line number="10" hits="0" branch="false"/>
            <line number="15" hits="2" branch="false"/>
            <line number="16" hits="16" branch="false"/>
            <line number="17" hits="16" branch="false"/>
            <line number="19" hits="16" branch="false"/>
            <line number="28" hits="16" branch="false"/>
            <line number="37" hits="16" branch="false"/>
            <line number="43" hits="0" branch="false"/>
            <line number="44" hits="0" branch="false"/>
            <line number="51" hits="2" branch="false"/>
            <line number="52" hits="21" branch="false"/>
            <line number="53" hits="21" branch="false"/>
            <line number="55" hits="21" branch="false"/>
            <line number="58" hits="21" branch="true" condition-coverage="100% (5/5)"/>
            <line number="59" hits="2" branch="false"/>
            <line number="60" hits="2" branch="false"/>
            <line number="68" hits="19" branch="false"/>
            <line number="69" hits="19" branch="false"/>
            <line number="70" hits="19" branch="true" condition-coverage="50% (1/2)"/>
            <line number="71" hits="0" branch="false"/>
            <line number="72" hits="0" branch="false"/>
            <line number="80" hits="19" branch="false"/>
            <line number="81" hits="19" branch="false"/>
            <line number="82" hits="19" branch="false"/>
            <line number="85" hits="19" branch="false"/>
            <line number="86" hits="19" branch="false"/>
            <line number="92" hits="16" branch="true" condition-coverage="50% (1/2)"/>
            <line number="93" hits="0" branch="false"/>
            <line number="94" hits="0" branch="false"/>
            <line number="102" hits="16" branch="false"/>
            <line number="103" hits="16" branch="false"/>
            <line number="106" hits="16" branch="false"/>
            <line number="107" hits="16" branch="false"/>
            <line number="108" hits="14" branch="false"/>
            <line number="110" hits="14" branch="false"/>
            <line number="111" hits="14" branch="false"/>
            <line number="122" hits="5" branch="false"/>
            <line number="123" hits="5" branch="false"/>
            <line number="134" hits="2" branch="false"/>
            <line number="135" hits="12" branch="false"/>
            <line number="136" hits="12" branch="false"/>
            <line number="138" hits="12" branch="false"/>
            <line number="141" hits="12" branch="true" condition-coverage="75% (3/4)"/>
            <line number="142" hits="0" branch="false"/>
            <line number="143" hits="0" branch="false"/>
            <line number="147" hits="12" branch="false"/>
            <line number="148" hits="12" branch="false"/>
            <line number="149" hits="11" branch="true" condition-coverage="100% (2/2)"/>
            <line number="150" hits="9" branch="false"/>
            <line number="151" hits="9" branch="false"/>
            <line number="155" hits="2" branch="false"/>
            <line number="156" hits="2" branch="false"/>
            <line number="157" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="158" hits="0" branch="false"/>
            <line number="159" hits="0" branch="false"/>
            <line number="163" hits="0" branch="false"/>
            <line number="164" hits="0" branch="false"/>
            <line number="167" hits="0" branch="false"/>
            <line number="168" hits="0" branch="false"/>
            <line number="169" hits="0" branch="false"/>
            <line number="171" hits="0" branch="false"/>
            <line number="172" hits="0" branch="false"/>
            <line number="183" hits="3" branch="false"/>
            <line number="184" hits="3" branch="false"/>
            <line number="195" hits="2" branch="false"/>
            <line number="196" hits="2" branch="false"/>
            <line number="197" hits="2" branch="false"/>
            <line number="199" hits="2" branch="false"/>
            <line number="201" hits="2" branch="true" condition-coverage="50% (1/2)"/>
            <line number="202" hits="0" branch="false"/>
            <line number="203" hits="0" branch="false"/>
            <line number="207" hits="2" branch="false"/>
            <line number="208" hits="2" branch="false"/>
            <line number="211" hits="0" branch="false"/>
            <line number="212" hits="0" branch="false"/>
            <line number="217" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="218" hits="0" branch="false"/>
            <line number="219" hits="0" branch="false"/>
            <line number="223" hits="0" branch="false"/>
            <line number="224" hits="0" branch="false"/>
            <line number="227" hits="0" branch="false"/>
            <line number="228" hits="0" branch="false"/>
            <line number="229" hits="0" branch="false"/>
            <line number="232" hits="0" branch="false"/>
            <line number="234" hits="0" branch="false"/>
            <line number="235" hits="0" branch="false"/>
            <line number="243" hits="2" branch="false"/>
            <line number="245" hits="2" branch="true" condition-coverage="50% (1/2)"/>
            <line number="246" hits="0" branch="false"/>
            <line number="247" hits="0" branch="false"/>
            <line number="250" hits="2" branch="false"/>
            <line number="261" hits="2" branch="false"/>
            <line number="262" hits="0" branch="false"/>
            <line number="263" hits="0" branch="false"/>
            <line number="265" hits="0" branch="false"/>
            <line number="267" hits="0" branch="false"/>
            <line number="268" hits="0" branch="false"/>
            <line number="279" hits="0" branch="false"/>
            <line number="280" hits="0" branch="false"/>
            <line number="291" hits="2" branch="false"/>
            <line number="292" hits="0" branch="false"/>
            <line number="293" hits="0" branch="false"/>
            <line number="295" hits="0" branch="false"/>
            <line number="298" hits="0" branch="false"/>
            <line number="300" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="301" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="302" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="303" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="306" hits="0" branch="false"/>
            <line number="307" hits="0" branch="false"/>
            <line number="313" hits="0" branch="false"/>
            <line number="314" hits="0" branch="false"/>
            <line number="325" hits="0" branch="false"/>
            <line number="326" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="budget.controller.js" filename="src\controllers\budget.controller.js" line-rate="0.0978" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="8" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="77" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="111" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="141" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="224" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="256" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_6)" hits="0" signature="()V">
              <lines>
                <line number="280" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_7)" hits="0" signature="()V">
              <lines>
                <line number="290" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_8)" hits="0" signature="()V">
              <lines>
                <line number="314" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_9)" hits="0" signature="()V">
              <lines>
                <line number="316" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="8" hits="1" branch="false"/>
            <line number="9" hits="0" branch="false"/>
            <line number="10" hits="0" branch="false"/>
            <line number="13" hits="0" branch="false"/>
            <line number="18" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="19" hits="0" branch="false"/>
            <line number="26" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="27" hits="0" branch="false"/>
            <line number="34" hits="0" branch="false"/>
            <line number="41" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="42" hits="0" branch="false"/>
            <line number="49" hits="0" branch="false"/>
            <line number="59" hits="0" branch="false"/>
            <line number="61" hits="0" branch="false"/>
            <line number="66" hits="0" branch="false"/>
            <line number="67" hits="0" branch="false"/>
            <line number="77" hits="1" branch="false"/>
            <line number="78" hits="0" branch="false"/>
            <line number="79" hits="0" branch="false"/>
            <line number="82" hits="0" branch="false"/>
            <line number="85" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="86" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="87" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="90" hits="0" branch="false"/>
            <line number="94" hits="0" branch="false"/>
            <line number="100" hits="0" branch="false"/>
            <line number="101" hits="0" branch="false"/>
            <line number="111" hits="1" branch="false"/>
            <line number="112" hits="0" branch="false"/>
            <line number="113" hits="0" branch="false"/>
            <line number="118" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="119" hits="0" branch="false"/>
            <line number="125" hits="0" branch="false"/>
            <line number="130" hits="0" branch="false"/>
            <line number="131" hits="0" branch="false"/>
            <line number="141" hits="1" branch="false"/>
            <line number="142" hits="0" branch="false"/>
            <line number="144" hits="0" branch="false"/>
            <line number="149" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="150" hits="0" branch="false"/>
            <line number="157" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="158" hits="0" branch="false"/>
            <line number="163" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="164" hits="0" branch="false"/>
            <line number="171" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="172" hits="0" branch="false"/>
            <line number="180" hits="0" branch="true" condition-coverage="0% (0/5)"/>
            <line number="181" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="182" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="183" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="185" hits="0" branch="false"/>
            <line number="193" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="194" hits="0" branch="false"/>
            <line number="202" hits="0" branch="false"/>
            <line number="208" hits="0" branch="false"/>
            <line number="213" hits="0" branch="false"/>
            <line number="214" hits="0" branch="false"/>
            <line number="224" hits="1" branch="false"/>
            <line number="225" hits="0" branch="false"/>
            <line number="226" hits="0" branch="false"/>
            <line number="231" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="232" hits="0" branch="false"/>
            <line number="238" hits="0" branch="false"/>
            <line number="240" hits="0" branch="false"/>
            <line number="245" hits="0" branch="false"/>
            <line number="246" hits="0" branch="false"/>
            <line number="256" hits="1" branch="false"/>
            <line number="257" hits="0" branch="false"/>
            <line number="258" hits="0" branch="false"/>
            <line number="260" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="261" hits="0" branch="false"/>
            <line number="268" hits="0" branch="false"/>
            <line number="275" hits="0" branch="false"/>
            <line number="276" hits="0" branch="false"/>
            <line number="279" hits="0" branch="false"/>
            <line number="282" hits="0" branch="false"/>
            <line number="289" hits="0" branch="false"/>
            <line number="290" hits="0" branch="false"/>
            <line number="294" hits="0" branch="false"/>
            <line number="295" hits="0" branch="false"/>
            <line number="297" hits="0" branch="false"/>
            <line number="314" hits="0" branch="false"/>
            <line number="315" hits="0" branch="false"/>
            <line number="316" hits="0" branch="false"/>
            <line number="319" hits="0" branch="false"/>
            <line number="320" hits="0" branch="false"/>
            <line number="322" hits="0" branch="false"/>
            <line number="336" hits="0" branch="false"/>
            <line number="337" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="category.controller.js" filename="src\controllers\category.controller.js" line-rate="0.129" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="7" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="49" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="79" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="109" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="163" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="208" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="7" hits="1" branch="false"/>
            <line number="8" hits="0" branch="false"/>
            <line number="9" hits="0" branch="false"/>
            <line number="12" hits="0" branch="false"/>
            <line number="17" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="18" hits="0" branch="false"/>
            <line number="25" hits="0" branch="false"/>
            <line number="33" hits="0" branch="false"/>
            <line number="38" hits="0" branch="false"/>
            <line number="39" hits="0" branch="false"/>
            <line number="49" hits="1" branch="false"/>
            <line number="50" hits="0" branch="false"/>
            <line number="51" hits="0" branch="false"/>
            <line number="54" hits="0" branch="false"/>
            <line number="57" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="60" hits="0" branch="false"/>
            <line number="62" hits="0" branch="false"/>
            <line number="68" hits="0" branch="false"/>
            <line number="69" hits="0" branch="false"/>
            <line number="79" hits="1" branch="false"/>
            <line number="80" hits="0" branch="false"/>
            <line number="81" hits="0" branch="false"/>
            <line number="86" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="87" hits="0" branch="false"/>
            <line number="93" hits="0" branch="false"/>
            <line number="98" hits="0" branch="false"/>
            <line number="99" hits="0" branch="false"/>
            <line number="109" hits="1" branch="false"/>
            <line number="110" hits="0" branch="false"/>
            <line number="112" hits="0" branch="false"/>
            <line number="117" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="118" hits="0" branch="false"/>
            <line number="125" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="126" hits="0" branch="false"/>
            <line number="132" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="133" hits="0" branch="false"/>
            <line number="141" hits="0" branch="false"/>
            <line number="147" hits="0" branch="false"/>
            <line number="152" hits="0" branch="false"/>
            <line number="153" hits="0" branch="false"/>
            <line number="163" hits="1" branch="false"/>
            <line number="164" hits="0" branch="false"/>
            <line number="166" hits="0" branch="false"/>
            <line number="171" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="172" hits="0" branch="false"/>
            <line number="179" hits="0" branch="false"/>
            <line number="183" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="184" hits="0" branch="false"/>
            <line number="190" hits="0" branch="false"/>
            <line number="192" hits="0" branch="false"/>
            <line number="197" hits="0" branch="false"/>
            <line number="198" hits="0" branch="false"/>
            <line number="208" hits="1" branch="false"/>
            <line number="209" hits="0" branch="false"/>
            <line number="211" hits="0" branch="false"/>
            <line number="223" hits="0" branch="false"/>
            <line number="231" hits="0" branch="false"/>
            <line number="233" hits="0" branch="false"/>
            <line number="239" hits="0" branch="false"/>
            <line number="240" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="system.controller.js" filename="src\controllers\system.controller.js" line-rate="0.6666" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="1" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="0" branch="false"/>
            <line number="10" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="transaction.controller.js" filename="src\controllers\transaction.controller.js" line-rate="0.11109999999999999" branch-rate="0">
          <methods>
            <method name="validateTransactionInput" hits="0" signature="()V">
              <lines>
                <line number="9" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="48" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="128" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="197" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="232" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="296" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_6)" hits="0" signature="()V">
              <lines>
                <line number="334" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="5" hits="1" branch="false"/>
            <line number="6" hits="1" branch="false"/>
            <line number="10" hits="0" branch="false"/>
            <line number="13" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="14" hits="0" branch="false"/>
            <line number="18" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="19" hits="0" branch="false"/>
            <line number="23" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="24" hits="0" branch="false"/>
            <line number="28" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="29" hits="0" branch="false"/>
            <line number="33" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="34" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="35" hits="0" branch="false"/>
            <line number="37" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="38" hits="0" branch="false"/>
            <line number="42" hits="0" branch="false"/>
            <line number="48" hits="1" branch="false"/>
            <line number="49" hits="0" branch="false"/>
            <line number="50" hits="0" branch="false"/>
            <line number="53" hits="0" branch="false"/>
            <line number="54" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="55" hits="0" branch="false"/>
            <line number="63" hits="0" branch="false"/>
            <line number="68" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="69" hits="0" branch="false"/>
            <line number="76" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="77" hits="0" branch="false"/>
            <line number="84" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="85" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="86" hits="0" branch="false"/>
            <line number="90" hits="0" branch="false"/>
            <line number="102" hits="0" branch="false"/>
            <line number="105" hits="0" branch="false"/>
            <line number="107" hits="0" branch="false"/>
            <line number="117" hits="0" branch="false"/>
            <line number="118" hits="0" branch="false"/>
            <line number="128" hits="1" branch="false"/>
            <line number="129" hits="0" branch="false"/>
            <line number="130" hits="0" branch="false"/>
            <line number="133" hits="0" branch="false"/>
            <line number="134" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="135" hits="0" branch="false"/>
            <line number="145" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="146" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="147" hits="0" branch="false"/>
            <line number="149" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="150" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="152" hits="0" branch="false"/>
            <line number="158" hits="0" branch="false"/>
            <line number="160" hits="0" branch="false"/>
            <line number="175" hits="0" branch="false"/>
            <line number="176" hits="0" branch="false"/>
            <line number="182" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="184" hits="0" branch="false"/>
            <line number="186" hits="0" branch="false"/>
            <line number="197" hits="1" branch="false"/>
            <line number="198" hits="0" branch="false"/>
            <line number="199" hits="0" branch="false"/>
            <line number="204" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="205" hits="0" branch="false"/>
            <line number="211" hits="0" branch="false"/>
            <line number="221" hits="0" branch="false"/>
            <line number="222" hits="0" branch="false"/>
            <line number="232" hits="1" branch="false"/>
            <line number="233" hits="0" branch="false"/>
            <line number="235" hits="0" branch="false"/>
            <line number="241" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="242" hits="0" branch="false"/>
            <line number="249" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="250" hits="0" branch="false"/>
            <line number="255" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="256" hits="0" branch="false"/>
            <line number="263" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="264" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="265" hits="0" branch="false"/>
            <line number="273" hits="0" branch="false"/>
            <line number="275" hits="0" branch="false"/>
            <line number="285" hits="0" branch="false"/>
            <line number="286" hits="0" branch="false"/>
            <line number="296" hits="1" branch="false"/>
            <line number="297" hits="0" branch="false"/>
            <line number="298" hits="0" branch="false"/>
            <line number="303" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="304" hits="0" branch="false"/>
            <line number="311" hits="0" branch="false"/>
            <line number="313" hits="0" branch="false"/>
            <line number="323" hits="0" branch="false"/>
            <line number="324" hits="0" branch="false"/>
            <line number="334" hits="1" branch="false"/>
            <line number="335" hits="0" branch="false"/>
            <line number="336" hits="0" branch="false"/>
            <line number="338" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="339" hits="0" branch="false"/>
            <line number="345" hits="0" branch="false"/>
            <line number="369" hits="0" branch="false"/>
            <line number="418" hits="0" branch="false"/>
            <line number="419" hits="0" branch="false"/>
            <line number="432" hits="0" branch="false"/>
            <line number="433" hits="0" branch="false"/>
            <line number="439" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="441" hits="0" branch="false"/>
            <line number="443" hits="0" branch="false"/>
            <line number="444" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="middleware" line-rate="0.16920000000000002" branch-rate="0">
      <classes>
        <class name="auth.js" filename="src\middleware\auth.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="11" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="2" hits="0" branch="false"/>
            <line number="3" hits="0" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="6" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="7" hits="0" branch="false"/>
            <line number="11" hits="0" branch="false"/>
            <line number="12" hits="0" branch="false"/>
            <line number="13" hits="0" branch="false"/>
            <line number="15" hits="0" branch="false"/>
            <line number="16" hits="0" branch="true" condition-coverage="0% (0/4)"/>
            <line number="17" hits="0" branch="false"/>
            <line number="20" hits="0" branch="false"/>
            <line number="21" hits="0" branch="false"/>
            <line number="26" hits="0" branch="false"/>
            <line number="31" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="32" hits="0" branch="false"/>
            <line number="35" hits="0" branch="false"/>
            <line number="36" hits="0" branch="false"/>
            <line number="37" hits="0" branch="false"/>
            <line number="39" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="40" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="41" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="43" hits="0" branch="false"/>
            <line number="51" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="auth.middleware.js" filename="src\middleware\auth.middleware.js" line-rate="0.21420000000000003" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="8" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="12" hits="0" branch="false"/>
            <line number="16" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="17" hits="0" branch="false"/>
            <line number="23" hits="0" branch="false"/>
            <line number="25" hits="0" branch="false"/>
            <line number="28" hits="0" branch="false"/>
            <line number="30" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="31" hits="0" branch="false"/>
            <line number="37" hits="0" branch="false"/>
            <line number="39" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="errorHandler.js" filename="src\middleware\errorHandler.js" line-rate="0.2173" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="31" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="5" hits="1" branch="false"/>
            <line number="7" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="8" hits="0" branch="false"/>
            <line number="11" hits="0" branch="false"/>
            <line number="12" hits="0" branch="false"/>
            <line number="15" hits="0" branch="false"/>
            <line number="18" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="19" hits="0" branch="false"/>
            <line number="20" hits="0" branch="false"/>
            <line number="24" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="25" hits="0" branch="false"/>
            <line number="26" hits="0" branch="false"/>
            <line number="30" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="31" hits="0" branch="false"/>
            <line number="32" hits="0" branch="false"/>
            <line number="36" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="37" hits="0" branch="false"/>
            <line number="38" hits="0" branch="false"/>
            <line number="41" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="48" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="rateLimiter.js" filename="src\middleware\rateLimiter.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="9" hits="1" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="models" line-rate="0.6274000000000001" branch-rate="0.3">
      <classes>
        <class name="budget.model.js" filename="src\models\budget.model.js" line-rate="0.3846" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="43" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="40" hits="1" branch="false"/>
            <line number="43" hits="1" branch="false"/>
            <line number="44" hits="0" branch="false"/>
            <line number="47" hits="0" branch="false"/>
            <line number="48" hits="0" branch="false"/>
            <line number="51" hits="0" branch="false"/>
            <line number="68" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="69" hits="0" branch="false"/>
            <line number="70" hits="0" branch="false"/>
            <line number="72" hits="0" branch="false"/>
            <line number="80" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="category.model.js" filename="src\models\category.model.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="36" hits="1" branch="false"/>
            <line number="38" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="transaction.model.js" filename="src\models\transaction.model.js" line-rate="0.7142000000000001" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="60" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="57" hits="1" branch="false"/>
            <line number="60" hits="1" branch="false"/>
            <line number="61" hits="0" branch="false"/>
            <line number="77" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="80" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="user.model.js" filename="src\models\user.model.js" line-rate="0.6666" branch-rate="0.5">
          <methods>
            <method name="(anonymous_0)" hits="5" signature="()V">
              <lines>
                <line number="29" hits="5"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="2" signature="()V">
              <lines>
                <line number="56" hits="2"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="2" signature="()V">
              <lines>
                <line number="67" hits="2"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="77" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="85" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="92" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_6)" hits="0" signature="()V">
              <lines>
                <line number="97" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="3" branch="false"/>
            <line number="2" hits="3" branch="false"/>
            <line number="3" hits="3" branch="false"/>
            <line number="4" hits="3" branch="false"/>
            <line number="6" hits="3" branch="false"/>
            <line number="30" hits="5" branch="false"/>
            <line number="56" hits="3" branch="false"/>
            <line number="57" hits="2" branch="true" condition-coverage="50% (1/2)"/>
            <line number="58" hits="0" branch="false"/>
            <line number="62" hits="2" branch="false"/>
            <line number="63" hits="2" branch="false"/>
            <line number="67" hits="3" branch="false"/>
            <line number="68" hits="2" branch="true" condition-coverage="50% (2/4)"/>
            <line number="69" hits="2" branch="false"/>
            <line number="72" hits="0" branch="false"/>
            <line number="73" hits="0" branch="false"/>
            <line number="77" hits="3" branch="false"/>
            <line number="78" hits="0" branch="false"/>
            <line number="85" hits="3" branch="false"/>
            <line number="86" hits="0" branch="false"/>
            <line number="87" hits="0" branch="false"/>
            <line number="88" hits="0" branch="false"/>
            <line number="92" hits="3" branch="false"/>
            <line number="93" hits="0" branch="false"/>
            <line number="97" hits="3" branch="false"/>
            <line number="98" hits="0" branch="false"/>
            <line number="101" hits="3" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="routes" line-rate="1" branch-rate="1">
      <classes>
        <class name="auth.routes.js" filename="src\routes\auth.routes.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="3" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="7" hits="1" branch="false"/>
            <line number="8" hits="1" branch="false"/>
            <line number="11" hits="1" branch="false"/>
            <line number="12" hits="1" branch="false"/>
            <line number="14" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="budget.routes.js" filename="src\routes\budget.routes.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="9" hits="1" branch="false"/>
            <line number="10" hits="1" branch="false"/>
            <line number="12" hits="1" branch="false"/>
            <line number="15" hits="1" branch="false"/>
            <line number="18" hits="1" branch="false"/>
            <line number="22" hits="1" branch="false"/>
            <line number="25" hits="1" branch="false"/>
            <line number="30" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="category.routes.js" filename="src\routes\category.routes.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="9" hits="1" branch="false"/>
            <line number="10" hits="1" branch="false"/>
            <line number="12" hits="1" branch="false"/>
            <line number="15" hits="1" branch="false"/>
            <line number="18" hits="1" branch="false"/>
            <line number="22" hits="1" branch="false"/>
            <line number="25" hits="1" branch="false"/>
            <line number="30" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="system.routes.js" filename="src\routes\system.routes.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="4" hits="1" branch="false"/>
            <line number="6" hits="1" branch="false"/>
            <line number="8" hits="1" branch="false"/>
          </lines>
        </class>
        <class name="transaction.routes.js" filename="src\routes\transaction.routes.js" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="9" hits="1" branch="false"/>
            <line number="10" hits="1" branch="false"/>
            <line number="12" hits="1" branch="false"/>
            <line number="15" hits="1" branch="false"/>
            <line number="18" hits="1" branch="false"/>
            <line number="22" hits="1" branch="false"/>
            <line number="25" hits="1" branch="false"/>
            <line number="30" hits="1" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="services" line-rate="0.3478" branch-rate="0.2857">
      <classes>
        <class name="cache.service.js" filename="src\services\cache.service.js" line-rate="0.3478" branch-rate="0.2857">
          <methods>
            <method name="(anonymous_0)" hits="1" signature="()V">
              <lines>
                <line number="5" hits="1"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="14" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="19" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="32" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="40" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="1" branch="false"/>
            <line number="2" hits="1" branch="false"/>
            <line number="6" hits="1" branch="false"/>
            <line number="10" hits="1" branch="false"/>
            <line number="11" hits="1" branch="false"/>
            <line number="12" hits="1" branch="false"/>
            <line number="14" hits="1" branch="false"/>
            <line number="15" hits="0" branch="false"/>
            <line number="20" hits="0" branch="false"/>
            <line number="21" hits="0" branch="false"/>
            <line number="22" hits="0" branch="false"/>
            <line number="23" hits="0" branch="false"/>
            <line number="24" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="25" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="27" hits="0" branch="false"/>
            <line number="28" hits="0" branch="false"/>
            <line number="33" hits="0" branch="false"/>
            <line number="34" hits="0" branch="false"/>
            <line number="36" hits="0" branch="false"/>
            <line number="41" hits="0" branch="false"/>
            <line number="42" hits="0" branch="false"/>
            <line number="44" hits="0" branch="false"/>
            <line number="49" hits="1" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="tests" line-rate="0" branch-rate="1">
      <classes>
        <class name="test-connection.js" filename="src\tests\test-connection.js" line-rate="0" branch-rate="1">
          <methods>
            <method name="testConnection" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="2" hits="0" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="6" hits="0" branch="false"/>
            <line number="8" hits="0" branch="false"/>
            <line number="13" hits="0" branch="false"/>
            <line number="14" hits="0" branch="false"/>
            <line number="20" hits="0" branch="false"/>
            <line number="21" hits="0" branch="false"/>
            <line number="23" hits="0" branch="false"/>
            <line number="24" hits="0" branch="false"/>
            <line number="25" hits="0" branch="false"/>
            <line number="29" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="utils" line-rate="0.2" branch-rate="1">
      <classes>
        <class name="ApiError.js" filename="src\utils\ApiError.js" line-rate="0.2" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="2" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="3" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="6" hits="0" branch="false"/>
            <line number="10" hits="2" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>
