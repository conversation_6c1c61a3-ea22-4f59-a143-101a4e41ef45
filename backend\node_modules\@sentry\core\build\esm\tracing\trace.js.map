{"version": 3, "file": "trace.js", "sources": ["../../../src/tracing/trace.ts"], "sourcesContent": ["/* eslint-disable max-lines */\n\nimport { getAsyncContextStrategy } from '../asyncContext';\nimport type { AsyncContextStrategy } from '../asyncContext/types';\nimport { getMainCarrier } from '../carrier';\nimport { getClient, getCurrentScope, getIsolationScope, withScope } from '../currentScopes';\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { Scope } from '../scope';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE } from '../semanticAttributes';\nimport type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport type { ClientOptions } from '../types-hoist/options';\nimport type { SentrySpanArguments, Span, SpanTimeInput } from '../types-hoist/span';\nimport type { StartSpanOptions } from '../types-hoist/startSpanOptions';\nimport { handleCallbackErrors } from '../utils/handleCallbackErrors';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled';\nimport { logger } from '../utils/logger';\nimport { parseSampleRate } from '../utils/parseSampleRate';\nimport { generateTraceId } from '../utils/propagationContext';\nimport { _getSpanForScope, _setSpanForScope } from '../utils/spanOnScope';\nimport { addChildSpanToSpan, getRootSpan, spanIsSampled, spanTimeInputToSeconds, spanToJSON } from '../utils/spanUtils';\nimport { propagationContextFromHeaders } from '../utils/tracing';\nimport { freezeDscOnSpan, getDynamicSamplingContextFromSpan } from './dynamicSamplingContext';\nimport { logSpanStart } from './logSpans';\nimport { sampleSpan } from './sampling';\nimport { SentryNonRecordingSpan } from './sentryNonRecordingSpan';\nimport { SentrySpan } from './sentrySpan';\nimport { SPAN_STATUS_ERROR } from './spanstatus';\nimport { setCapturedScopesOnSpan } from './utils';\n\nconst SUPPRESS_TRACING_KEY = '__SENTRY_SUPPRESS_TRACING__';\n\n/**\n * Wraps a function with a transaction/span and finishes the span after the function is done.\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * If you want to create a span that is not set as active, use {@link startInactiveSpan}.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpan<T>(options: StartSpanOptions, callback: (span: Span) => T): T {\n  const acs = getAcs();\n  if (acs.startSpan) {\n    return acs.startSpan(options, callback);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan, scope: customScope } = options;\n\n  // We still need to fork a potentially passed scope, as we set the active span on it\n  // and we need to ensure that it is cleaned up properly once the span ends.\n  const customForkedScope = customScope?.clone();\n\n  return withScope(customForkedScope, () => {\n    // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n    const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n    return wrapper(() => {\n      const scope = getCurrentScope();\n      const parentSpan = getParentSpan(scope);\n\n      const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? new SentryNonRecordingSpan()\n        : createChildOrRootSpan({\n            parentSpan,\n            spanArguments,\n            forceTransaction,\n            scope,\n          });\n\n      _setSpanForScope(scope, activeSpan);\n\n      return handleCallbackErrors(\n        () => callback(activeSpan),\n        () => {\n          // Only update the span status if it hasn't been changed yet, and the span is not yet finished\n          const { status } = spanToJSON(activeSpan);\n          if (activeSpan.isRecording() && (!status || status === 'ok')) {\n            activeSpan.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n          }\n        },\n        () => {\n          activeSpan.end();\n        },\n      );\n    });\n  });\n}\n\n/**\n * Similar to `Sentry.startSpan`. Wraps a function with a transaction/span, but does not finish the span\n * after the function is done automatically. Use `span.end()` to end the span.\n *\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpanManual<T>(options: StartSpanOptions, callback: (span: Span, finish: () => void) => T): T {\n  const acs = getAcs();\n  if (acs.startSpanManual) {\n    return acs.startSpanManual(options, callback);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan, scope: customScope } = options;\n\n  const customForkedScope = customScope?.clone();\n\n  return withScope(customForkedScope, () => {\n    // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n    const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n    return wrapper(() => {\n      const scope = getCurrentScope();\n      const parentSpan = getParentSpan(scope);\n\n      const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? new SentryNonRecordingSpan()\n        : createChildOrRootSpan({\n            parentSpan,\n            spanArguments,\n            forceTransaction,\n            scope,\n          });\n\n      _setSpanForScope(scope, activeSpan);\n\n      return handleCallbackErrors(\n        // We pass the `finish` function to the callback, so the user can finish the span manually\n        // this is mainly here for historic purposes because previously, we instructed users to call\n        // `finish` instead of `span.end()` to also clean up the scope. Nowadays, calling `span.end()`\n        // or `finish` has the same effect and we simply leave it here to avoid breaking user code.\n        () => callback(activeSpan, () => activeSpan.end()),\n        () => {\n          // Only update the span status if it hasn't been changed yet, and the span is not yet finished\n          const { status } = spanToJSON(activeSpan);\n          if (activeSpan.isRecording() && (!status || status === 'ok')) {\n            activeSpan.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n          }\n        },\n      );\n    });\n  });\n}\n\n/**\n * Creates a span. This span is not set as active, so will not get automatic instrumentation spans\n * as children or be able to be accessed via `Sentry.getActiveSpan()`.\n *\n * If you want to create a span that is set as active, use {@link startSpan}.\n *\n * This function will always return a span,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startInactiveSpan(options: StartSpanOptions): Span {\n  const acs = getAcs();\n  if (acs.startInactiveSpan) {\n    return acs.startInactiveSpan(options);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan } = options;\n\n  // If `options.scope` is defined, we use this as as a wrapper,\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = options.scope\n    ? (callback: () => Span) => withScope(options.scope, callback)\n    : customParentSpan !== undefined\n      ? (callback: () => Span) => withActiveSpan(customParentSpan, callback)\n      : (callback: () => Span) => callback();\n\n  return wrapper(() => {\n    const scope = getCurrentScope();\n    const parentSpan = getParentSpan(scope);\n\n    const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n\n    if (shouldSkipSpan) {\n      return new SentryNonRecordingSpan();\n    }\n\n    return createChildOrRootSpan({\n      parentSpan,\n      spanArguments,\n      forceTransaction,\n      scope,\n    });\n  });\n}\n\n/**\n * Continue a trace from `sentry-trace` and `baggage` values.\n * These values can be obtained from incoming request headers, or in the browser from `<meta name=\"sentry-trace\">`\n * and `<meta name=\"baggage\">` HTML tags.\n *\n * Spans started with `startSpan`, `startSpanManual` and `startInactiveSpan`, within the callback will automatically\n * be attached to the incoming trace.\n */\nexport const continueTrace = <V>(\n  options: {\n    sentryTrace: Parameters<typeof propagationContextFromHeaders>[0];\n    baggage: Parameters<typeof propagationContextFromHeaders>[1];\n  },\n  callback: () => V,\n): V => {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.continueTrace) {\n    return acs.continueTrace(options, callback);\n  }\n\n  const { sentryTrace, baggage } = options;\n\n  return withScope(scope => {\n    const propagationContext = propagationContextFromHeaders(sentryTrace, baggage);\n    scope.setPropagationContext(propagationContext);\n    return callback();\n  });\n};\n\n/**\n * Forks the current scope and sets the provided span as active span in the context of the provided callback. Can be\n * passed `null` to start an entirely new span tree.\n *\n * @param span Spans started in the context of the provided callback will be children of this span. If `null` is passed,\n * spans started within the callback will not be attached to a parent span.\n * @param callback Execution context in which the provided span will be active. Is passed the newly forked scope.\n * @returns the value returned from the provided callback function.\n */\nexport function withActiveSpan<T>(span: Span | null, callback: (scope: Scope) => T): T {\n  const acs = getAcs();\n  if (acs.withActiveSpan) {\n    return acs.withActiveSpan(span, callback);\n  }\n\n  return withScope(scope => {\n    _setSpanForScope(scope, span || undefined);\n    return callback(scope);\n  });\n}\n\n/** Suppress tracing in the given callback, ensuring no spans are generated inside of it. */\nexport function suppressTracing<T>(callback: () => T): T {\n  const acs = getAcs();\n\n  if (acs.suppressTracing) {\n    return acs.suppressTracing(callback);\n  }\n\n  return withScope(scope => {\n    // Note: We do not wait for the callback to finish before we reset the metadata\n    // the reason for this is that otherwise, in the browser this can lead to very weird behavior\n    // as there is only a single top scope, if the callback takes longer to finish,\n    // other, unrelated spans may also be suppressed, which we do not want\n    // so instead, we only suppress tracing synchronoysly in the browser\n    scope.setSDKProcessingMetadata({ [SUPPRESS_TRACING_KEY]: true });\n    const res = callback();\n    scope.setSDKProcessingMetadata({ [SUPPRESS_TRACING_KEY]: undefined });\n    return res;\n  });\n}\n\n/**\n * Starts a new trace for the duration of the provided callback. Spans started within the\n * callback will be part of the new trace instead of a potentially previously started trace.\n *\n * Important: Only use this function if you want to override the default trace lifetime and\n * propagation mechanism of the SDK for the duration and scope of the provided callback.\n * The newly created trace will also be the root of a new distributed trace, for example if\n * you make http requests within the callback.\n * This function might be useful if the operation you want to instrument should not be part\n * of a potentially ongoing trace.\n *\n * Default behavior:\n * - Server-side: A new trace is started for each incoming request.\n * - Browser: A new trace is started for each page our route. Navigating to a new route\n *            or page will automatically create a new trace.\n */\nexport function startNewTrace<T>(callback: () => T): T {\n  return withScope(scope => {\n    scope.setPropagationContext({\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    });\n    DEBUG_BUILD && logger.info(`Starting a new trace with id ${scope.getPropagationContext().traceId}`);\n    return withActiveSpan(null, callback);\n  });\n}\n\nfunction createChildOrRootSpan({\n  parentSpan,\n  spanArguments,\n  forceTransaction,\n  scope,\n}: {\n  parentSpan: SentrySpan | undefined;\n  spanArguments: SentrySpanArguments;\n  forceTransaction?: boolean;\n  scope: Scope;\n}): Span {\n  if (!hasSpansEnabled()) {\n    const span = new SentryNonRecordingSpan();\n\n    // If this is a root span, we ensure to freeze a DSC\n    // So we can have at least partial data here\n    if (forceTransaction || !parentSpan) {\n      const dsc = {\n        sampled: 'false',\n        sample_rate: '0',\n        transaction: spanArguments.name,\n        ...getDynamicSamplingContextFromSpan(span),\n      } satisfies Partial<DynamicSamplingContext>;\n      freezeDscOnSpan(span, dsc);\n    }\n\n    return span;\n  }\n\n  const isolationScope = getIsolationScope();\n\n  let span: Span;\n  if (parentSpan && !forceTransaction) {\n    span = _startChildSpan(parentSpan, scope, spanArguments);\n    addChildSpanToSpan(parentSpan, span);\n  } else if (parentSpan) {\n    // If we forced a transaction but have a parent span, make sure to continue from the parent span, not the scope\n    const dsc = getDynamicSamplingContextFromSpan(parentSpan);\n    const { traceId, spanId: parentSpanId } = parentSpan.spanContext();\n    const parentSampled = spanIsSampled(parentSpan);\n\n    span = _startRootSpan(\n      {\n        traceId,\n        parentSpanId,\n        ...spanArguments,\n      },\n      scope,\n      parentSampled,\n    );\n\n    freezeDscOnSpan(span, dsc);\n  } else {\n    const {\n      traceId,\n      dsc,\n      parentSpanId,\n      sampled: parentSampled,\n    } = {\n      ...isolationScope.getPropagationContext(),\n      ...scope.getPropagationContext(),\n    };\n\n    span = _startRootSpan(\n      {\n        traceId,\n        parentSpanId,\n        ...spanArguments,\n      },\n      scope,\n      parentSampled,\n    );\n\n    if (dsc) {\n      freezeDscOnSpan(span, dsc);\n    }\n  }\n\n  logSpanStart(span);\n\n  setCapturedScopesOnSpan(span, scope, isolationScope);\n\n  return span;\n}\n\n/**\n * This converts StartSpanOptions to SentrySpanArguments.\n * For the most part (for now) we accept the same options,\n * but some of them need to be transformed.\n */\nfunction parseSentrySpanArguments(options: StartSpanOptions): SentrySpanArguments {\n  const exp = options.experimental || {};\n  const initialCtx: SentrySpanArguments = {\n    isStandalone: exp.standalone,\n    ...options,\n  };\n\n  if (options.startTime) {\n    const ctx: SentrySpanArguments & { startTime?: SpanTimeInput } = { ...initialCtx };\n    ctx.startTimestamp = spanTimeInputToSeconds(options.startTime);\n    delete ctx.startTime;\n    return ctx;\n  }\n\n  return initialCtx;\n}\n\nfunction getAcs(): AsyncContextStrategy {\n  const carrier = getMainCarrier();\n  return getAsyncContextStrategy(carrier);\n}\n\nfunction _startRootSpan(spanArguments: SentrySpanArguments, scope: Scope, parentSampled?: boolean): SentrySpan {\n  const client = getClient();\n  const options: Partial<ClientOptions> = client?.getOptions() || {};\n\n  const { name = '' } = spanArguments;\n\n  const mutableSpanSamplingData = { spanAttributes: { ...spanArguments.attributes }, spanName: name, parentSampled };\n\n  // we don't care about the decision for the moment; this is just a placeholder\n  client?.emit('beforeSampling', mutableSpanSamplingData, { decision: false });\n\n  // If hook consumers override the parentSampled flag, we will use that value instead of the actual one\n  const finalParentSampled = mutableSpanSamplingData.parentSampled ?? parentSampled;\n  const finalAttributes = mutableSpanSamplingData.spanAttributes;\n\n  const currentPropagationContext = scope.getPropagationContext();\n  const [sampled, sampleRate, localSampleRateWasApplied] = scope.getScopeData().sdkProcessingMetadata[\n    SUPPRESS_TRACING_KEY\n  ]\n    ? [false]\n    : sampleSpan(\n        options,\n        {\n          name,\n          parentSampled: finalParentSampled,\n          attributes: finalAttributes,\n          parentSampleRate: parseSampleRate(currentPropagationContext.dsc?.sample_rate),\n        },\n        currentPropagationContext.sampleRand,\n      );\n\n  const rootSpan = new SentrySpan({\n    ...spanArguments,\n    attributes: {\n      [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'custom',\n      [SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE]:\n        sampleRate !== undefined && localSampleRateWasApplied ? sampleRate : undefined,\n      ...finalAttributes,\n    },\n    sampled,\n  });\n\n  if (!sampled && client) {\n    DEBUG_BUILD && logger.log('[Tracing] Discarding root span because its trace was not chosen to be sampled.');\n    client.recordDroppedEvent('sample_rate', 'transaction');\n  }\n\n  if (client) {\n    client.emit('spanStart', rootSpan);\n  }\n\n  return rootSpan;\n}\n\n/**\n * Creates a new `Span` while setting the current `Span.id` as `parentSpanId`.\n * This inherits the sampling decision from the parent span.\n */\nfunction _startChildSpan(parentSpan: Span, scope: Scope, spanArguments: SentrySpanArguments): Span {\n  const { spanId, traceId } = parentSpan.spanContext();\n  const sampled = scope.getScopeData().sdkProcessingMetadata[SUPPRESS_TRACING_KEY] ? false : spanIsSampled(parentSpan);\n\n  const childSpan = sampled\n    ? new SentrySpan({\n        ...spanArguments,\n        parentSpanId: spanId,\n        traceId,\n        sampled,\n      })\n    : new SentryNonRecordingSpan({ traceId });\n\n  addChildSpanToSpan(parentSpan, childSpan);\n\n  const client = getClient();\n  if (client) {\n    client.emit('spanStart', childSpan);\n    // If it has an endTimestamp, it's already ended\n    if (spanArguments.endTimestamp) {\n      client.emit('spanEnd', childSpan);\n    }\n  }\n\n  return childSpan;\n}\n\nfunction getParentSpan(scope: Scope): SentrySpan | undefined {\n  const span = _getSpanForScope(scope) as SentrySpan | undefined;\n\n  if (!span) {\n    return undefined;\n  }\n\n  const client = getClient();\n  const options: Partial<ClientOptions> = client ? client.getOptions() : {};\n  if (options.parentSpanIsAlwaysRootSpan) {\n    return getRootSpan(span) as SentrySpan;\n  }\n\n  return span;\n}\n\nfunction getActiveSpanWrapper<T>(parentSpan: Span | undefined | null): (callback: () => T) => T {\n  return parentSpan !== undefined\n    ? (callback: () => T) => {\n        return withActiveSpan(parentSpan, callback);\n      }\n    : (callback: () => T) => callback();\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;;AA6BA,MAAM,oBAAA,GAAuB,6BAA6B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAI,OAAO,EAAoB,QAAQ,EAAwB;AACxF,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,SAAS,EAAE;AACrB,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC3C;;AAEA,EAAE,MAAM,aAAc,GAAE,wBAAwB,CAAC,OAAO,CAAC;AACzD,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAY,EAAA,GAAI,OAAO;;AAExF;AACA;AACA,EAAE,MAAM,iBAAkB,GAAE,WAAW,EAAE,KAAK,EAAE;;AAEhD,EAAE,OAAO,SAAS,CAAC,iBAAiB,EAAE,MAAM;AAC5C;AACA,IAAI,MAAM,OAAQ,GAAE,oBAAoB,CAAI,gBAAgB,CAAC;;AAE7D,IAAI,OAAO,OAAO,CAAC,MAAM;AACzB,MAAM,MAAM,KAAA,GAAQ,eAAe,EAAE;AACrC,MAAM,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,CAAC;;AAE7C,MAAM,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU;AAChE,MAAM,MAAM,aAAa;AACzB,UAAU,IAAI,sBAAsB;AACpC,UAAU,qBAAqB,CAAC;AAChC,YAAY,UAAU;AACtB,YAAY,aAAa;AACzB,YAAY,gBAAgB;AAC5B,YAAY,KAAK;AACjB,WAAW,CAAC;;AAEZ,MAAM,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC;;AAEzC,MAAM,OAAO,oBAAoB;AACjC,QAAQ,MAAM,QAAQ,CAAC,UAAU,CAAC;AAClC,QAAQ,MAAM;AACd;AACA,UAAU,MAAM,EAAE,MAAO,EAAA,GAAI,UAAU,CAAC,UAAU,CAAC;AACnD,UAAU,IAAI,UAAU,CAAC,WAAW,EAAG,KAAI,CAAC,UAAU,MAAA,KAAW,IAAI,CAAC,EAAE;AACxE,YAAY,UAAU,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,gBAAA,EAAkB,CAAC;AACxF;AACA,SAAS;AACT,QAAQ,MAAM;AACd,UAAU,UAAU,CAAC,GAAG,EAAE;AAC1B,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAI,OAAO,EAAoB,QAAQ,EAA4C;AAClH,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,eAAe,EAAE;AAC3B,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC;AACjD;;AAEA,EAAE,MAAM,aAAc,GAAE,wBAAwB,CAAC,OAAO,CAAC;AACzD,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAY,EAAA,GAAI,OAAO;;AAExF,EAAE,MAAM,iBAAkB,GAAE,WAAW,EAAE,KAAK,EAAE;;AAEhD,EAAE,OAAO,SAAS,CAAC,iBAAiB,EAAE,MAAM;AAC5C;AACA,IAAI,MAAM,OAAQ,GAAE,oBAAoB,CAAI,gBAAgB,CAAC;;AAE7D,IAAI,OAAO,OAAO,CAAC,MAAM;AACzB,MAAM,MAAM,KAAA,GAAQ,eAAe,EAAE;AACrC,MAAM,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,CAAC;;AAE7C,MAAM,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU;AAChE,MAAM,MAAM,aAAa;AACzB,UAAU,IAAI,sBAAsB;AACpC,UAAU,qBAAqB,CAAC;AAChC,YAAY,UAAU;AACtB,YAAY,aAAa;AACzB,YAAY,gBAAgB;AAC5B,YAAY,KAAK;AACjB,WAAW,CAAC;;AAEZ,MAAM,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC;;AAEzC,MAAM,OAAO,oBAAoB;AACjC;AACA;AACA;AACA;AACA,QAAQ,MAAM,QAAQ,CAAC,UAAU,EAAE,MAAM,UAAU,CAAC,GAAG,EAAE,CAAC;AAC1D,QAAQ,MAAM;AACd;AACA,UAAU,MAAM,EAAE,MAAO,EAAA,GAAI,UAAU,CAAC,UAAU,CAAC;AACnD,UAAU,IAAI,UAAU,CAAC,WAAW,EAAG,KAAI,CAAC,UAAU,MAAA,KAAW,IAAI,CAAC,EAAE;AACxE,YAAY,UAAU,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,gBAAA,EAAkB,CAAC;AACxF;AACA,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,OAAO,EAA0B;AACnE,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,iBAAiB,EAAE;AAC7B,IAAI,OAAO,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC;AACzC;;AAEA,EAAE,MAAM,aAAc,GAAE,wBAAwB,CAAC,OAAO,CAAC;AACzD,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAA,EAAmB,GAAE,OAAO;;AAEpE;AACA;AACA,EAAE,MAAM,OAAA,GAAU,OAAO,CAAC;AAC1B,MAAM,CAAC,QAAQ,KAAiB,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ;AACjE,MAAM,qBAAqB;AAC3B,QAAQ,CAAC,QAAQ,KAAiB,cAAc,CAAC,gBAAgB,EAAE,QAAQ;AAC3E,QAAQ,CAAC,QAAQ,KAAiB,QAAQ,EAAE;;AAE5C,EAAE,OAAO,OAAO,CAAC,MAAM;AACvB,IAAI,MAAM,KAAA,GAAQ,eAAe,EAAE;AACnC,IAAI,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,CAAC;;AAE3C,IAAI,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU;;AAE9D,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,OAAO,IAAI,sBAAsB,EAAE;AACzC;;AAEA,IAAI,OAAO,qBAAqB,CAAC;AACjC,MAAM,UAAU;AAChB,MAAM,aAAa;AACnB,MAAM,gBAAgB;AACtB,MAAM,KAAK;AACX,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gBAAgB;AAC7B,EAAE;;AAGA;AACF,EAAE,QAAQ;AACV,KAAQ;AACR,EAAE,MAAM,OAAA,GAAU,cAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAE,uBAAuB,CAAC,OAAO,CAAC;AAC9C,EAAE,IAAI,GAAG,CAAC,aAAa,EAAE;AACzB,IAAI,OAAO,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC/C;;AAEA,EAAE,MAAM,EAAE,WAAW,EAAE,OAAQ,EAAA,GAAI,OAAO;;AAE1C,EAAE,OAAO,SAAS,CAAC,KAAA,IAAS;AAC5B,IAAI,MAAM,qBAAqB,6BAA6B,CAAC,WAAW,EAAE,OAAO,CAAC;AAClF,IAAI,KAAK,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;AACnD,IAAI,OAAO,QAAQ,EAAE;AACrB,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAI,IAAI,EAAe,QAAQ,EAA0B;AACvF,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,cAAc,EAAE;AAC1B,IAAI,OAAO,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC7C;;AAEA,EAAE,OAAO,SAAS,CAAC,KAAA,IAAS;AAC5B,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAK,IAAG,SAAS,CAAC;AAC9C,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC;AAC1B,GAAG,CAAC;AACJ;;AAEA;AACO,SAAS,eAAe,CAAI,QAAQ,EAAc;AACzD,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;;AAEtB,EAAE,IAAI,GAAG,CAAC,eAAe,EAAE;AAC3B,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC;AACxC;;AAEA,EAAE,OAAO,SAAS,CAAC,KAAA,IAAS;AAC5B;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,wBAAwB,CAAC,EAAE,CAAC,oBAAoB,GAAG,IAAK,EAAC,CAAC;AACpE,IAAI,MAAM,GAAA,GAAM,QAAQ,EAAE;AAC1B,IAAI,KAAK,CAAC,wBAAwB,CAAC,EAAE,CAAC,oBAAoB,GAAG,SAAU,EAAC,CAAC;AACzE,IAAI,OAAO,GAAG;AACd,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAI,QAAQ,EAAc;AACvD,EAAE,OAAO,SAAS,CAAC,KAAA,IAAS;AAC5B,IAAI,KAAK,CAAC,qBAAqB,CAAC;AAChC,MAAM,OAAO,EAAE,eAAe,EAAE;AAChC,MAAM,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;AAC/B,KAAK,CAAC;AACN,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,qBAAqB,EAAE,CAAC,OAAO,CAAC,CAAA,CAAA;AACA,IAAA,OAAA,cAAA,CAAA,IAAA,EAAA,QAAA,CAAA;AACA,GAAA,CAAA;AACA;;AAEA,SAAA,qBAAA,CAAA;AACA,EAAA,UAAA;AACA,EAAA,aAAA;AACA,EAAA,gBAAA;AACA,EAAA,KAAA;AACA;;AAKA,EAAA;AACA,EAAA,IAAA,CAAA,eAAA,EAAA,EAAA;AACA,IAAA,MAAA,IAAA,GAAA,IAAA,sBAAA,EAAA;;AAEA;AACA;AACA,IAAA,IAAA,gBAAA,IAAA,CAAA,UAAA,EAAA;AACA,MAAA,MAAA,GAAA,GAAA;AACA,QAAA,OAAA,EAAA,OAAA;AACA,QAAA,WAAA,EAAA,GAAA;AACA,QAAA,WAAA,EAAA,aAAA,CAAA,IAAA;AACA,QAAA,GAAA,iCAAA,CAAA,IAAA,CAAA;AACA,OAAA;AACA,MAAA,eAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA;;AAEA,IAAA,OAAA,IAAA;AACA;;AAEA,EAAA,MAAA,cAAA,GAAA,iBAAA,EAAA;;AAEA,EAAA,IAAA,IAAA;AACA,EAAA,IAAA,UAAA,IAAA,CAAA,gBAAA,EAAA;AACA,IAAA,IAAA,GAAA,eAAA,CAAA,UAAA,EAAA,KAAA,EAAA,aAAA,CAAA;AACA,IAAA,kBAAA,CAAA,UAAA,EAAA,IAAA,CAAA;AACA,GAAA,MAAA,IAAA,UAAA,EAAA;AACA;AACA,IAAA,MAAA,GAAA,GAAA,iCAAA,CAAA,UAAA,CAAA;AACA,IAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,GAAA,UAAA,CAAA,WAAA,EAAA;AACA,IAAA,MAAA,aAAA,GAAA,aAAA,CAAA,UAAA,CAAA;;AAEA,IAAA,IAAA,GAAA,cAAA;AACA,MAAA;AACA,QAAA,OAAA;AACA,QAAA,YAAA;AACA,QAAA,GAAA,aAAA;AACA,OAAA;AACA,MAAA,KAAA;AACA,MAAA,aAAA;AACA,KAAA;;AAEA,IAAA,eAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,MAAA;AACA,MAAA,OAAA;AACA,MAAA,GAAA;AACA,MAAA,YAAA;AACA,MAAA,OAAA,EAAA,aAAA;AACA,KAAA,GAAA;AACA,MAAA,GAAA,cAAA,CAAA,qBAAA,EAAA;AACA,MAAA,GAAA,KAAA,CAAA,qBAAA,EAAA;AACA,KAAA;;AAEA,IAAA,IAAA,GAAA,cAAA;AACA,MAAA;AACA,QAAA,OAAA;AACA,QAAA,YAAA;AACA,QAAA,GAAA,aAAA;AACA,OAAA;AACA,MAAA,KAAA;AACA,MAAA,aAAA;AACA,KAAA;;AAEA,IAAA,IAAA,GAAA,EAAA;AACA,MAAA,eAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA;AACA;;AAEA,EAAA,YAAA,CAAA,IAAA,CAAA;;AAEA,EAAA,uBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,cAAA,CAAA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAA,wBAAA,CAAA,OAAA,EAAA;AACA,EAAA,MAAA,GAAA,GAAA,OAAA,CAAA,YAAA,IAAA,EAAA;AACA,EAAA,MAAA,UAAA,GAAA;AACA,IAAA,YAAA,EAAA,GAAA,CAAA,UAAA;AACA,IAAA,GAAA,OAAA;AACA,GAAA;;AAEA,EAAA,IAAA,OAAA,CAAA,SAAA,EAAA;AACA,IAAA,MAAA,GAAA,GAAA,EAAA,GAAA,UAAA,EAAA;AACA,IAAA,GAAA,CAAA,cAAA,GAAA,sBAAA,CAAA,OAAA,CAAA,SAAA,CAAA;AACA,IAAA,OAAA,GAAA,CAAA,SAAA;AACA,IAAA,OAAA,GAAA;AACA;;AAEA,EAAA,OAAA,UAAA;AACA;;AAEA,SAAA,MAAA,GAAA;AACA,EAAA,MAAA,OAAA,GAAA,cAAA,EAAA;AACA,EAAA,OAAA,uBAAA,CAAA,OAAA,CAAA;AACA;;AAEA,SAAA,cAAA,CAAA,aAAA,EAAA,KAAA,EAAA,aAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAA,SAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,MAAA,EAAA,UAAA,EAAA,IAAA,EAAA;;AAEA,EAAA,MAAA,EAAA,IAAA,GAAA,EAAA,EAAA,GAAA,aAAA;;AAEA,EAAA,MAAA,uBAAA,GAAA,EAAA,cAAA,EAAA,EAAA,GAAA,aAAA,CAAA,UAAA,EAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAA,EAAA;;AAEA;AACA,EAAA,MAAA,EAAA,IAAA,CAAA,gBAAA,EAAA,uBAAA,EAAA,EAAA,QAAA,EAAA,KAAA,EAAA,CAAA;;AAEA;AACA,EAAA,MAAA,kBAAA,GAAA,uBAAA,CAAA,aAAA,IAAA,aAAA;AACA,EAAA,MAAA,eAAA,GAAA,uBAAA,CAAA,cAAA;;AAEA,EAAA,MAAA,yBAAA,GAAA,KAAA,CAAA,qBAAA,EAAA;AACA,EAAA,MAAA,CAAA,OAAA,EAAA,UAAA,EAAA,yBAAA,CAAA,GAAA,KAAA,CAAA,YAAA,EAAA,CAAA,qBAAA;AACA,IAAA;AACA;AACA,MAAA,CAAA,KAAA;AACA,MAAA,UAAA;AACA,QAAA,OAAA;AACA,QAAA;AACA,UAAA,IAAA;AACA,UAAA,aAAA,EAAA,kBAAA;AACA,UAAA,UAAA,EAAA,eAAA;AACA,UAAA,gBAAA,EAAA,eAAA,CAAA,yBAAA,CAAA,GAAA,EAAA,WAAA,CAAA;AACA,SAAA;AACA,QAAA,yBAAA,CAAA,UAAA;AACA,OAAA;;AAEA,EAAA,MAAA,QAAA,GAAA,IAAA,UAAA,CAAA;AACA,IAAA,GAAA,aAAA;AACA,IAAA,UAAA,EAAA;AACA,MAAA,CAAA,gCAAA,GAAA,QAAA;AACA,MAAA,CAAA,qCAAA;AACA,QAAA,UAAA,KAAA,SAAA,IAAA,yBAAA,GAAA,UAAA,GAAA,SAAA;AACA,MAAA,GAAA,eAAA;AACA,KAAA;AACA,IAAA,OAAA;AACA,GAAA,CAAA;;AAEA,EAAA,IAAA,CAAA,OAAA,IAAA,MAAA,EAAA;AACA,IAAA,WAAA,IAAA,MAAA,CAAA,GAAA,CAAA,gFAAA,CAAA;AACA,IAAA,MAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,aAAA,CAAA;AACA;;AAEA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,QAAA,CAAA;AACA;;AAEA,EAAA,OAAA,QAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,eAAA,CAAA,UAAA,EAAA,KAAA,EAAA,aAAA,EAAA;AACA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,GAAA,UAAA,CAAA,WAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,KAAA,CAAA,YAAA,EAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,GAAA,KAAA,GAAA,aAAA,CAAA,UAAA,CAAA;;AAEA,EAAA,MAAA,SAAA,GAAA;AACA,MAAA,IAAA,UAAA,CAAA;AACA,QAAA,GAAA,aAAA;AACA,QAAA,YAAA,EAAA,MAAA;AACA,QAAA,OAAA;AACA,QAAA,OAAA;AACA,OAAA;AACA,MAAA,IAAA,sBAAA,CAAA,EAAA,OAAA,EAAA,CAAA;;AAEA,EAAA,kBAAA,CAAA,UAAA,EAAA,SAAA,CAAA;;AAEA,EAAA,MAAA,MAAA,GAAA,SAAA,EAAA;AACA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,SAAA,CAAA;AACA;AACA,IAAA,IAAA,aAAA,CAAA,YAAA,EAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,SAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,SAAA;AACA;;AAEA,SAAA,aAAA,CAAA,KAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAA,gBAAA,CAAA,KAAA,CAAA;;AAEA,EAAA,IAAA,CAAA,IAAA,EAAA;AACA,IAAA,OAAA,SAAA;AACA;;AAEA,EAAA,MAAA,MAAA,GAAA,SAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,MAAA,GAAA,MAAA,CAAA,UAAA,EAAA,GAAA,EAAA;AACA,EAAA,IAAA,OAAA,CAAA,0BAAA,EAAA;AACA,IAAA,OAAA,WAAA,CAAA,IAAA,CAAA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA,SAAA,oBAAA,CAAA,UAAA,EAAA;AACA,EAAA,OAAA,UAAA,KAAA;AACA,MAAA,CAAA,QAAA,KAAA;AACA,QAAA,OAAA,cAAA,CAAA,UAAA,EAAA,QAAA,CAAA;AACA;AACA,MAAA,CAAA,QAAA,KAAA,QAAA,EAAA;AACA;;;;"}