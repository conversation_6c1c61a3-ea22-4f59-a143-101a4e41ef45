{"version": 3, "file": "stackStrategy.js", "sources": ["../../../src/asyncContext/stackStrategy.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { getDefaultCurrentScope, getDefaultIsolationScope } from '../defaultScopes';\nimport { Scope } from '../scope';\nimport { isThenable } from '../utils/is';\nimport { getMainCarrier, getSentryCarrier } from './../carrier';\nimport type { AsyncContextStrategy } from './types';\n\ninterface Layer {\n  client?: Client;\n  scope: Scope;\n}\n\n/**\n * This is an object that holds a stack of scopes.\n */\nexport class AsyncContextStack {\n  private readonly _stack: [Layer, ...Layer[]];\n  private _isolationScope: Scope;\n\n  public constructor(scope?: Scope, isolationScope?: Scope) {\n    let assignedScope;\n    if (!scope) {\n      assignedScope = new Scope();\n    } else {\n      assignedScope = scope;\n    }\n\n    let assignedIsolationScope;\n    if (!isolationScope) {\n      assignedIsolationScope = new Scope();\n    } else {\n      assignedIsolationScope = isolationScope;\n    }\n\n    // scope stack for domains or the process\n    this._stack = [{ scope: assignedScope }];\n    this._isolationScope = assignedIsolationScope;\n  }\n\n  /**\n   * Fork a scope for the stack.\n   */\n  public withScope<T>(callback: (scope: Scope) => T): T {\n    const scope = this._pushScope();\n\n    let maybePromiseResult: T;\n    try {\n      maybePromiseResult = callback(scope);\n    } catch (e) {\n      this._popScope();\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      // @ts-expect-error - isThenable returns the wrong type\n      return maybePromiseResult.then(\n        res => {\n          this._popScope();\n          return res;\n        },\n        e => {\n          this._popScope();\n          throw e;\n        },\n      );\n    }\n\n    this._popScope();\n    return maybePromiseResult;\n  }\n\n  /**\n   * Get the client of the stack.\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this.getStackTop().client as C;\n  }\n\n  /**\n   * Returns the scope of the top stack.\n   */\n  public getScope(): Scope {\n    return this.getStackTop().scope;\n  }\n\n  /**\n   * Get the isolation scope for the stack.\n   */\n  public getIsolationScope(): Scope {\n    return this._isolationScope;\n  }\n\n  /**\n   * Returns the topmost scope layer in the order domain > local > process.\n   */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1] as Layer;\n  }\n\n  /**\n   * Push a scope to the stack.\n   */\n  private _pushScope(): Scope {\n    // We want to clone the content of prev scope\n    const scope = this.getScope().clone();\n    this._stack.push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * Pop a scope from the stack.\n   */\n  private _popScope(): boolean {\n    if (this._stack.length <= 1) return false;\n    return !!this._stack.pop();\n  }\n}\n\n/**\n * Get the global async context stack.\n * This will be removed during the v8 cycle and is only here to make migration easier.\n */\nfunction getAsyncContextStack(): AsyncContextStack {\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n\n  return (sentry.stack = sentry.stack || new AsyncContextStack(getDefaultCurrentScope(), getDefaultIsolationScope()));\n}\n\nfunction withScope<T>(callback: (scope: Scope) => T): T {\n  return getAsyncContextStack().withScope(callback);\n}\n\nfunction withSetScope<T>(scope: Scope, callback: (scope: Scope) => T): T {\n  const stack = getAsyncContextStack() as AsyncContextStack;\n  return stack.withScope(() => {\n    stack.getStackTop().scope = scope;\n    return callback(scope);\n  });\n}\n\nfunction withIsolationScope<T>(callback: (isolationScope: Scope) => T): T {\n  return getAsyncContextStack().withScope(() => {\n    return callback(getAsyncContextStack().getIsolationScope());\n  });\n}\n\n/**\n * Get the stack-based async context strategy.\n */\nexport function getStackAsyncContextStrategy(): AsyncContextStrategy {\n  return {\n    withIsolationScope,\n    withScope,\n    withSetScope,\n    withSetIsolationScope: <T>(_isolationScope: Scope, callback: (isolationScope: Scope) => T) => {\n      return withIsolationScope(callback);\n    },\n    getCurrentScope: () => getAsyncContextStack().getScope(),\n    getIsolationScope: () => getAsyncContextStack().getIsolationScope(),\n  };\n}\n"], "names": ["scope", "<PERSON><PERSON>", "isThenable", "getMainCarrier", "getSentryCarrier", "getDefaultCurrentScope", "getDefaultIsolationScope"], "mappings": ";;;;;;;AAYA;AACA;AACA;AACO,MAAM,iBAAkB,CAAA;;AAI/B,GAAS,WAAW,CAACA,OAAK,EAAU,cAAc,EAAU;AAC5D,IAAI,IAAI,aAAa;AACrB,IAAI,IAAI,CAACA,OAAK,EAAE;AAChB,MAAM,aAAc,GAAE,IAAIC,WAAK,EAAE;AACjC,WAAW;AACX,MAAM,aAAA,GAAgBD,OAAK;AAC3B;;AAEA,IAAI,IAAI,sBAAsB;AAC9B,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,sBAAuB,GAAE,IAAIC,WAAK,EAAE;AAC1C,WAAW;AACX,MAAM,sBAAA,GAAyB,cAAc;AAC7C;;AAEA;AACA,IAAI,IAAI,CAAC,MAAA,GAAS,CAAC,EAAE,KAAK,EAAE,aAAc,EAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,eAAgB,GAAE,sBAAsB;AACjD;;AAEA;AACA;AACA;AACA,GAAS,SAAS,CAAI,QAAQ,EAA0B;AACxD,IAAI,MAAM,KAAM,GAAE,IAAI,CAAC,UAAU,EAAE;;AAEnC,IAAI,IAAI,kBAAkB;AAC1B,IAAI,IAAI;AACR,MAAM,kBAAmB,GAAE,QAAQ,CAAC,KAAK,CAAC;AAC1C,KAAM,CAAA,OAAO,CAAC,EAAE;AAChB,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,MAAM,MAAM,CAAC;AACb;;AAEA,IAAI,IAAIC,aAAU,CAAC,kBAAkB,CAAC,EAAE;AACxC;AACA,MAAM,OAAO,kBAAkB,CAAC,IAAI;AACpC,QAAQ,OAAO;AACf,UAAU,IAAI,CAAC,SAAS,EAAE;AAC1B,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,KAAK;AACb,UAAU,IAAI,CAAC,SAAS,EAAE;AAC1B,UAAU,MAAM,CAAC;AACjB,SAAS;AACT,OAAO;AACP;;AAEA,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,kBAAkB;AAC7B;;AAEA;AACA;AACA;AACA,GAAS,SAAS,GAAoC;AACtD,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAO;AACrC;;AAEA;AACA;AACA;AACA,GAAS,QAAQ,GAAU;AAC3B,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK;AACnC;;AAEA;AACA;AACA;AACA,GAAS,iBAAiB,GAAU;AACpC,IAAI,OAAO,IAAI,CAAC,eAAe;AAC/B;;AAEA;AACA;AACA;AACA,GAAS,WAAW,GAAU;AAC9B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAA,GAAS,CAAC,CAAE;AAC/C;;AAEA;AACA;AACA;AACA,GAAU,UAAU,GAAU;AAC9B;AACA,IAAI,MAAM,KAAM,GAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE;AACzC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACrB,MAAM,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;AAC9B,MAAM,KAAK;AACX,KAAK,CAAC;AACN,IAAI,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,GAAU,SAAS,GAAY;AAC/B,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAA,IAAU,CAAC,EAAE,OAAO,KAAK;AAC7C,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;AAC9B;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,GAAsB;AACnD,EAAE,MAAM,QAAA,GAAWC,sBAAc,EAAE;AACnC,EAAE,MAAM,MAAO,GAAEC,wBAAgB,CAAC,QAAQ,CAAC;;AAE3C,EAAE,QAAQ,MAAM,CAAC,QAAQ,MAAM,CAAC,KAAM,IAAG,IAAI,iBAAiB,CAACC,oCAAsB,EAAE,EAAEC,sCAAwB,EAAE,CAAC;AACpH;;AAEA,SAAS,SAAS,CAAI,QAAQ,EAA0B;AACxD,EAAE,OAAO,oBAAoB,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;AACnD;;AAEA,SAAS,YAAY,CAAI,KAAK,EAAS,QAAQ,EAA0B;AACzE,EAAE,MAAM,KAAA,GAAQ,oBAAoB,EAAG;AACvC,EAAE,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM;AAC/B,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC,KAAA,GAAQ,KAAK;AACrC,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC;AAC1B,GAAG,CAAC;AACJ;;AAEA,SAAS,kBAAkB,CAAI,QAAQ,EAAmC;AAC1E,EAAE,OAAO,oBAAoB,EAAE,CAAC,SAAS,CAAC,MAAM;AAChD,IAAI,OAAO,QAAQ,CAAC,oBAAoB,EAAE,CAAC,iBAAiB,EAAE,CAAC;AAC/D,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAAS,4BAA4B,GAAyB;AACrE,EAAE,OAAO;AACT,IAAI,kBAAkB;AACtB,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,qBAAqB,EAAE,CAAI,eAAe,EAAS,QAAQ,KAAmC;AAClG,MAAM,OAAO,kBAAkB,CAAC,QAAQ,CAAC;AACzC,KAAK;AACL,IAAI,eAAe,EAAE,MAAM,oBAAoB,EAAE,CAAC,QAAQ,EAAE;AAC5D,IAAI,iBAAiB,EAAE,MAAM,oBAAoB,EAAE,CAAC,iBAAiB,EAAE;AACvE,GAAG;AACH;;;;;"}