{"version": 3, "file": "error.js", "sources": ["../../../src/utils/error.ts"], "sourcesContent": ["import type { ConsoleLevel } from '../types-hoist/instrument';\n\n/**\n * An error emitted by Sentry SDKs and related utilities.\n * @deprecated This class is no longer used and will be removed in a future version. Use `Error` instead.\n */\nexport class SentryError extends Error {\n  public logLevel: ConsoleLevel;\n\n  public constructor(\n    public message: string,\n    logLevel: ConsoleLevel = 'warn',\n  ) {\n    super(message);\n\n    this.logLevel = logLevel;\n  }\n}\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AACA;AACO,MAAM,WAAY,SAAQ,KAAM,CAAA;;AAGvC,GAAS,WAAW;AACpB,KAAW,OAAO;AAClB,IAAI,QAAQ,GAAiB,MAAM;AACnC,IAAI;AACJ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA,IAAA,CAAA,OAAA,GAAA,OAAA;AAElB,IAAI,IAAI,CAAC,QAAS,GAAE,QAAQ;AAC5B;AACA;;;;"}