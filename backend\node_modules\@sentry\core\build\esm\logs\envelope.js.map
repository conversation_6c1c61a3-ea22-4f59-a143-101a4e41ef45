{"version": 3, "file": "envelope.js", "sources": ["../../../src/logs/envelope.ts"], "sourcesContent": ["import type { DsnComponents } from '../types-hoist/dsn';\nimport type { LogContainerItem, LogEnvelope } from '../types-hoist/envelope';\nimport type { SerializedLog } from '../types-hoist/log';\nimport type { SdkMetadata } from '../types-hoist/sdkmetadata';\nimport { dsnToString } from '../utils/dsn';\nimport { createEnvelope } from '../utils/envelope';\n\n/**\n * Creates a log container envelope item for a list of logs.\n *\n * @param items - The logs to include in the envelope.\n * @returns The created log container envelope item.\n */\nexport function createLogContainerEnvelopeItem(items: Array<SerializedLog>): LogContainerItem {\n  return [\n    {\n      type: 'log',\n      item_count: items.length,\n      content_type: 'application/vnd.sentry.items.log+json',\n    },\n    {\n      items,\n    },\n  ];\n}\n\n/**\n * Creates an envelope for a list of logs.\n *\n * Logs from multiple traces can be included in the same envelope.\n *\n * @param logs - The logs to include in the envelope.\n * @param metadata - The metadata to include in the envelope.\n * @param tunnel - The tunnel to include in the envelope.\n * @param dsn - The DSN to include in the envelope.\n * @returns The created envelope.\n */\nexport function createLogEnvelope(\n  logs: Array<SerializedLog>,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n  dsn?: DsnComponents,\n): LogEnvelope {\n  const headers: LogEnvelope[0] = {};\n\n  if (metadata?.sdk) {\n    headers.sdk = {\n      name: metadata.sdk.name,\n      version: metadata.sdk.version,\n    };\n  }\n\n  if (!!tunnel && !!dsn) {\n    headers.dsn = dsnToString(dsn);\n  }\n\n  return createEnvelope<LogEnvelope>(headers, [createLogContainerEnvelopeItem(logs)]);\n}\n"], "names": [], "mappings": ";;;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,8BAA8B,CAAC,KAAK,EAA0C;AAC9F,EAAE,OAAO;AACT,IAAI;AACJ,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,UAAU,EAAE,KAAK,CAAC,MAAM;AAC9B,MAAM,YAAY,EAAE,uCAAuC;AAC3D,KAAK;AACL,IAAI;AACJ,MAAM,KAAK;AACX,KAAK;AACL,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB;AACjC,EAAE,IAAI;AACN,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,GAAG;AACL,EAAe;AACf,EAAE,MAAM,OAAO,GAAmB,EAAE;;AAEpC,EAAE,IAAI,QAAQ,EAAE,GAAG,EAAE;AACrB,IAAI,OAAO,CAAC,GAAA,GAAM;AAClB,MAAM,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI;AAC7B,MAAM,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO;AACnC,KAAK;AACL;;AAEA,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE;AACzB,IAAI,OAAO,CAAC,GAAA,GAAM,WAAW,CAAC,GAAG,CAAC;AAClC;;AAEA,EAAE,OAAO,cAAc,CAAc,OAAO,EAAE,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC,CAAC;AACrF;;;;"}