const fs = require('fs');
const mongoose = require('mongoose');
require('dotenv').config();

const logFile = './test-debug.log';

function log(message) {
  fs.appendFileSync(logFile, `${new Date().toISOString()} - ${message}\n`);
}

log('MINIMAL TEST: Starting test suite');

describe('Minimal Mongoose Test', () => {
  beforeAll(async () => {
    try {
      log('MINIMAL TEST: Connecting to MongoDB');
      await mongoose.connect(process.env.MONGODB_URI, {
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 10000,
        connectTimeoutMS: 10000
      });
      log(`MINIMAL TEST: Connection state: ${mongoose.connection.readyState}`);
      log('MINIMAL TEST: Connected successfully');
    } catch (err) {
      log(`MINIMAL TEST: Connection failed: ${err}`);
      throw err;
    }
  });

  afterAll(async () => {
    try {
      log('MINIMAL TEST: Disconnecting from MongoDB');
      await mongoose.disconnect();
      log('MINIMAL TEST: Disconnected successfully');
    } catch (err) {
      log(`MINIMAL TEST: Disconnection failed: ${err}`);
    }
  });

  it('should simply pass', () => {
    log('MINIMAL TEST: Running basic test');
    expect(true).toBe(true);
  });

  it('should have active connection', () => {
    log('MINIMAL TEST: Checking connection state');
    log(`Current connection state: ${mongoose.connection.readyState}`);
    expect(mongoose.connection.readyState).toBe(1);
  });
});
