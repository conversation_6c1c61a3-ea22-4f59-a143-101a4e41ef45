{"version": 3, "file": "breadcrumbs.js", "sources": ["../../src/breadcrumbs.ts"], "sourcesContent": ["import { getClient, getIsolationScope } from './currentScopes';\nimport type { Breadcrumb, BreadcrumbHint } from './types-hoist/breadcrumb';\nimport { consoleSandbox } from './utils/logger';\nimport { dateTimestampInSeconds } from './utils/time';\n\n/**\n * Default maximum number of breadcrumbs added to an event. Can be overwritten\n * with {@link Options.maxBreadcrumbs}.\n */\nconst DEFAULT_BREADCRUMBS = 100;\n\n/**\n * Records a new breadcrumb which will be attached to future events.\n *\n * Breadcrumbs will be added to subsequent events to provide more context on\n * user's actions prior to an error or crash.\n */\nexport function addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void {\n  const client = getClient();\n  const isolationScope = getIsolationScope();\n\n  if (!client) return;\n\n  const { beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS } = client.getOptions();\n\n  if (maxBreadcrumbs <= 0) return;\n\n  const timestamp = dateTimestampInSeconds();\n  const mergedBreadcrumb = { timestamp, ...breadcrumb };\n  const finalBreadcrumb = beforeBreadcrumb\n    ? (consoleSandbox(() => beforeBreadcrumb(mergedBreadcrumb, hint)) as Breadcrumb | null)\n    : mergedBreadcrumb;\n\n  if (finalBreadcrumb === null) return;\n\n  if (client.emit) {\n    client.emit('beforeAddBreadcrumb', finalBreadcrumb, hint);\n  }\n\n  isolationScope.addBreadcrumb(finalBreadcrumb, maxBreadcrumbs);\n}\n"], "names": [], "mappings": ";;;;AAKA;AACA;AACA;AACA;AACA,MAAM,mBAAA,GAAsB,GAAG;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,UAAU,EAAc,IAAI,EAAyB;AACnF,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;AAC5B,EAAE,MAAM,cAAA,GAAiB,iBAAiB,EAAE;;AAE5C,EAAE,IAAI,CAAC,MAAM,EAAE;;AAEf,EAAE,MAAM,EAAE,gBAAiB,GAAE,IAAI,EAAE,cAAA,GAAiB,mBAAA,KAAwB,MAAM,CAAC,UAAU,EAAE;;AAE/F,EAAE,IAAI,cAAA,IAAkB,CAAC,EAAE;;AAE3B,EAAE,MAAM,SAAA,GAAY,sBAAsB,EAAE;AAC5C,EAAE,MAAM,mBAAmB,EAAE,SAAS,EAAE,GAAG,YAAY;AACvD,EAAE,MAAM,kBAAkB;AAC1B,OAAO,cAAc,CAAC,MAAM,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAE;AACtE,MAAM,gBAAgB;;AAEtB,EAAE,IAAI,eAAA,KAAoB,IAAI,EAAE;;AAEhC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;AACnB,IAAI,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,eAAe,EAAE,IAAI,CAAC;AAC7D;;AAEA,EAAE,cAAc,CAAC,aAAa,CAAC,eAAe,EAAE,cAAc,CAAC;AAC/D;;;;"}