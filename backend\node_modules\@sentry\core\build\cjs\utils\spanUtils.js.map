{"version": 3, "file": "spanUtils.js", "sources": ["../../../src/utils/spanUtils.ts"], "sourcesContent": ["import { getAsyncContextStrategy } from '../asyncContext';\nimport { getMainCarrier } from '../carrier';\nimport { getCurrentScope } from '../currentScopes';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { SentrySpan } from '../tracing/sentrySpan';\nimport { SPAN_STATUS_OK, SPAN_STATUS_UNSET } from '../tracing/spanstatus';\nimport { getCapturedScopesOnSpan } from '../tracing/utils';\nimport type { TraceContext } from '../types-hoist/context';\nimport type { SpanLink, SpanLinkJSON } from '../types-hoist/link';\nimport type { Span, SpanAttributes, SpanJSON, SpanOrigin, SpanTimeInput } from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\nimport { consoleSandbox } from '../utils/logger';\nimport { addNonEnumerableProperty } from '../utils/object';\nimport { generateSpanId } from '../utils/propagationContext';\nimport { timestampInSeconds } from '../utils/time';\nimport { generateSentryTraceHeader } from '../utils/tracing';\nimport { _getSpanForScope } from './spanOnScope';\n\n// These are aligned with OpenTelemetry trace flags\nexport const TRACE_FLAG_NONE = 0x0;\nexport const TRACE_FLAG_SAMPLED = 0x1;\n\nlet hasShownSpanDropWarning = false;\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in an event.\n * By default, this will only include trace_id, span_id & parent_span_id.\n * If `includeAllData` is true, it will also include data, op, status & origin.\n */\nexport function spanToTransactionTraceContext(span: Span): TraceContext {\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n  const { data, op, parent_span_id, status, origin, links } = spanToJSON(span);\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n    data,\n    op,\n    status,\n    origin,\n    links,\n  };\n}\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in a non-transaction event.\n */\nexport function spanToTraceContext(span: Span): TraceContext {\n  const { spanId, traceId: trace_id, isRemote } = span.spanContext();\n\n  // If the span is remote, we use a random/virtual span as span_id to the trace context,\n  // and the remote span as parent_span_id\n  const parent_span_id = isRemote ? spanId : spanToJSON(span).parent_span_id;\n  const scope = getCapturedScopesOnSpan(span).scope;\n\n  const span_id = isRemote ? scope?.getPropagationContext().propagationSpanId || generateSpanId() : spanId;\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n  };\n}\n\n/**\n * Convert a Span to a Sentry trace header.\n */\nexport function spanToTraceHeader(span: Span): string {\n  const { traceId, spanId } = span.spanContext();\n  const sampled = spanIsSampled(span);\n  return generateSentryTraceHeader(traceId, spanId, sampled);\n}\n\n/**\n *  Converts the span links array to a flattened version to be sent within an envelope.\n *\n *  If the links array is empty, it returns `undefined` so the empty value can be dropped before it's sent.\n */\nexport function convertSpanLinksForEnvelope(links?: SpanLink[]): SpanLinkJSON[] | undefined {\n  if (links && links.length > 0) {\n    return links.map(({ context: { spanId, traceId, traceFlags, ...restContext }, attributes }) => ({\n      span_id: spanId,\n      trace_id: traceId,\n      sampled: traceFlags === TRACE_FLAG_SAMPLED,\n      attributes,\n      ...restContext,\n    }));\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Convert a span time input into a timestamp in seconds.\n */\nexport function spanTimeInputToSeconds(input: SpanTimeInput | undefined): number {\n  if (typeof input === 'number') {\n    return ensureTimestampInSeconds(input);\n  }\n\n  if (Array.isArray(input)) {\n    // See {@link HrTime} for the array-based time format\n    return input[0] + input[1] / 1e9;\n  }\n\n  if (input instanceof Date) {\n    return ensureTimestampInSeconds(input.getTime());\n  }\n\n  return timestampInSeconds();\n}\n\n/**\n * Converts a timestamp to second, if it was in milliseconds, or keeps it as second.\n */\nfunction ensureTimestampInSeconds(timestamp: number): number {\n  const isMs = timestamp > 9999999999;\n  return isMs ? timestamp / 1000 : timestamp;\n}\n\n/**\n * Convert a span to a JSON representation.\n */\n// Note: Because of this, we currently have a circular type dependency (which we opted out of in package.json).\n// This is not avoidable as we need `spanToJSON` in `spanUtils.ts`, which in turn is needed by `span.ts` for backwards compatibility.\n// And `spanToJSON` needs the Span class from `span.ts` to check here.\nexport function spanToJSON(span: Span): SpanJSON {\n  if (spanIsSentrySpan(span)) {\n    return span.getSpanJSON();\n  }\n\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n\n  // Handle a span from @opentelemetry/sdk-base-trace's `Span` class\n  if (spanIsOpenTelemetrySdkTraceBaseSpan(span)) {\n    const { attributes, startTime, name, endTime, status, links } = span;\n\n    // In preparation for the next major of OpenTelemetry, we want to support\n    // looking up the parent span id according to the new API\n    // In OTel v1, the parent span id is accessed as `parentSpanId`\n    // In OTel v2, the parent span id is accessed as `spanId` on the `parentSpanContext`\n    const parentSpanId =\n      'parentSpanId' in span\n        ? span.parentSpanId\n        : 'parentSpanContext' in span\n          ? (span.parentSpanContext as { spanId?: string } | undefined)?.spanId\n          : undefined;\n\n    return {\n      span_id,\n      trace_id,\n      data: attributes,\n      description: name,\n      parent_span_id: parentSpanId,\n      start_timestamp: spanTimeInputToSeconds(startTime),\n      // This is [0,0] by default in OTEL, in which case we want to interpret this as no end time\n      timestamp: spanTimeInputToSeconds(endTime) || undefined,\n      status: getStatusMessage(status),\n      op: attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP],\n      origin: attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined,\n      links: convertSpanLinksForEnvelope(links),\n    };\n  }\n\n  // Finally, at least we have `spanContext()`....\n  // This should not actually happen in reality, but we need to handle it for type safety.\n  return {\n    span_id,\n    trace_id,\n    start_timestamp: 0,\n    data: {},\n  };\n}\n\nfunction spanIsOpenTelemetrySdkTraceBaseSpan(span: Span): span is OpenTelemetrySdkTraceBaseSpan {\n  const castSpan = span as Partial<OpenTelemetrySdkTraceBaseSpan>;\n  return !!castSpan.attributes && !!castSpan.startTime && !!castSpan.name && !!castSpan.endTime && !!castSpan.status;\n}\n\n/** Exported only for tests. */\nexport interface OpenTelemetrySdkTraceBaseSpan extends Span {\n  attributes: SpanAttributes;\n  startTime: SpanTimeInput;\n  name: string;\n  status: SpanStatus;\n  endTime: SpanTimeInput;\n  parentSpanId?: string;\n  links?: SpanLink[];\n}\n\n/**\n * Sadly, due to circular dependency checks we cannot actually import the Span class here and check for instanceof.\n * :( So instead we approximate this by checking if it has the `getSpanJSON` method.\n */\nfunction spanIsSentrySpan(span: Span): span is SentrySpan {\n  return typeof (span as SentrySpan).getSpanJSON === 'function';\n}\n\n/**\n * Returns true if a span is sampled.\n * In most cases, you should just use `span.isRecording()` instead.\n * However, this has a slightly different semantic, as it also returns false if the span is finished.\n * So in the case where this distinction is important, use this method.\n */\nexport function spanIsSampled(span: Span): boolean {\n  // We align our trace flags with the ones OpenTelemetry use\n  // So we also check for sampled the same way they do.\n  const { traceFlags } = span.spanContext();\n  return traceFlags === TRACE_FLAG_SAMPLED;\n}\n\n/** Get the status message to use for a JSON representation of a span. */\nexport function getStatusMessage(status: SpanStatus | undefined): string | undefined {\n  if (!status || status.code === SPAN_STATUS_UNSET) {\n    return undefined;\n  }\n\n  if (status.code === SPAN_STATUS_OK) {\n    return 'ok';\n  }\n\n  return status.message || 'unknown_error';\n}\n\nconst CHILD_SPANS_FIELD = '_sentryChildSpans';\nconst ROOT_SPAN_FIELD = '_sentryRootSpan';\n\ntype SpanWithPotentialChildren = Span & {\n  [CHILD_SPANS_FIELD]?: Set<Span>;\n  [ROOT_SPAN_FIELD]?: Span;\n};\n\n/**\n * Adds an opaque child span reference to a span.\n */\nexport function addChildSpanToSpan(span: SpanWithPotentialChildren, childSpan: Span): void {\n  // We store the root span reference on the child span\n  // We need this for `getRootSpan()` to work\n  const rootSpan = span[ROOT_SPAN_FIELD] || span;\n  addNonEnumerableProperty(childSpan as SpanWithPotentialChildren, ROOT_SPAN_FIELD, rootSpan);\n\n  // We store a list of child spans on the parent span\n  // We need this for `getSpanDescendants()` to work\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].add(childSpan);\n  } else {\n    addNonEnumerableProperty(span, CHILD_SPANS_FIELD, new Set([childSpan]));\n  }\n}\n\n/** This is only used internally by Idle Spans. */\nexport function removeChildSpanFromSpan(span: SpanWithPotentialChildren, childSpan: Span): void {\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].delete(childSpan);\n  }\n}\n\n/**\n * Returns an array of the given span and all of its descendants.\n */\nexport function getSpanDescendants(span: SpanWithPotentialChildren): Span[] {\n  const resultSet = new Set<Span>();\n\n  function addSpanChildren(span: SpanWithPotentialChildren): void {\n    // This exit condition is required to not infinitely loop in case of a circular dependency.\n    if (resultSet.has(span)) {\n      return;\n      // We want to ignore unsampled spans (e.g. non recording spans)\n    } else if (spanIsSampled(span)) {\n      resultSet.add(span);\n      const childSpans = span[CHILD_SPANS_FIELD] ? Array.from(span[CHILD_SPANS_FIELD]) : [];\n      for (const childSpan of childSpans) {\n        addSpanChildren(childSpan);\n      }\n    }\n  }\n\n  addSpanChildren(span);\n\n  return Array.from(resultSet);\n}\n\n/**\n * Returns the root span of a given span.\n */\nexport function getRootSpan(span: SpanWithPotentialChildren): Span {\n  return span[ROOT_SPAN_FIELD] || span;\n}\n\n/**\n * Returns the currently active span.\n */\nexport function getActiveSpan(): Span | undefined {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.getActiveSpan) {\n    return acs.getActiveSpan();\n  }\n\n  return _getSpanForScope(getCurrentScope());\n}\n\n/**\n * Logs a warning once if `beforeSendSpan` is used to drop spans.\n */\nexport function showSpanDropWarning(): void {\n  if (!hasShownSpanDropWarning) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.',\n      );\n    });\n    hasShownSpanDropWarning = true;\n  }\n}\n\n/**\n * Updates the name of the given span and ensures that the span name is not\n * overwritten by the Sentry SDK.\n *\n * Use this function instead of `span.updateName()` if you want to make sure that\n * your name is kept. For some spans, for example root `http.server` spans the\n * Sentry SDK would otherwise overwrite the span name with a high-quality name\n * it infers when the span ends.\n *\n * Use this function in server code or when your span is started on the server\n * and on the client (browser). If you only update a span name on the client,\n * you can also use `span.updateName()` the SDK does not overwrite the name.\n *\n * @param span - The span to update the name of.\n * @param name - The name to set on the span.\n */\nexport function updateSpanName(span: Span, name: string): void {\n  span.updateName(name);\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'custom',\n    [SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME]: name,\n  });\n}\n"], "names": ["getCapturedScopesOnSpan", "generateSpanId", "generateSentryTraceHeader", "timestampInSeconds", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "SPAN_STATUS_UNSET", "SPAN_STATUS_OK", "addNonEnumerableProperty", "carrier", "getMainCarrier", "getAsyncContextStrategy", "_getSpanForScope", "getCurrentScope", "consoleSandbox", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME"], "mappings": ";;;;;;;;;;;;;;;AAuBA;AACO,MAAM,eAAgB,GAAE;AACxB,MAAM,kBAAmB,GAAE;;AAElC,IAAI,uBAAA,GAA0B,KAAK;;AAEnC;AACA;AACA;AACA;AACA;AACO,SAAS,6BAA6B,CAAC,IAAI,EAAsB;AACxE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAA,KAAa,IAAI,CAAC,WAAW,EAAE;AACnE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,KAAM,EAAA,GAAI,UAAU,CAAC,IAAI,CAAC;;AAE9E,EAAE,OAAO;AACT,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,IAAI;AACR,IAAI,EAAE;AACN,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,KAAK;AACT,GAAG;AACH;;AAEA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,IAAI,EAAsB;AAC7D,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAA,KAAa,IAAI,CAAC,WAAW,EAAE;;AAEpE;AACA;AACA,EAAE,MAAM,cAAA,GAAiB,QAAA,GAAW,MAAA,GAAS,UAAU,CAAC,IAAI,CAAC,CAAC,cAAc;AAC5E,EAAE,MAAM,QAAQA,6BAAuB,CAAC,IAAI,CAAC,CAAC,KAAK;;AAEnD,EAAE,MAAM,OAAQ,GAAE,QAAS,GAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,iBAAkB,IAAGC,iCAAc,EAAC,GAAI,MAAM;;AAE1G,EAAE,OAAO;AACT,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,GAAG;AACH;;AAEA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,IAAI,EAAgB;AACtD,EAAE,MAAM,EAAE,OAAO,EAAE,MAAA,EAAS,GAAE,IAAI,CAAC,WAAW,EAAE;AAChD,EAAE,MAAM,OAAQ,GAAE,aAAa,CAAC,IAAI,CAAC;AACrC,EAAE,OAAOC,iCAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B,CAAC,KAAK,EAA2C;AAC5F,EAAE,IAAI,KAAM,IAAG,KAAK,CAAC,MAAA,GAAS,CAAC,EAAE;AACjC,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,WAAY,EAAC,EAAE,UAAW,EAAC,MAAM;AACpG,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,OAAO,EAAE,UAAW,KAAI,kBAAkB;AAChD,MAAM,UAAU;AAChB,MAAM,GAAG,WAAW;AACpB,KAAK,CAAC,CAAC;AACP,SAAS;AACT,IAAI,OAAO,SAAS;AACpB;AACA;;AAEA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,KAAK,EAAqC;AACjF,EAAE,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;AACjC,IAAI,OAAO,wBAAwB,CAAC,KAAK,CAAC;AAC1C;;AAEA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B;AACA,IAAI,OAAO,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAE,GAAE,GAAG;AACpC;;AAEA,EAAE,IAAI,KAAM,YAAW,IAAI,EAAE;AAC7B,IAAI,OAAO,wBAAwB,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACpD;;AAEA,EAAE,OAAOC,uBAAkB,EAAE;AAC7B;;AAEA;AACA;AACA;AACA,SAAS,wBAAwB,CAAC,SAAS,EAAkB;AAC7D,EAAE,MAAM,IAAA,GAAO,SAAA,GAAY,UAAU;AACrC,EAAE,OAAO,IAAK,GAAE,YAAY,IAAA,GAAO,SAAS;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,IAAI,EAAkB;AACjD,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;AAC7B;;AAEA,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAA,KAAa,IAAI,CAAC,WAAW,EAAE;;AAEnE;AACA,EAAE,IAAI,mCAAmC,CAAC,IAAI,CAAC,EAAE;AACjD,IAAI,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAM,EAAA,GAAI,IAAI;;AAExE;AACA;AACA;AACA;AACA,IAAI,MAAM,YAAa;AACvB,MAAM,kBAAkB;AACxB,UAAU,IAAI,CAAC;AACf,UAAU,uBAAuB;AACjC,YAAY,CAAC,IAAI,CAAC,iBAAA,IAAuD;AACzE,YAAY,SAAS;;AAErB,IAAI,OAAO;AACX,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,eAAe,EAAE,sBAAsB,CAAC,SAAS,CAAC;AACxD;AACA,MAAM,SAAS,EAAE,sBAAsB,CAAC,OAAO,CAAA,IAAK,SAAS;AAC7D,MAAM,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC;AACtC,MAAM,EAAE,EAAE,UAAU,CAACC,+CAA4B,CAAC;AAClD,MAAM,MAAM,EAAE,UAAU,CAACC,mDAAgC,CAAE;AAC3D,MAAM,KAAK,EAAE,2BAA2B,CAAC,KAAK,CAAC;AAC/C,KAAK;AACL;;AAEA;AACA;AACA,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,eAAe,EAAE,CAAC;AACtB,IAAI,IAAI,EAAE,EAAE;AACZ,GAAG;AACH;;AAEA,SAAS,mCAAmC,CAAC,IAAI,EAA+C;AAChG,EAAE,MAAM,QAAS,GAAE,IAAK;AACxB,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAA,IAAc,CAAC,CAAC,QAAQ,CAAC,SAAA,IAAa,CAAC,CAAC,QAAQ,CAAC,IAAA,IAAQ,CAAC,CAAC,QAAQ,CAAC,OAAA,IAAW,CAAC,CAAC,QAAQ,CAAC,MAAM;AACpH;;AAEA;;AAWA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,IAAI,EAA4B;AAC1D,EAAE,OAAO,OAAO,CAAC,IAAA,GAAoB,WAAA,KAAgB,UAAU;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,IAAI,EAAiB;AACnD;AACA;AACA,EAAE,MAAM,EAAE,UAAW,EAAA,GAAI,IAAI,CAAC,WAAW,EAAE;AAC3C,EAAE,OAAO,UAAW,KAAI,kBAAkB;AAC1C;;AAEA;AACO,SAAS,gBAAgB,CAAC,MAAM,EAA8C;AACrF,EAAE,IAAI,CAAC,MAAO,IAAG,MAAM,CAAC,IAAA,KAASC,4BAAiB,EAAE;AACpD,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,IAAI,MAAM,CAAC,IAAK,KAAIC,yBAAc,EAAE;AACtC,IAAI,OAAO,IAAI;AACf;;AAEA,EAAE,OAAO,MAAM,CAAC,OAAA,IAAW,eAAe;AAC1C;;AAEA,MAAM,iBAAA,GAAoB,mBAAmB;AAC7C,MAAM,eAAA,GAAkB,iBAAiB;;AAOzC;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,IAAI,EAA6B,SAAS,EAAc;AAC3F;AACA;AACA,EAAE,MAAM,WAAW,IAAI,CAAC,eAAe,CAAA,IAAK,IAAI;AAChD,EAAEC,+BAAwB,CAAC,SAAA,GAAwC,eAAe,EAAE,QAAQ,CAAC;;AAE7F;AACA;AACA,EAAE,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE;AAC/B,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;AAC1C,SAAS;AACT,IAAIA,+BAAwB,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3E;AACA;;AAEA;AACO,SAAS,uBAAuB,CAAC,IAAI,EAA6B,SAAS,EAAc;AAChG,EAAE,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE;AAC/B,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AAC7C;AACA;;AAEA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,IAAI,EAAqC;AAC5E,EAAE,MAAM,SAAU,GAAE,IAAI,GAAG,EAAQ;;AAEnC,EAAE,SAAS,eAAe,CAAC,IAAI,EAAmC;AAClE;AACA,IAAI,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7B,MAAM;AACN;AACA,KAAI,MAAO,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;AACpC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AACzB,MAAM,MAAM,UAAW,GAAE,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAE,GAAE,EAAE;AAC3F,MAAM,KAAK,MAAM,SAAU,IAAG,UAAU,EAAE;AAC1C,QAAQ,eAAe,CAAC,SAAS,CAAC;AAClC;AACA;AACA;;AAEA,EAAE,eAAe,CAAC,IAAI,CAAC;;AAEvB,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;AAC9B;;AAEA;AACA;AACA;AACO,SAAS,WAAW,CAAC,IAAI,EAAmC;AACnE,EAAE,OAAO,IAAI,CAAC,eAAe,CAAA,IAAK,IAAI;AACtC;;AAEA;AACA;AACA;AACO,SAAS,aAAa,GAAqB;AAClD,EAAE,MAAMC,SAAA,GAAUC,sBAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAEC,6BAAuB,CAACF,SAAO,CAAC;AAC9C,EAAE,IAAI,GAAG,CAAC,aAAa,EAAE;AACzB,IAAI,OAAO,GAAG,CAAC,aAAa,EAAE;AAC9B;;AAEA,EAAE,OAAOG,4BAAgB,CAACC,6BAAe,EAAE,CAAC;AAC5C;;AAEA;AACA;AACA;AACO,SAAS,mBAAmB,GAAS;AAC5C,EAAE,IAAI,CAAC,uBAAuB,EAAE;AAChC,IAAIC,qBAAc,CAAC,MAAM;AACzB;AACA,MAAM,OAAO,CAAC,IAAI;AAClB,QAAQ,qIAAqI;AAC7I,OAAO;AACP,KAAK,CAAC;AACN,IAAI,uBAAA,GAA0B,IAAI;AAClC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,IAAI,EAAQ,IAAI,EAAgB;AAC/D,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACvB,EAAE,IAAI,CAAC,aAAa,CAAC;AACrB,IAAI,CAACC,mDAAgC,GAAG,QAAQ;AAChD,IAAI,CAACC,6DAA0C,GAAG,IAAI;AACtD,GAAG,CAAC;AACJ;;;;;;;;;;;;;;;;;;;;"}