{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../src/client.ts"], "names": [], "mappings": "AAMA,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAEtD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAMrC,OAAO,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AACnH,OAAO,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAC3E,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAC1D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AACvD,OAAO,KAAK,EAAE,sBAAsB,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAC/E,OAAO,KAAK,EAAc,KAAK,EAAE,SAAS,EAAoB,MAAM,qBAAqB,CAAC;AAC1F,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAC5D,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,KAAK,EAAE,OAAO,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AACxE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAC5D,OAAO,KAAK,EAAE,IAAI,EAAE,cAAc,EAAE,eAAe,EAAY,MAAM,oBAAoB,CAAC;AAC1F,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,KAAK,EAAE,SAAS,EAAE,4BAA4B,EAAE,MAAM,yBAAyB,CAAC;AAqDvF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,8BAAsB,MAAM,CAAC,CAAC,SAAS,aAAa,GAAG,aAAa;IAClE,iCAAiC;IACjC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAE/B,2FAA2F;IAC3F,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC;IAExC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC;IAE1C,oCAAoC;IACpC,SAAS,CAAC,aAAa,EAAE,gBAAgB,CAAC;IAE1C,sCAAsC;IACtC,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC;IAEjC,SAAS,CAAC,gBAAgB,EAAE,cAAc,EAAE,CAAC;IAE7C,uBAAuB;IACvB,OAAO,CAAC,SAAS,CAA4B;IAG7C,OAAO,CAAC,MAAM,CAA6B;IAE3C;;;;OAIG;IACH,SAAS,aAAa,OAAO,EAAE,CAAC;IA6BhC;;;;OAIG;IACI,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM;IAuBpF;;;;OAIG;IACI,cAAc,CACnB,OAAO,EAAE,mBAAmB,EAC5B,KAAK,CAAC,EAAE,aAAa,EACrB,IAAI,CAAC,EAAE,SAAS,EAChB,YAAY,CAAC,EAAE,KAAK,GACnB,MAAM;IAiBT;;;;OAIG;IACI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE,KAAK,GAAG,MAAM;IAyBjF;;OAEG;IACI,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAM7C;;;;;;;;OAQG;IACI,cAAc,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM;IAE9F;;OAEG;IACI,MAAM,IAAI,aAAa,GAAG,SAAS;IAI1C;;OAEG;IACI,UAAU,IAAI,CAAC;IAItB;;;OAGG;IACI,cAAc,IAAI,WAAW,GAAG,SAAS;IAIhD;;;OAGG;IACI,YAAY,IAAI,SAAS,GAAG,SAAS;IAI5C;;;;;;;OAOG;IACI,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAYpD;;;;;;;OAOG;IACI,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAQpD;;OAEG;IACI,kBAAkB,IAAI,cAAc,EAAE;IAI7C;;OAEG;IACI,iBAAiB,CAAC,cAAc,EAAE,cAAc,GAAG,IAAI;IAI9D;;;OAGG;IACI,IAAI,IAAI,IAAI;IAcnB;;;;OAIG;IACI,oBAAoB,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW,EAAE,eAAe,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS;IAIxG;;;;;;OAMG;IACI,cAAc,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;IAWrD;;OAEG;IACI,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,SAAc,GAAG,IAAI;IAe1D;;OAEG;IACI,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,iBAAiB,GAAG,IAAI;IA8B9D;;OAEG;IACI,kBAAkB,CAAC,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,GAAE,MAAU,GAAG,IAAI;IAenG;;;;OAIG;IACI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI;IAExE;;;;OAIG;IACI,EAAE,CACP,IAAI,EAAE,gBAAgB,EACtB,QAAQ,EAAE,CACR,YAAY,EAAE;QACZ,cAAc,EAAE,cAAc,CAAC;QAC/B,QAAQ,EAAE,MAAM,CAAC;QACjB,aAAa,CAAC,EAAE,OAAO,CAAC;QACxB,gBAAgB,CAAC,EAAE,MAAM,CAAC;QAC1B,aAAa,CAAC,EAAE,eAAe,CAAC;KACjC,EACD,gBAAgB,EAAE;QAAE,QAAQ,EAAE,OAAO,CAAA;KAAE,KACpC,IAAI,GACR,IAAI;IAEP;;;;;OAKG;IACI,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI;IAEtE;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI;IAEvF;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,GAAG,MAAM,IAAI;IAErF;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,MAAM,IAAI;IAEnF;;;;;OAKG;IACI,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,KAAK,IAAI,GAAG,MAAM,IAAI;IAE9G;;;;OAIG;IACI,EAAE,CAAC,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,GAAG,iBAAiB,KAAK,IAAI,GAAG,MAAM,IAAI;IAE1G;;;;;OAKG;IACI,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,KAAK,IAAI,GAAG,MAAM,IAAI;IAE9G;;;;;OAKG;IACI,EAAE,CAAC,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,KAAK,IAAI,GAAG,MAAM,IAAI;IAE/G;;;OAGG;IACI,EAAE,CACP,IAAI,EAAE,gBAAgB,EACtB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,4BAA4B,KAAK,IAAI,GAC3E,MAAM,IAAI;IAEb;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,KAAK,IAAI,GAAG,MAAM,IAAI;IAErH;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,sBAAsB,EAAE,QAAQ,CAAC,EAAE,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI;IAE1G;;;;;OAKG;IACI,EAAE,CACP,IAAI,EAAE,oBAAoB,EAC1B,QAAQ,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,OAAO,CAAA;KAAE,KAAK,IAAI,GACjF,MAAM,IAAI;IAEb;;OAEG;IACI,EAAE,CAAC,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI;IAEvE;;;OAGG;IACI,EAAE,CACP,IAAI,EAAE,mBAAmB,EACzB,QAAQ,EAAE,CACR,OAAO,EAAE,gBAAgB,EACzB,YAAY,CAAC,EAAE;QAAE,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,KAC9E,IAAI,GACR,MAAM,IAAI;IAEb;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,GAAG,MAAM,IAAI;IAEjG;;;OAGG;IACI,EAAE,CACP,IAAI,EAAE,2BAA2B,EACjC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,GAAG,mBAAmB,KAAK,IAAI,GAC5E,MAAM,IAAI;IAEb;;;OAGG;IACI,EAAE,CACP,IAAI,EAAE,iCAAiC,EACvC,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,iBAAiB,GAAG,mBAAmB,KAAK,IAAI,GACxF,MAAM,IAAI;IAEb;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI;IAE1D;;;OAGG;IACI,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI;IAE1D;;;;OAIG;IACI,EAAE,CAAC,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI;IAE7E;;;;OAIG;IACI,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI;IAE5E;;;;OAIG;IACI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI;IAwB9D,0CAA0C;IACnC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IAEhD,iEAAiE;IAC1D,IAAI,CACT,IAAI,EAAE,gBAAgB,EACtB,YAAY,EAAE;QACZ,cAAc,EAAE,cAAc,CAAC;QAC/B,QAAQ,EAAE,MAAM,CAAC;QACjB,aAAa,CAAC,EAAE,OAAO,CAAC;QACxB,gBAAgB,CAAC,EAAE,MAAM,CAAC;QAC1B,aAAa,CAAC,EAAE,eAAe,CAAC;KACjC,EACD,gBAAgB,EAAE;QAAE,QAAQ,EAAE,OAAO,CAAA;KAAE,GACtC,IAAI;IAEP,wCAAwC;IACjC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IAE9C;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IAMxD,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAKtD,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAE3D;;;;OAIG;IACI,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI;IAE1E;;;OAGG;IACI,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,OAAO,GAAG,iBAAiB,GAAG,IAAI;IAElF;;;OAGG;IACI,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI;IAE1E;;;OAGG;IACI,IAAI,CAAC,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI;IAMpE,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,4BAA4B,GAAG,IAAI;IAEnG;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,GAAG,IAAI;IAE7F;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,sBAAsB,EAAE,QAAQ,CAAC,EAAE,IAAI,GAAG,IAAI;IAElF;;;;OAIG;IACI,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI;IAE7G;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,oBAAoB,GAAG,IAAI;IAE7C;;OAEG;IACI,IAAI,CACT,IAAI,EAAE,mBAAmB,EACzB,OAAO,EAAE,gBAAgB,EACzB,YAAY,CAAC,EAAE;QAAE,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAChF,IAAI;IAEP;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAEzE;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,2BAA2B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,GAAG,mBAAmB,GAAG,IAAI;IAE/G;;OAEG;IACI,IAAI,CACT,IAAI,EAAE,iCAAiC,EACvC,UAAU,EAAE,UAAU,EACtB,IAAI,EAAE,iBAAiB,GAAG,mBAAmB,GAC5C,IAAI;IAEP;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAEhC;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAEhC;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,kBAAkB,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI;IAErD;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,GAAG,EAAE,GAAG,GAAG,IAAI;IAEpD;;OAEG;IACI,IAAI,CAAC,IAAI,EAAE,WAAW,GAAG,IAAI;IAYpC;;OAEG;IACI,YAAY,CAAC,QAAQ,EAAE,QAAQ,GAAG,WAAW,CAAC,4BAA4B,CAAC;IAiBlF,0CAA0C;IAC1C,SAAS,CAAC,kBAAkB,IAAI,IAAI;IAMpC,2DAA2D;IAC3D,SAAS,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAgCvE;;;;;;;;;OASG;IACH,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAoBzE,yEAAyE;IACzE,SAAS,CAAC,UAAU,IAAI,OAAO;IAI/B;;;;;;;;;;;;;OAaG;IACH,SAAS,CAAC,aAAa,CACrB,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,SAAS,EACf,YAAY,EAAE,KAAK,EACnB,cAAc,EAAE,KAAK,GACpB,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;IAoC5B;;;;;OAKG;IACH,SAAS,CAAC,aAAa,CACrB,KAAK,EAAE,KAAK,EACZ,IAAI,GAAE,SAAc,EACpB,YAAY,QAAoB,EAChC,cAAc,QAAsB,GACnC,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;IAwBlC;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,aAAa,CACrB,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,SAAS,EACf,YAAY,EAAE,KAAK,EACnB,cAAc,EAAE,KAAK,GACpB,WAAW,CAAC,KAAK,CAAC;IAkGrB;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;IAcpD;;OAEG;IACH,SAAS,CAAC,cAAc,IAAI,OAAO,EAAE;IAarC;;OAEG;IACH,SAAS,CAAC,cAAc,IAAI,IAAI;IAyBhC;;OAEG;aACa,kBAAkB,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;IAE9F;;OAEG;aACa,gBAAgB,CAC9B,QAAQ,EAAE,mBAAmB,EAC7B,MAAM,CAAC,EAAE,aAAa,EACtB,KAAK,CAAC,EAAE,SAAS,GAChB,WAAW,CAAC,KAAK,CAAC;CACtB;AAED;;GAEG;AAEH,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC;AAEhC;;GAEG;AAEH,eAAO,MAAM,UAAU,eAAS,CAAC;AAgGjC,2CAA2C;AAC3C,wBAAgB,sBAAsB,CACpC,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,KAAK,GAAG,SAAS,GACvB,CAAC,sBAAsB,EAAE,OAAO,CAAC,sBAAsB,CAAC,GAAG,SAAS,EAAE,YAAY,EAAE,YAAY,GAAG,SAAS,CAAC,CAa/G"}