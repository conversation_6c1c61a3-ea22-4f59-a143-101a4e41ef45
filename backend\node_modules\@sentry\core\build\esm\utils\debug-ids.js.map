{"version": 3, "file": "debug-ids.js", "sources": ["../../../src/utils/debug-ids.ts"], "sourcesContent": ["import type { DebugImage } from '../types-hoist/debugMeta';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { GLOBAL_OBJ } from './worldwide';\n\ntype StackString = string;\ntype CachedResult = [string, string];\n\nlet parsedStackResults: Record<StackString, CachedResult> | undefined;\nlet lastKeysCount: number | undefined;\nlet cachedFilenameDebugIds: Record<string, string> | undefined;\n\n/**\n * Returns a map of filenames to debug identifiers.\n */\nexport function getFilenameToDebugIdMap(stackParser: StackParser): Record<string, string> {\n  const debugIdMap = GLOBAL_OBJ._sentryDebugIds;\n  if (!debugIdMap) {\n    return {};\n  }\n\n  const debugIdKeys = Object.keys(debugIdMap);\n\n  // If the count of registered globals hasn't changed since the last call, we\n  // can just return the cached result.\n  if (cachedFilenameDebugIds && debugIdKeys.length === lastKeysCount) {\n    return cachedFilenameDebugIds;\n  }\n\n  lastKeysCount = debugIdKeys.length;\n\n  // Build a map of filename -> debug_id.\n  cachedFilenameDebugIds = debugIdKeys.reduce<Record<string, string>>((acc, stackKey) => {\n    if (!parsedStackResults) {\n      parsedStackResults = {};\n    }\n\n    const result = parsedStackResults[stackKey];\n\n    if (result) {\n      acc[result[0]] = result[1];\n    } else {\n      const parsedStack = stackParser(stackKey);\n\n      for (let i = parsedStack.length - 1; i >= 0; i--) {\n        const stackFrame = parsedStack[i];\n        const filename = stackFrame?.filename;\n        const debugId = debugIdMap[stackKey];\n\n        if (filename && debugId) {\n          acc[filename] = debugId;\n          parsedStackResults[stackKey] = [filename, debugId];\n          break;\n        }\n      }\n    }\n\n    return acc;\n  }, {});\n\n  return cachedFilenameDebugIds;\n}\n\n/**\n * Returns a list of debug images for the given resources.\n */\nexport function getDebugImagesForResources(\n  stackParser: StackParser,\n  resource_paths: ReadonlyArray<string>,\n): DebugImage[] {\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  if (!filenameDebugIdMap) {\n    return [];\n  }\n\n  const images: DebugImage[] = [];\n  for (const path of resource_paths) {\n    if (path && filenameDebugIdMap[path]) {\n      images.push({\n        type: 'sourcemap',\n        code_file: path,\n        debug_id: filenameDebugIdMap[path] as string,\n      });\n    }\n  }\n\n  return images;\n}\n"], "names": [], "mappings": ";;AAOA,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,sBAAsB;;AAE1B;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,WAAW,EAAuC;AAC1F,EAAE,MAAM,UAAA,GAAa,UAAU,CAAC,eAAe;AAC/C,EAAE,IAAI,CAAC,UAAU,EAAE;AACnB,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,MAAM,cAAc,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;;AAE7C;AACA;AACA,EAAE,IAAI,sBAAuB,IAAG,WAAW,CAAC,MAAA,KAAW,aAAa,EAAE;AACtE,IAAI,OAAO,sBAAsB;AACjC;;AAEA,EAAE,aAAc,GAAE,WAAW,CAAC,MAAM;;AAEpC;AACA,EAAE,sBAAA,GAAyB,WAAW,CAAC,MAAM,CAAyB,CAAC,GAAG,EAAE,QAAQ,KAAK;AACzF,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC7B,MAAM,kBAAA,GAAqB,EAAE;AAC7B;;AAEA,IAAI,MAAM,MAAO,GAAE,kBAAkB,CAAC,QAAQ,CAAC;;AAE/C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA,GAAI,MAAM,CAAC,CAAC,CAAC;AAChC,WAAW;AACX,MAAM,MAAM,WAAY,GAAE,WAAW,CAAC,QAAQ,CAAC;;AAE/C,MAAM,KAAK,IAAI,CAAE,GAAE,WAAW,CAAC,MAAA,GAAS,CAAC,EAAE,CAAE,IAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACxD,QAAQ,MAAM,UAAW,GAAE,WAAW,CAAC,CAAC,CAAC;AACzC,QAAQ,MAAM,QAAA,GAAW,UAAU,EAAE,QAAQ;AAC7C,QAAQ,MAAM,OAAQ,GAAE,UAAU,CAAC,QAAQ,CAAC;;AAE5C,QAAQ,IAAI,QAAS,IAAG,OAAO,EAAE;AACjC,UAAU,GAAG,CAAC,QAAQ,CAAA,GAAI,OAAO;AACjC,UAAU,kBAAkB,CAAC,QAAQ,CAAA,GAAI,CAAC,QAAQ,EAAE,OAAO,CAAC;AAC5D,UAAU;AACV;AACA;AACA;;AAEA,IAAI,OAAO,GAAG;AACd,GAAG,EAAE,EAAE,CAAC;;AAER,EAAE,OAAO,sBAAsB;AAC/B;;AAEA;AACA;AACA;AACO,SAAS,0BAA0B;AAC1C,EAAE,WAAW;AACb,EAAE,cAAc;AAChB,EAAgB;AAChB,EAAE,MAAM,kBAAmB,GAAE,uBAAuB,CAAC,WAAW,CAAC;;AAEjE,EAAE,IAAI,CAAC,kBAAkB,EAAE;AAC3B,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,MAAM,MAAM,GAAiB,EAAE;AACjC,EAAE,KAAK,MAAM,IAAK,IAAG,cAAc,EAAE;AACrC,IAAI,IAAI,IAAK,IAAG,kBAAkB,CAAC,IAAI,CAAC,EAAE;AAC1C,MAAM,MAAM,CAAC,IAAI,CAAC;AAClB,QAAQ,IAAI,EAAE,WAAW;AACzB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,QAAQ,EAAE,kBAAkB,CAAC,IAAI,CAAE;AAC3C,OAAO,CAAC;AACR;AACA;;AAEA,EAAE,OAAO,MAAM;AACf;;;;"}