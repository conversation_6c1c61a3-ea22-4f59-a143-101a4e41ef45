console.log('Loading Jest configuration...');

module.exports = {
  verbose: true,
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/src/tests/jest.setup.js'],
  globalSetup: '<rootDir>/src/tests/jest.global-setup.js',
  globalTeardown: '<rootDir>/src/tests/jest.global-teardown.js',
  testMatch: [
    '**/test/**/*.test.js',
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/'
  ],
  testTimeout: 30000, // Increased timeout for CI environments
  detectOpenHandles: true,
  forceExit: true,
  coverageDirectory: './coverage',
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    '!**/node_modules/**',
    '!**/test/**',
    '!**/__tests__/**',
    '!**/coverage/**',
    '!**/jest.config.js',
    '!**/src/server.js',
    '!**/src/app.js'
  ],
  coverageReporters: ['text', 'cobertura', 'lcov'],
  reporters: [
    'default',
    ['jest-junit', { 
      outputDirectory: './test-results', 
      outputName: 'junit.xml',
      ancestorSeparator: ' > ',
      uniqueOutputName: 'false',
      suiteNameTemplate: '{filepath}'
    }]
  ],
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  },
  globals: {
    'ts-jest': {
      isolatedModules: true
    }
  },
  // Add this to help with debugging
  testRunner: 'jest-circus/runner'
};
