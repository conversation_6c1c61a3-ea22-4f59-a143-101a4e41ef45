{"version": 3, "file": "dynamicSamplingContext.js", "sources": ["../../../src/tracing/dynamicSamplingContext.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getClient } from '../currentScopes';\nimport type { Scope } from '../scope';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport type { Span } from '../types-hoist/span';\nimport { baggageHeaderToDynamicSamplingContext, dynamicSamplingContextToSentryBaggageHeader } from '../utils/baggage';\nimport { extractOrgIdFromDsnHost } from '../utils/dsn';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled';\nimport { addNonEnumerableProperty } from '../utils/object';\nimport { getRootSpan, spanIsSampled, spanToJSO<PERSON> } from '../utils/spanUtils';\nimport { getCapturedScopesOnSpan } from './utils';\n\n/**\n * If you change this value, also update the terser plugin config to\n * avoid minification of the object property!\n */\nconst FROZEN_DSC_FIELD = '_frozenDsc';\n\ntype SpanWithMaybeDsc = Span & {\n  [FROZEN_DSC_FIELD]?: Partial<DynamicSamplingContext> | undefined;\n};\n\n/**\n * Freeze the given DSC on the given span.\n */\nexport function freezeDscOnSpan(span: Span, dsc: Partial<DynamicSamplingContext>): void {\n  const spanWithMaybeDsc = span as SpanWithMaybeDsc;\n  addNonEnumerableProperty(spanWithMaybeDsc, FROZEN_DSC_FIELD, dsc);\n}\n\n/**\n * Creates a dynamic sampling context from a client.\n *\n * Dispatches the `createDsc` lifecycle hook as a side effect.\n */\nexport function getDynamicSamplingContextFromClient(trace_id: string, client: Client): DynamicSamplingContext {\n  const options = client.getOptions();\n\n  const { publicKey: public_key, host } = client.getDsn() || {};\n\n  let org_id: string | undefined;\n  if (options.orgId) {\n    org_id = String(options.orgId);\n  } else if (host) {\n    org_id = extractOrgIdFromDsnHost(host);\n  }\n\n  // Instead of conditionally adding non-undefined values, we add them and then remove them if needed\n  // otherwise, the order of baggage entries changes, which \"breaks\" a bunch of tests etc.\n  const dsc: DynamicSamplingContext = {\n    environment: options.environment || DEFAULT_ENVIRONMENT,\n    release: options.release,\n    public_key,\n    trace_id,\n    org_id,\n  };\n\n  client.emit('createDsc', dsc);\n\n  return dsc;\n}\n\n/**\n * Get the dynamic sampling context for the currently active scopes.\n */\nexport function getDynamicSamplingContextFromScope(client: Client, scope: Scope): Partial<DynamicSamplingContext> {\n  const propagationContext = scope.getPropagationContext();\n  return propagationContext.dsc || getDynamicSamplingContextFromClient(propagationContext.traceId, client);\n}\n\n/**\n * Creates a dynamic sampling context from a span (and client and scope)\n *\n * @param span the span from which a few values like the root span name and sample rate are extracted.\n *\n * @returns a dynamic sampling context\n */\nexport function getDynamicSamplingContextFromSpan(span: Span): Readonly<Partial<DynamicSamplingContext>> {\n  const client = getClient();\n  if (!client) {\n    return {};\n  }\n\n  const rootSpan = getRootSpan(span);\n  const rootSpanJson = spanToJSON(rootSpan);\n  const rootSpanAttributes = rootSpanJson.data;\n  const traceState = rootSpan.spanContext().traceState;\n\n  // The span sample rate that was locally applied to the root span should also always be applied to the DSC, even if the DSC is frozen.\n  // This is so that the downstream traces/services can use parentSampleRate in their `tracesSampler` to make consistent sampling decisions across the entire trace.\n  const rootSpanSampleRate =\n    traceState?.get('sentry.sample_rate') ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE];\n\n  function applyLocalSampleRateToDsc(dsc: Partial<DynamicSamplingContext>): Partial<DynamicSamplingContext> {\n    if (typeof rootSpanSampleRate === 'number' || typeof rootSpanSampleRate === 'string') {\n      dsc.sample_rate = `${rootSpanSampleRate}`;\n    }\n    return dsc;\n  }\n\n  // For core implementation, we freeze the DSC onto the span as a non-enumerable property\n  const frozenDsc = (rootSpan as SpanWithMaybeDsc)[FROZEN_DSC_FIELD];\n  if (frozenDsc) {\n    return applyLocalSampleRateToDsc(frozenDsc);\n  }\n\n  // For OpenTelemetry, we freeze the DSC on the trace state\n  const traceStateDsc = traceState?.get('sentry.dsc');\n\n  // If the span has a DSC, we want it to take precedence\n  const dscOnTraceState = traceStateDsc && baggageHeaderToDynamicSamplingContext(traceStateDsc);\n\n  if (dscOnTraceState) {\n    return applyLocalSampleRateToDsc(dscOnTraceState);\n  }\n\n  // Else, we generate it from the span\n  const dsc = getDynamicSamplingContextFromClient(span.spanContext().traceId, client);\n\n  // We don't want to have a transaction name in the DSC if the source is \"url\" because URLs might contain PII\n  const source = rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n\n  // after JSON conversion, txn.name becomes jsonSpan.description\n  const name = rootSpanJson.description;\n  if (source !== 'url' && name) {\n    dsc.transaction = name;\n  }\n\n  // How can we even land here with hasSpansEnabled() returning false?\n  // Otel creates a Non-recording span in Tracing Without Performance mode when handling incoming requests\n  // So we end up with an active span that is not sampled (neither positively nor negatively)\n  if (hasSpansEnabled()) {\n    dsc.sampled = String(spanIsSampled(rootSpan));\n    dsc.sample_rand =\n      // In OTEL we store the sample rand on the trace state because we cannot access scopes for NonRecordingSpans\n      // The Sentry OTEL SpanSampler takes care of writing the sample rand on the root span\n      traceState?.get('sentry.sample_rand') ??\n      // On all other platforms we can actually get the scopes from a root span (we use this as a fallback)\n      getCapturedScopesOnSpan(rootSpan).scope?.getPropagationContext().sampleRand.toString();\n  }\n\n  applyLocalSampleRateToDsc(dsc);\n\n  client.emit('createDsc', dsc, rootSpan);\n\n  return dsc;\n}\n\n/**\n * Convert a Span to a baggage header.\n */\nexport function spanToBaggageHeader(span: Span): string | undefined {\n  const dsc = getDynamicSamplingContextFromSpan(span);\n  return dynamicSamplingContextToSentryBaggageHeader(dsc);\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA,MAAM,gBAAA,GAAmB,YAAY;;AAMrC;AACA;AACA;AACO,SAAS,eAAe,CAAC,IAAI,EAAQ,GAAG,EAAyC;AACxF,EAAE,MAAM,gBAAiB,GAAE,IAAK;AAChC,EAAE,wBAAwB,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,mCAAmC,CAAC,QAAQ,EAAU,MAAM,EAAkC;AAC9G,EAAE,MAAM,OAAQ,GAAE,MAAM,CAAC,UAAU,EAAE;;AAErC,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAK,EAAA,GAAI,MAAM,CAAC,MAAM,EAAG,IAAG,EAAE;;AAE/D,EAAE,IAAI,MAAM;AACZ,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,SAAS,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAClC,GAAI,MAAK,IAAI,IAAI,EAAE;AACnB,IAAI,MAAO,GAAE,uBAAuB,CAAC,IAAI,CAAC;AAC1C;;AAEA;AACA;AACA,EAAE,MAAM,GAAG,GAA2B;AACtC,IAAI,WAAW,EAAE,OAAO,CAAC,WAAA,IAAe,mBAAmB;AAC3D,IAAI,OAAO,EAAE,OAAO,CAAC,OAAO;AAC5B,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,GAAG;;AAEH,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;;AAE/B,EAAE,OAAO,GAAG;AACZ;;AAEA;AACA;AACA;AACO,SAAS,kCAAkC,CAAC,MAAM,EAAU,KAAK,EAA0C;AAClH,EAAE,MAAM,kBAAmB,GAAE,KAAK,CAAC,qBAAqB,EAAE;AAC1D,EAAE,OAAO,kBAAkB,CAAC,GAAA,IAAO,mCAAmC,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC;AAC1G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iCAAiC,CAAC,IAAI,EAAmD;AACzG,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,MAAM,QAAS,GAAE,WAAW,CAAC,IAAI,CAAC;AACpC,EAAE,MAAM,YAAa,GAAE,UAAU,CAAC,QAAQ,CAAC;AAC3C,EAAE,MAAM,kBAAA,GAAqB,YAAY,CAAC,IAAI;AAC9C,EAAE,MAAM,aAAa,QAAQ,CAAC,WAAW,EAAE,CAAC,UAAU;;AAEtD;AACA;AACA,EAAE,MAAM,kBAAmB;AAC3B,IAAI,UAAU,EAAE,GAAG,CAAC,oBAAoB,CAAE;AAC1C,IAAI,kBAAkB,CAAC,qCAAqC,CAAE;AAC9D,IAAI,kBAAkB,CAAC,oDAAoD,CAAC;;AAE5E,EAAE,SAAS,yBAAyB,CAAC,GAAG,EAAoE;AAC5G,IAAI,IAAI,OAAO,kBAAmB,KAAI,QAAS,IAAG,OAAO,kBAAA,KAAuB,QAAQ,EAAE;AAC1F,MAAM,GAAG,CAAC,WAAY,GAAE,CAAC,EAAA,kBAAA,CAAA,CAAA;AACA;AACA,IAAA,OAAA,GAAA;AACA;;AAEA;AACA,EAAA,MAAA,SAAA,GAAA,CAAA,QAAA,GAAA,gBAAA,CAAA;AACA,EAAA,IAAA,SAAA,EAAA;AACA,IAAA,OAAA,yBAAA,CAAA,SAAA,CAAA;AACA;;AAEA;AACA,EAAA,MAAA,aAAA,GAAA,UAAA,EAAA,GAAA,CAAA,YAAA,CAAA;;AAEA;AACA,EAAA,MAAA,eAAA,GAAA,aAAA,IAAA,qCAAA,CAAA,aAAA,CAAA;;AAEA,EAAA,IAAA,eAAA,EAAA;AACA,IAAA,OAAA,yBAAA,CAAA,eAAA,CAAA;AACA;;AAEA;AACA,EAAA,MAAA,GAAA,GAAA,mCAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,OAAA,EAAA,MAAA,CAAA;;AAEA;AACA,EAAA,MAAA,MAAA,GAAA,kBAAA,CAAA,gCAAA,CAAA;;AAEA;AACA,EAAA,MAAA,IAAA,GAAA,YAAA,CAAA,WAAA;AACA,EAAA,IAAA,MAAA,KAAA,KAAA,IAAA,IAAA,EAAA;AACA,IAAA,GAAA,CAAA,WAAA,GAAA,IAAA;AACA;;AAEA;AACA;AACA;AACA,EAAA,IAAA,eAAA,EAAA,EAAA;AACA,IAAA,GAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA,CAAA,CAAA;AACA,IAAA,GAAA,CAAA,WAAA;AACA;AACA;AACA,MAAA,UAAA,EAAA,GAAA,CAAA,oBAAA,CAAA;AACA;AACA,MAAA,uBAAA,CAAA,QAAA,CAAA,CAAA,KAAA,EAAA,qBAAA,EAAA,CAAA,UAAA,CAAA,QAAA,EAAA;AACA;;AAEA,EAAA,yBAAA,CAAA,GAAA,CAAA;;AAEA,EAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,GAAA,EAAA,QAAA,CAAA;;AAEA,EAAA,OAAA,GAAA;AACA;;AAEA;AACA;AACA;AACA,SAAA,mBAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,GAAA,GAAA,iCAAA,CAAA,IAAA,CAAA;AACA,EAAA,OAAA,2CAAA,CAAA,GAAA,CAAA;AACA;;;;"}