const Transaction = require('../models/transaction.model');
const Category = require('../models/category.model');
const cache = require('../services/cache.service');
const mongoose = require('mongoose');
const validator = require('validator');
const { v4: uuidv4 } = require('uuid');

// Function to validate transaction input
function validateTransactionInput(data) {
  const errors = [];

  // Validate amount
  if (typeof data.amount !== 'number' || isNaN(data.amount)) {
    errors.push('Amount must be a number');
  }

  // Validate type
  if (!['income', 'expense'].includes(data.type)) {
    errors.push("Type must be either 'income' or 'expense'");
  }

  // Validate category ID
  if (!validator.isMongoId(data.category)) {
    errors.push('Category ID is invalid');
  }

  // Validate date if present
  if (data.date && !validator.isISO8601(data.date)) {
    errors.push('Date must be in ISO 8601 format');
  }

  // Validate recurring fields if isRecurring is true
  if (data.isRecurring) {
    if (!['daily', 'weekly', 'monthly', 'yearly'].includes(data.recurringFrequency)) {
      errors.push('Recurring frequency must be one of: daily, weekly, monthly, yearly');
    }
    if (data.recurringEndDate && !validator.isISO8601(data.recurringEndDate)) {
      errors.push('Recurring end date must be in ISO 8601 format');
    }
  }

  return errors;
}

// @desc    Create a new transaction
// @route   POST /api/transactions
// @access  Private
exports.createTransaction = async (req, res) => {
  try {
    const { amount, type, category, description, date, isRecurring, recurringFrequency, recurringEndDate } = req.body;

    // Validate input
    const validationErrors = validateTransactionInput(req.body);
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    // Validate category exists and belongs to the user
    const categoryExists = await Category.findOne({
      _id: category,
      user: req.user.id,
    });

    if (!categoryExists) {
      return res.status(404).json({
        success: false,
        message: 'Category not found',
      });
    }

    // Make sure category type matches transaction type
    if (categoryExists.type !== type) {
      return res.status(400).json({
        success: false,
        message: `Category must be of type '${type}'`,
      });
    }

    // Convert string IDs to ObjectId for MongoDB
    let userId = req.body.user || req.body.userId;
    if (userId && typeof userId === 'string') {
      userId = new mongoose.Types.ObjectId(userId);
    }

    // Create transaction
    const transactionData = {
      user: userId || req.user.id,
      amount,
      type,
      category,
      description,
      date: date || Date.now(),
      isRecurring: isRecurring || false,
      recurringFrequency: isRecurring ? recurringFrequency : null,
      recurringEndDate: isRecurring ? recurringEndDate : null,
    };

    const transaction = await Transaction.create(transactionData);

    // Invalidate all cached transactions for this user
    await cache.invalidate(`transactions:${req.user.id}:*`);

    res.status(201).json({
      success: true,
      data: transaction,
      meta: {
        cacheHit: false,
        requestId: uuidv4(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

// @desc    Get all transactions for a user with filtering
// @route   GET /api/transactions
// @access  Private
exports.getTransactions = async (req, res) => {
  try {
    const cacheKey = `transactions:${req.user.id}:${JSON.stringify(req.query)}`;
    
    // Try to get from cache first
    const cachedData = await cache.get(cacheKey);
    if (cachedData) {
      return res.json({
        ...cachedData,
        meta: {
          cacheHit: true,
          requestId: uuidv4(),
          timestamp: new Date().toISOString()
        }
      });
    }
    
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const sortField = req.query.sortBy || 'date';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;
    
    const transactions = await Transaction.find({ user: req.user.id })
      .sort({ [sortField]: sortOrder })
      .skip(skip)
      .limit(limit)
      .populate('category', 'name color');

    const total = await Transaction.countDocuments({ user: req.user.id });
    
    const response = {
      success: true,
      count: transactions.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      data: transactions,
      meta: {
        cacheHit: false,
        requestId: uuidv4(),
        timestamp: new Date().toISOString()
      }
    };
    
    // Cache the response for 5 minutes
    const endpoint = req.path.split('/').pop();
    const ttlMap = {
      transactions: 600, // 10 minutes for transaction lists
      statistics: 3600, // 1 hour for dashboard stats
      categories: 172800, // 48 hours for category lists
      userProfile: 86400 // 24 hours for user profile data
    };
    await cache.set(cacheKey, response, ttlMap[endpoint] || 300);
    
    res.json(response);
  } catch (err) {
    res.status(500).json({
      success: false,
      message: 'Server Error',
      code: 'TRANSACTION_FETCH_ERROR'
    });
  }
};

// @desc    Get a single transaction
// @route   GET /api/transactions/:id
// @access  Private
exports.getTransaction = async (req, res) => {
  try {
    const transaction = await Transaction.findOne({
      _id: req.params.id,
      user: req.user._id,
    }).populate('category', 'name color icon');

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    res.status(200).json({
      success: true,
      data: transaction,
      meta: {
        cacheHit: false,
        requestId: uuidv4(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

// @desc    Update a transaction
// @route   PUT /api/transactions/:id
// @access  Private
exports.updateTransaction = async (req, res) => {
  try {
    // Make sure transaction exists and belongs to user
    let transaction = await Transaction.findOneAndUpdate(
      { _id: req.params.id, user: req.user._id },
      req.body,
      { new: true, runValidators: true }
    ).populate('category', 'name color icon');

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    // If category is being updated, validate it
    if (req.body.category) {
      const category = await Category.findOne({
        _id: req.body.category,
        user: req.user.id,
      });

      if (!category) {
        return res.status(404).json({
          success: false,
          message: 'Category not found',
        });
      }

      // Make sure category type matches transaction type
      const type = req.body.type || transaction.type;
      if (category.type !== type) {
        return res.status(400).json({
          success: false,
          message: `Category must be of type '${type}'`,
        });
      }
    }

    // Invalidate all cached transactions for this user
    await cache.invalidate(`transactions:${req.user.id}:*`);

    res.status(200).json({
      success: true,
      data: transaction,
      meta: {
        cacheHit: false,
        requestId: uuidv4(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

// @desc    Delete a transaction
// @route   DELETE /api/transactions/:id
// @access  Private
exports.deleteTransaction = async (req, res) => {
  try {
    const transaction = await Transaction.findOneAndDelete({
      _id: req.params.id,
      user: req.user._id,
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    // Invalidate all cached transactions for this user
    await cache.invalidate(`transactions:${req.user.id}:*`);

    res.status(200).json({
      success: true,
      data: {},
      meta: {
        cacheHit: false,
        requestId: uuidv4(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

// @desc    Get transaction statistics
// @route   GET /api/transactions/stats
// @access  Private
exports.getTransactionStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Please provide startDate and endDate',
      });
    }

    const stats = await Transaction.aggregate([
      {
        $match: {
          user: req.user._id,
          date: { $gte: new Date(startDate), $lte: new Date(endDate) },
        },
      },
      {
        $group: {
          _id: {
            type: '$type',
            month: { $month: '$date' },
            year: { $year: '$date' },
          },
          total: { $sum: '$amount' },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 },
      },
    ]);

    // Also get category breakdown
    const categoryBreakdown = await Transaction.aggregate([
      {
        $match: {
          user: req.user._id,
          date: { $gte: new Date(startDate), $lte: new Date(endDate) },
        },
      },
      {
        $group: {
          _id: {
            type: '$type',
            category: '$category',
          },
          total: { $sum: '$amount' },
          count: { $sum: 1 },
        },
      },
      {
        $lookup: {
          from: 'categories',
          localField: '_id.category',
          foreignField: '_id',
          as: 'categoryInfo',
        },
      },
      {
        $addFields: {
          categoryName: { $arrayElemAt: ['$categoryInfo.name', 0] },
          categoryColor: { $arrayElemAt: ['$categoryInfo.color', 0] },
          categoryIcon: { $arrayElemAt: ['$categoryInfo.icon', 0] },
        },
      },
      {
        $project: {
          _id: 0,
          type: '$_id.type',
          category: '$_id.category',
          categoryName: 1,
          categoryColor: 1,
          categoryIcon: 1,
          total: 1,
          count: 1,
        },
      },
      {
        $sort: { total: -1 },
      },
    ]);

    const cacheKey = `transactions:${req.user.id}:stats`;
    const response = {
      success: true,
      data: {
        monthlyStats: stats,
        categoryBreakdown,
      },
      meta: {
        cacheHit: false,
        requestId: uuidv4(),
        timestamp: new Date().toISOString()
      }
    };

    const endpoint = req.path.split('/').pop();
    const ttlMap = {
      transactions: 600, // 10 minutes for transaction lists
      statistics: 3600, // 1 hour for dashboard stats
      categories: 172800, // 48 hours for category lists
      userProfile: 86400 // 24 hours for user profile data
    };
    await cache.set(cacheKey, response, ttlMap[endpoint] || 300);

    res.status(200).json(response);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};
