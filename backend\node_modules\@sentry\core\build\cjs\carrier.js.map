{"version": 3, "file": "carrier.js", "sources": ["../../src/carrier.ts"], "sourcesContent": ["import type { AsyncContextStack } from './asyncContext/stackStrategy';\nimport type { AsyncContextStrategy } from './asyncContext/types';\nimport type { Scope } from './scope';\nimport type { Logger } from './utils/logger';\nimport { SDK_VERSION } from './utils/version';\nimport { GLOBAL_OBJ } from './utils/worldwide';\n\n/**\n * An object that contains globally accessible properties and maintains a scope stack.\n * @hidden\n */\nexport interface Carrier {\n  __SENTRY__?: VersionedCarrier;\n}\n\ntype VersionedCarrier = {\n  version?: string;\n} & Record<Exclude<string, 'version'>, SentryCarrier>;\n\nexport interface SentryCarrier {\n  acs?: AsyncContextStrategy;\n  stack?: AsyncContextStack;\n\n  globalScope?: Scope;\n  defaultIsolationScope?: Scope;\n  defaultCurrentScope?: Scope;\n  logger?: Logger;\n\n  /** Overwrites TextEncoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  encodePolyfill?: (input: string) => Uint8Array;\n  /** Overwrites TextDecoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  decodePolyfill?: (input: Uint8Array) => string;\n}\n\n/**\n * Returns the global shim registry.\n *\n * FIXME: This function is problematic, because despite always returning a valid Carrier,\n * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check\n * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.\n **/\nexport function getMainCarrier(): Carrier {\n  // This ensures a Sentry carrier exists\n  getSentryCarrier(GLOBAL_OBJ);\n  return GLOBAL_OBJ;\n}\n\n/** Will either get the existing sentry carrier, or create a new one. */\nexport function getSentryCarrier(carrier: Carrier): SentryCarrier {\n  const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});\n\n  // For now: First SDK that sets the .version property wins\n  __SENTRY__.version = __SENTRY__.version || SDK_VERSION;\n\n  // Intentionally populating and returning the version of \"this\" SDK instance\n  // rather than what's set in .version so that \"this\" SDK always gets its carrier\n  return (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n}\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__[]` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nexport function getGlobalSingleton<Prop extends keyof SentryCarrier>(\n  name: Prop,\n  creator: () => NonNullable<SentryCarrier[Prop]>,\n  obj = GLOBAL_OBJ,\n): NonNullable<SentryCarrier[Prop]> {\n  const __SENTRY__ = (obj.__SENTRY__ = obj.__SENTRY__ || {});\n  const carrier = (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n  // Note: We do not want to set `carrier.version` here, as this may be called before any `init` is called, e.g. for the default scopes\n  return carrier[name] || (carrier[name] = creator());\n}\n"], "names": ["GLOBAL_OBJ", "SDK_VERSION"], "mappings": ";;;;;AAOA;AACA;AACA;AACA;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,GAAY;AAC1C;AACA,EAAE,gBAAgB,CAACA,oBAAU,CAAC;AAC9B,EAAE,OAAOA,oBAAU;AACnB;;AAEA;AACO,SAAS,gBAAgB,CAAC,OAAO,EAA0B;AAClE,EAAE,MAAM,UAAA,IAAc,OAAO,CAAC,UAAA,GAAa,OAAO,CAAC,UAAA,IAAc,EAAE,CAAC;;AAEpE;AACA,EAAE,UAAU,CAAC,OAAQ,GAAE,UAAU,CAAC,OAAA,IAAWC,mBAAW;;AAExD;AACA;AACA,EAAE,QAAQ,UAAU,CAACA,mBAAW,CAAE,GAAE,UAAU,CAACA,mBAAW,CAAA,IAAK,EAAE;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB;AAClC,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,GAAA,GAAMD,oBAAU;AAClB,EAAoC;AACpC,EAAE,MAAM,UAAA,IAAc,GAAG,CAAC,UAAA,GAAa,GAAG,CAAC,UAAA,IAAc,EAAE,CAAC;AAC5D,EAAE,MAAM,OAAQ,IAAG,UAAU,CAACC,mBAAW,CAAA,GAAI,UAAU,CAACA,mBAAW,CAAA,IAAK,EAAE,CAAC;AAC3E;AACA,EAAE,OAAO,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAE,GAAE,OAAO,EAAE,CAAC;AACrD;;;;;;"}