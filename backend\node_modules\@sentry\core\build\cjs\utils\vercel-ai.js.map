{"version": 3, "file": "vercel-ai.js", "sources": ["../../../src/utils/vercel-ai.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '../semanticAttributes';\nimport type { Event } from '../types-hoist/event';\nimport type { Span, SpanAttributes, SpanJSON, SpanOrigin } from '../types-hoist/span';\nimport { spanToJSON } from './spanUtils';\nimport {\n  AI_MODEL_ID_ATTRIBUTE,\n  AI_MODEL_PROVIDER_ATTRIBUTE,\n  AI_PROMPT_ATTRIBUTE,\n  AI_PROMPT_MESSAGES_ATTRIBUTE,\n  AI_PROMPT_TOOLS_ATTRIBUTE,\n  AI_RESPONSE_TEXT_ATTRIBUTE,\n  AI_RESPONSE_TOOL_CALLS_ATTRIBUTE,\n  AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE,\n  AI_TOOL_CALL_ID_ATTRIBUTE,\n  AI_TOOL_CALL_NAME_ATTRIBUTE,\n  AI_<PERSON>GE_COMPLETION_TOKENS_ATTRIBUTE,\n  AI_USAGE_PROMPT_TOKENS_ATTRIBUTE,\n  GEN_AI_RESPONSE_MODEL_ATTRIBUTE,\n  GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE,\n  GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE,\n} from './vercel-ai-attributes';\n\nfunction addOriginToSpan(span: Span, origin: SpanOrigin): void {\n  span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, origin);\n}\n\n/**\n * Post-process spans emitted by the Vercel AI SDK.\n * This is supposed to be used in `client.on('spanStart', ...)\n */\nfunction onVercelAiSpanStart(span: Span): void {\n  const { data: attributes, description: name } = spanToJSON(span);\n\n  if (!name) {\n    return;\n  }\n\n  // Tool call spans\n  // https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans\n  if (attributes[AI_TOOL_CALL_NAME_ATTRIBUTE] && attributes[AI_TOOL_CALL_ID_ATTRIBUTE] && name === 'ai.toolCall') {\n    processToolCallSpan(span, attributes);\n    return;\n  }\n\n  // The AI and Provider must be defined for generate, stream, and embed spans.\n  // The id of the model\n  const aiModelId = attributes[AI_MODEL_ID_ATTRIBUTE];\n  // the provider of the model\n  const aiModelProvider = attributes[AI_MODEL_PROVIDER_ATTRIBUTE];\n  if (typeof aiModelId !== 'string' || typeof aiModelProvider !== 'string' || !aiModelId || !aiModelProvider) {\n    return;\n  }\n\n  processGenerateSpan(span, name, attributes);\n}\n\nconst vercelAiEventProcessor = Object.assign(\n  (event: Event): Event => {\n    if (event.type === 'transaction' && event.spans) {\n      for (const span of event.spans) {\n        // this mutates spans in-place\n        processEndedVercelAiSpan(span);\n      }\n    }\n    return event;\n  },\n  { id: 'VercelAiEventProcessor' },\n);\n\n/**\n * Post-process spans emitted by the Vercel AI SDK.\n */\nfunction processEndedVercelAiSpan(span: SpanJSON): void {\n  const { data: attributes, origin } = span;\n\n  if (origin !== 'auto.vercelai.otel') {\n    return;\n  }\n\n  renameAttributeKey(attributes, AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE, GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE);\n  renameAttributeKey(attributes, AI_USAGE_PROMPT_TOKENS_ATTRIBUTE, GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE);\n\n  if (\n    typeof attributes[GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE] === 'number' &&\n    typeof attributes[GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE] === 'number'\n  ) {\n    attributes['gen_ai.usage.total_tokens'] =\n      attributes[GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE] + attributes[GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE];\n  }\n\n  // Rename AI SDK attributes to standardized gen_ai attributes\n  renameAttributeKey(attributes, AI_PROMPT_MESSAGES_ATTRIBUTE, 'gen_ai.request.messages');\n  renameAttributeKey(attributes, AI_RESPONSE_TEXT_ATTRIBUTE, 'gen_ai.response.text');\n  renameAttributeKey(attributes, AI_RESPONSE_TOOL_CALLS_ATTRIBUTE, 'gen_ai.response.tool_calls');\n  renameAttributeKey(attributes, AI_PROMPT_TOOLS_ATTRIBUTE, 'gen_ai.request.available_tools');\n}\n\n/**\n * Renames an attribute key in the provided attributes object if the old key exists.\n * This function safely handles null and undefined values.\n */\nfunction renameAttributeKey(attributes: Record<string, unknown>, oldKey: string, newKey: string): void {\n  if (attributes[oldKey] != null) {\n    attributes[newKey] = attributes[oldKey];\n    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n    delete attributes[oldKey];\n  }\n}\n\nfunction processToolCallSpan(span: Span, attributes: SpanAttributes): void {\n  addOriginToSpan(span, 'auto.vercelai.otel');\n  span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.execute_tool');\n  span.setAttribute('gen_ai.tool.call.id', attributes[AI_TOOL_CALL_ID_ATTRIBUTE]);\n  span.setAttribute('gen_ai.tool.name', attributes[AI_TOOL_CALL_NAME_ATTRIBUTE]);\n  span.updateName(`execute_tool ${attributes[AI_TOOL_CALL_NAME_ATTRIBUTE]}`);\n}\n\nfunction processGenerateSpan(span: Span, name: string, attributes: SpanAttributes): void {\n  addOriginToSpan(span, 'auto.vercelai.otel');\n\n  const nameWthoutAi = name.replace('ai.', '');\n  span.setAttribute('ai.pipeline.name', nameWthoutAi);\n  span.updateName(nameWthoutAi);\n\n  // If a Telemetry name is set and it is a pipeline span, use that as the operation name\n  const functionId = attributes[AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE];\n  if (functionId && typeof functionId === 'string' && name.split('.').length - 1 === 1) {\n    span.updateName(`${nameWthoutAi} ${functionId}`);\n    span.setAttribute('ai.pipeline.name', functionId);\n  }\n\n  if (attributes[AI_PROMPT_ATTRIBUTE]) {\n    span.setAttribute('gen_ai.prompt', attributes[AI_PROMPT_ATTRIBUTE]);\n  }\n  if (attributes[AI_MODEL_ID_ATTRIBUTE] && !attributes[GEN_AI_RESPONSE_MODEL_ATTRIBUTE]) {\n    span.setAttribute(GEN_AI_RESPONSE_MODEL_ATTRIBUTE, attributes[AI_MODEL_ID_ATTRIBUTE]);\n  }\n  span.setAttribute('ai.streaming', name.includes('stream'));\n\n  // Generate Spans\n  if (name === 'ai.generateText') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.invoke_agent');\n    return;\n  }\n\n  if (name === 'ai.generateText.doGenerate') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.generate_text');\n    span.updateName(`generate_text ${attributes[AI_MODEL_ID_ATTRIBUTE]}`);\n    return;\n  }\n\n  if (name === 'ai.streamText') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.invoke_agent');\n    return;\n  }\n\n  if (name === 'ai.streamText.doStream') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.stream_text');\n    span.updateName(`stream_text ${attributes[AI_MODEL_ID_ATTRIBUTE]}`);\n    return;\n  }\n\n  if (name === 'ai.generateObject') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.invoke_agent');\n    return;\n  }\n\n  if (name === 'ai.generateObject.doGenerate') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.generate_object');\n    span.updateName(`generate_object ${attributes[AI_MODEL_ID_ATTRIBUTE]}`);\n    return;\n  }\n\n  if (name === 'ai.streamObject') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.invoke_agent');\n    return;\n  }\n\n  if (name === 'ai.streamObject.doStream') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.stream_object');\n    span.updateName(`stream_object ${attributes[AI_MODEL_ID_ATTRIBUTE]}`);\n    return;\n  }\n\n  if (name === 'ai.embed') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.invoke_agent');\n    return;\n  }\n\n  if (name === 'ai.embed.doEmbed') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.embed');\n    span.updateName(`embed ${attributes[AI_MODEL_ID_ATTRIBUTE]}`);\n    return;\n  }\n\n  if (name === 'ai.embedMany') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.invoke_agent');\n    return;\n  }\n\n  if (name === 'ai.embedMany.doEmbed') {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'gen_ai.embed_many');\n    span.updateName(`embed_many ${attributes[AI_MODEL_ID_ATTRIBUTE]}`);\n    return;\n  }\n\n  if (name.startsWith('ai.stream')) {\n    span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.run');\n    return;\n  }\n}\n\n/**\n * Add event processors to the given client to process Vercel AI spans.\n */\nexport function addVercelAiProcessors(client: Client): void {\n  client.on('spanStart', onVercelAiSpanStart);\n  // Note: We cannot do this on `spanEnd`, because the span cannot be mutated anymore at this point\n  client.addEventProcessor(vercelAiEventProcessor);\n}\n"], "names": ["SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "spanToJSON", "AI_TOOL_CALL_NAME_ATTRIBUTE", "AI_TOOL_CALL_ID_ATTRIBUTE", "AI_MODEL_ID_ATTRIBUTE", "AI_MODEL_PROVIDER_ATTRIBUTE", "AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE", "GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE", "AI_USAGE_PROMPT_TOKENS_ATTRIBUTE", "GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE", "AI_PROMPT_MESSAGES_ATTRIBUTE", "AI_RESPONSE_TEXT_ATTRIBUTE", "AI_RESPONSE_TOOL_CALLS_ATTRIBUTE", "AI_PROMPT_TOOLS_ATTRIBUTE", "SEMANTIC_ATTRIBUTE_SENTRY_OP", "AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE", "AI_PROMPT_ATTRIBUTE", "GEN_AI_RESPONSE_MODEL_ATTRIBUTE"], "mappings": ";;;;;;AAuBA,SAAS,eAAe,CAAC,IAAI,EAAQ,MAAM,EAAoB;AAC/D,EAAE,IAAI,CAAC,YAAY,CAACA,mDAAgC,EAAE,MAAM,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,IAAI,EAAc;AAC/C,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,IAAA,EAAO,GAAEC,oBAAU,CAAC,IAAI,CAAC;;AAElE,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI;AACJ;;AAEA;AACA;AACA,EAAE,IAAI,UAAU,CAACC,8CAA2B,CAAE,IAAG,UAAU,CAACC,4CAAyB,CAAE,IAAG,IAAK,KAAI,aAAa,EAAE;AAClH,IAAI,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC;AACzC,IAAI;AACJ;;AAEA;AACA;AACA,EAAE,MAAM,SAAU,GAAE,UAAU,CAACC,wCAAqB,CAAC;AACrD;AACA,EAAE,MAAM,eAAgB,GAAE,UAAU,CAACC,8CAA2B,CAAC;AACjE,EAAE,IAAI,OAAO,SAAU,KAAI,YAAY,OAAO,eAAgB,KAAI,YAAY,CAAC,aAAa,CAAC,eAAe,EAAE;AAC9G,IAAI;AACJ;;AAEA,EAAE,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC;AAC7C;;AAEA,MAAM,sBAAuB,GAAE,MAAM,CAAC,MAAM;AAC5C,EAAE,CAAC,KAAK,KAAmB;AAC3B,IAAI,IAAI,KAAK,CAAC,IAAA,KAAS,aAAA,IAAiB,KAAK,CAAC,KAAK,EAAE;AACrD,MAAM,KAAK,MAAM,IAAA,IAAQ,KAAK,CAAC,KAAK,EAAE;AACtC;AACA,QAAQ,wBAAwB,CAAC,IAAI,CAAC;AACtC;AACA;AACA,IAAI,OAAO,KAAK;AAChB,GAAG;AACH,EAAE,EAAE,EAAE,EAAE,wBAAA,EAA0B;AAClC,CAAC;;AAED;AACA;AACA;AACA,SAAS,wBAAwB,CAAC,IAAI,EAAkB;AACxD,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAA,EAAS,GAAE,IAAI;;AAE3C,EAAE,IAAI,MAAO,KAAI,oBAAoB,EAAE;AACvC,IAAI;AACJ;;AAEA,EAAE,kBAAkB,CAAC,UAAU,EAAEC,uDAAoC,EAAEC,uDAAoC,CAAC;AAC5G,EAAE,kBAAkB,CAAC,UAAU,EAAEC,mDAAgC,EAAEC,sDAAmC,CAAC;;AAEvG,EAAE;AACF,IAAI,OAAO,UAAU,CAACF,uDAAoC,CAAA,KAAM,QAAS;AACzE,IAAI,OAAO,UAAU,CAACE,sDAAmC,MAAM;AAC/D,IAAI;AACJ,IAAI,UAAU,CAAC,2BAA2B,CAAE;AAC5C,MAAM,UAAU,CAACF,uDAAoC,CAAA,GAAI,UAAU,CAACE,sDAAmC,CAAC;AACxG;;AAEA;AACA,EAAE,kBAAkB,CAAC,UAAU,EAAEC,+CAA4B,EAAE,yBAAyB,CAAC;AACzF,EAAE,kBAAkB,CAAC,UAAU,EAAEC,6CAA0B,EAAE,sBAAsB,CAAC;AACpF,EAAE,kBAAkB,CAAC,UAAU,EAAEC,mDAAgC,EAAE,4BAA4B,CAAC;AAChG,EAAE,kBAAkB,CAAC,UAAU,EAAEC,4CAAyB,EAAE,gCAAgC,CAAC;AAC7F;;AAEA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,UAAU,EAA2B,MAAM,EAAU,MAAM,EAAgB;AACvG,EAAE,IAAI,UAAU,CAAC,MAAM,CAAE,IAAG,IAAI,EAAE;AAClC,IAAI,UAAU,CAAC,MAAM,CAAA,GAAI,UAAU,CAAC,MAAM,CAAC;AAC3C;AACA,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC;AAC7B;AACA;;AAEA,SAAS,mBAAmB,CAAC,IAAI,EAAQ,UAAU,EAAwB;AAC3E,EAAE,eAAe,CAAC,IAAI,EAAE,oBAAoB,CAAC;AAC7C,EAAE,IAAI,CAAC,YAAY,CAACC,+CAA4B,EAAE,qBAAqB,CAAC;AACxE,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,UAAU,CAACX,4CAAyB,CAAC,CAAC;AACjF,EAAE,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,UAAU,CAACD,8CAA2B,CAAC,CAAC;AAChF,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,aAAa,EAAE,UAAU,CAACA,8CAA2B,CAAC,CAAC,CAAA,CAAA;AACA;;AAEA,SAAA,mBAAA,CAAA,IAAA,EAAA,IAAA,EAAA,UAAA,EAAA;AACA,EAAA,eAAA,CAAA,IAAA,EAAA,oBAAA,CAAA;;AAEA,EAAA,MAAA,YAAA,GAAA,IAAA,CAAA,OAAA,CAAA,KAAA,EAAA,EAAA,CAAA;AACA,EAAA,IAAA,CAAA,YAAA,CAAA,kBAAA,EAAA,YAAA,CAAA;AACA,EAAA,IAAA,CAAA,UAAA,CAAA,YAAA,CAAA;;AAEA;AACA,EAAA,MAAA,UAAA,GAAA,UAAA,CAAAa,qDAAA,CAAA;AACA,EAAA,IAAA,UAAA,IAAA,OAAA,UAAA,KAAA,QAAA,IAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,MAAA,GAAA,CAAA,KAAA,CAAA,EAAA;AACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAA,kBAAA,EAAA,UAAA,CAAA;AACA;;AAEA,EAAA,IAAA,UAAA,CAAAC,sCAAA,CAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAA,eAAA,EAAA,UAAA,CAAAA,sCAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,UAAA,CAAAZ,wCAAA,CAAA,IAAA,CAAA,UAAA,CAAAa,kDAAA,CAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAA,kDAAA,EAAA,UAAA,CAAAb,wCAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,YAAA,CAAA,cAAA,EAAA,IAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA;;AAEA;AACA,EAAA,IAAA,IAAA,KAAA,iBAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAU,+CAAA,EAAA,qBAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,4BAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAA,+CAAA,EAAA,sBAAA,CAAA;AACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,cAAA,EAAA,UAAA,CAAAV,wCAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,eAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAU,+CAAA,EAAA,qBAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,wBAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAA,+CAAA,EAAA,oBAAA,CAAA;AACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,YAAA,EAAA,UAAA,CAAAV,wCAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,mBAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAU,+CAAA,EAAA,qBAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,8BAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAA,+CAAA,EAAA,wBAAA,CAAA;AACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,gBAAA,EAAA,UAAA,CAAAV,wCAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,iBAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAU,+CAAA,EAAA,qBAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,0BAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAA,+CAAA,EAAA,sBAAA,CAAA;AACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,cAAA,EAAA,UAAA,CAAAV,wCAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,UAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAU,+CAAA,EAAA,qBAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,kBAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAA,+CAAA,EAAA,cAAA,CAAA;AACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,MAAA,EAAA,UAAA,CAAAV,wCAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,cAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAU,+CAAA,EAAA,qBAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,KAAA,sBAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAA,+CAAA,EAAA,mBAAA,CAAA;AACA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA,WAAA,EAAA,UAAA,CAAAV,wCAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA;AACA;;AAEA,EAAA,IAAA,IAAA,CAAA,UAAA,CAAA,WAAA,CAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAAU,+CAAA,EAAA,QAAA,CAAA;AACA,IAAA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,MAAA,EAAA;AACA,EAAA,MAAA,CAAA,EAAA,CAAA,WAAA,EAAA,mBAAA,CAAA;AACA;AACA,EAAA,MAAA,CAAA,iBAAA,CAAA,sBAAA,CAAA;AACA;;;;"}