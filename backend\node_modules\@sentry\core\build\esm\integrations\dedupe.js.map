{"version": 3, "file": "dedupe.js", "sources": ["../../../src/integrations/dedupe.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport { defineIntegration } from '../integration';\nimport type { Event } from '../types-hoist/event';\nimport type { Exception } from '../types-hoist/exception';\nimport type { IntegrationFn } from '../types-hoist/integration';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport { logger } from '../utils/logger';\nimport { getFramesFromEvent } from '../utils/stacktrace';\n\nconst INTEGRATION_NAME = 'Dedupe';\n\nconst _dedupeIntegration = (() => {\n  let previousEvent: Event | undefined;\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(currentEvent) {\n      // We want to ignore any non-error type events, e.g. transactions or replays\n      // These should never be deduped, and also not be compared against as _previousEvent.\n      if (currentEvent.type) {\n        return currentEvent;\n      }\n\n      // Juuust in case something goes wrong\n      try {\n        if (_shouldDropEvent(currentEvent, previousEvent)) {\n          DEBUG_BUILD && logger.warn('Event dropped due to being a duplicate of previously captured event.');\n          return null;\n        }\n      } catch (_oO) {} // eslint-disable-line no-empty\n\n      return (previousEvent = currentEvent);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Deduplication filter.\n */\nexport const dedupeIntegration = defineIntegration(_dedupeIntegration);\n\n/** only exported for tests. */\nexport function _shouldDropEvent(currentEvent: Event, previousEvent?: Event): boolean {\n  if (!previousEvent) {\n    return false;\n  }\n\n  if (_isSameMessageEvent(currentEvent, previousEvent)) {\n    return true;\n  }\n\n  if (_isSameExceptionEvent(currentEvent, previousEvent)) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction _isSameMessageEvent(currentEvent: Event, previousEvent: Event): boolean {\n  const currentMessage = currentEvent.message;\n  const previousMessage = previousEvent.message;\n\n  // If neither event has a message property, they were both exceptions, so bail out\n  if (!currentMessage && !previousMessage) {\n    return false;\n  }\n\n  // If only one event has a stacktrace, but not the other one, they are not the same\n  if ((currentMessage && !previousMessage) || (!currentMessage && previousMessage)) {\n    return false;\n  }\n\n  if (currentMessage !== previousMessage) {\n    return false;\n  }\n\n  if (!_isSameFingerprint(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  if (!_isSameStacktrace(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction _isSameExceptionEvent(currentEvent: Event, previousEvent: Event): boolean {\n  const previousException = _getExceptionFromEvent(previousEvent);\n  const currentException = _getExceptionFromEvent(currentEvent);\n\n  if (!previousException || !currentException) {\n    return false;\n  }\n\n  if (previousException.type !== currentException.type || previousException.value !== currentException.value) {\n    return false;\n  }\n\n  if (!_isSameFingerprint(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  if (!_isSameStacktrace(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction _isSameStacktrace(currentEvent: Event, previousEvent: Event): boolean {\n  let currentFrames = getFramesFromEvent(currentEvent);\n  let previousFrames = getFramesFromEvent(previousEvent);\n\n  // If neither event has a stacktrace, they are assumed to be the same\n  if (!currentFrames && !previousFrames) {\n    return true;\n  }\n\n  // If only one event has a stacktrace, but not the other one, they are not the same\n  if ((currentFrames && !previousFrames) || (!currentFrames && previousFrames)) {\n    return false;\n  }\n\n  currentFrames = currentFrames as StackFrame[];\n  previousFrames = previousFrames as StackFrame[];\n\n  // If number of frames differ, they are not the same\n  if (previousFrames.length !== currentFrames.length) {\n    return false;\n  }\n\n  // Otherwise, compare the two\n  for (let i = 0; i < previousFrames.length; i++) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const frameA = previousFrames[i]!;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const frameB = currentFrames[i]!;\n\n    if (\n      frameA.filename !== frameB.filename ||\n      frameA.lineno !== frameB.lineno ||\n      frameA.colno !== frameB.colno ||\n      frameA.function !== frameB.function\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction _isSameFingerprint(currentEvent: Event, previousEvent: Event): boolean {\n  let currentFingerprint = currentEvent.fingerprint;\n  let previousFingerprint = previousEvent.fingerprint;\n\n  // If neither event has a fingerprint, they are assumed to be the same\n  if (!currentFingerprint && !previousFingerprint) {\n    return true;\n  }\n\n  // If only one event has a fingerprint, but not the other one, they are not the same\n  if ((currentFingerprint && !previousFingerprint) || (!currentFingerprint && previousFingerprint)) {\n    return false;\n  }\n\n  currentFingerprint = currentFingerprint as string[];\n  previousFingerprint = previousFingerprint as string[];\n\n  // Otherwise, compare the two\n  try {\n    return !!(currentFingerprint.join('') === previousFingerprint.join(''));\n  } catch (_oO) {\n    return false;\n  }\n}\n\nfunction _getExceptionFromEvent(event: Event): Exception | undefined {\n  return event.exception?.values && event.exception.values[0];\n}\n"], "names": [], "mappings": ";;;;;AASA,MAAM,gBAAA,GAAmB,QAAQ;;AAEjC,MAAM,kBAAmB,IAAG,MAAM;AAClC,EAAE,IAAI,aAAa;;AAEnB,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,YAAY,CAAC,YAAY,EAAE;AAC/B;AACA;AACA,MAAM,IAAI,YAAY,CAAC,IAAI,EAAE;AAC7B,QAAQ,OAAO,YAAY;AAC3B;;AAEA;AACA,MAAM,IAAI;AACV,QAAQ,IAAI,gBAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AAC3D,UAAU,eAAe,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC;AAC5G,UAAU,OAAO,IAAI;AACrB;AACA,OAAQ,CAAA,OAAO,GAAG,EAAE,EAAC;;AAErB,MAAM,QAAQ,aAAc,GAAE,YAAY;AAC1C,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;MACa,iBAAkB,GAAE,iBAAiB,CAAC,kBAAkB;;AAErE;AACO,SAAS,gBAAgB,CAAC,YAAY,EAAS,aAAa,EAAmB;AACtF,EAAE,IAAI,CAAC,aAAa,EAAE;AACtB,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI,mBAAmB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACxD,IAAI,OAAO,IAAI;AACf;;AAEA,EAAE,IAAI,qBAAqB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AAC1D,IAAI,OAAO,IAAI;AACf;;AAEA,EAAE,OAAO,KAAK;AACd;;AAEA,SAAS,mBAAmB,CAAC,YAAY,EAAS,aAAa,EAAkB;AACjF,EAAE,MAAM,cAAA,GAAiB,YAAY,CAAC,OAAO;AAC7C,EAAE,MAAM,eAAA,GAAkB,aAAa,CAAC,OAAO;;AAE/C;AACA,EAAE,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE;AAC3C,IAAI,OAAO,KAAK;AAChB;;AAEA;AACA,EAAE,IAAI,CAAC,cAAA,IAAkB,CAAC,eAAe,MAAM,CAAC,cAAA,IAAkB,eAAe,CAAC,EAAE;AACpF,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI,cAAe,KAAI,eAAe,EAAE;AAC1C,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACxD,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACvD,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,OAAO,IAAI;AACb;;AAEA,SAAS,qBAAqB,CAAC,YAAY,EAAS,aAAa,EAAkB;AACnF,EAAE,MAAM,iBAAkB,GAAE,sBAAsB,CAAC,aAAa,CAAC;AACjE,EAAE,MAAM,gBAAiB,GAAE,sBAAsB,CAAC,YAAY,CAAC;;AAE/D,EAAE,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE;AAC/C,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI,iBAAiB,CAAC,IAAA,KAAS,gBAAgB,CAAC,IAAK,IAAG,iBAAiB,CAAC,KAAA,KAAU,gBAAgB,CAAC,KAAK,EAAE;AAC9G,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACxD,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACvD,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,OAAO,IAAI;AACb;;AAEA,SAAS,iBAAiB,CAAC,YAAY,EAAS,aAAa,EAAkB;AAC/E,EAAE,IAAI,aAAc,GAAE,kBAAkB,CAAC,YAAY,CAAC;AACtD,EAAE,IAAI,cAAe,GAAE,kBAAkB,CAAC,aAAa,CAAC;;AAExD;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE;AACzC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,EAAE,IAAI,CAAC,aAAA,IAAiB,CAAC,cAAc,MAAM,CAAC,aAAA,IAAiB,cAAc,CAAC,EAAE;AAChF,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,aAAA,GAAgB,aAAc;AAChC,EAAE,cAAA,GAAiB,cAAe;;AAElC;AACA,EAAE,IAAI,cAAc,CAAC,WAAW,aAAa,CAAC,MAAM,EAAE;AACtD,IAAI,OAAO,KAAK;AAChB;;AAEA;AACA,EAAE,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAE,GAAE,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD;AACA,IAAI,MAAM,MAAO,GAAE,cAAc,CAAC,CAAC,CAAC;AACpC;AACA,IAAI,MAAM,MAAO,GAAE,aAAa,CAAC,CAAC,CAAC;;AAEnC,IAAI;AACJ,MAAM,MAAM,CAAC,QAAA,KAAa,MAAM,CAAC,QAAS;AAC1C,MAAM,MAAM,CAAC,MAAA,KAAW,MAAM,CAAC,MAAO;AACtC,MAAM,MAAM,CAAC,KAAA,KAAU,MAAM,CAAC,KAAM;AACpC,MAAM,MAAM,CAAC,QAAS,KAAI,MAAM,CAAC;AACjC,MAAM;AACN,MAAM,OAAO,KAAK;AAClB;AACA;;AAEA,EAAE,OAAO,IAAI;AACb;;AAEA,SAAS,kBAAkB,CAAC,YAAY,EAAS,aAAa,EAAkB;AAChF,EAAE,IAAI,kBAAA,GAAqB,YAAY,CAAC,WAAW;AACnD,EAAE,IAAI,mBAAA,GAAsB,aAAa,CAAC,WAAW;;AAErD;AACA,EAAE,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,EAAE;AACnD,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,EAAE,IAAI,CAAC,kBAAA,IAAsB,CAAC,mBAAmB,MAAM,CAAC,kBAAA,IAAsB,mBAAmB,CAAC,EAAE;AACpG,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,kBAAA,GAAqB,kBAAmB;AAC1C,EAAE,mBAAA,GAAsB,mBAAoB;;AAE5C;AACA,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAA,KAAM,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3E,GAAI,CAAA,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,KAAK;AAChB;AACA;;AAEA,SAAS,sBAAsB,CAAC,KAAK,EAAgC;AACrE,EAAE,OAAO,KAAK,CAAC,SAAS,EAAE,MAAO,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D;;;;"}