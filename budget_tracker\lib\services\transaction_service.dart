import 'dart:convert';
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import 'package:hive/hive.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/transaction_model.dart';
import '../constants/app_constants.dart';
import 'auth_service.dart';

class TransactionService {
  final Box<Transaction> _transactionBox = Hive.box<Transaction>(
    'transactions',
  );
  final Connectivity _connectivity = Connectivity();
  final AuthService _authService = AuthService();

  // Get all transactions with optional filters
  Future<List<Transaction>> getTransactions({
    String? type,
    String? category,
    String? startDate,
    String? endDate,
  }) async {
    final connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult.contains(ConnectivityResult.none)) {
      // Offline: Fetch from local DB. Note: Filtering is not applied offline in this version.
      developer.log('Fetching transactions from local Hive box');
      return _transactionBox.values.toList();
    }

    // Online: Fetch from API and cache
    try {
      developer.log('Fetching transactions from API');
      final headers = await _getAuthHeaders();

      // Build query parameters
      final queryParams = <String, String>{};
      if (type != null) queryParams['type'] = type;
      if (category != null) queryParams['category'] = category;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      final uri = Uri.parse(
        AppConstants.transactionsEndpoint,
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final List<dynamic> transactionsData = responseData['data'];

        final transactions = transactionsData
            .map((data) => Transaction.fromJson(data))
            .toList();

        // Cache the data locally for offline use
        await _transactionBox.clear();
        final Map<String, Transaction> transactionMap = {
          for (var t in transactions) t.id!: t,
        };
        await _transactionBox.putAll(transactionMap);
        developer.log('${transactionMap.length} transactions cached locally.');

        return transactions;
      } else {
        throw Exception('Failed to load transactions');
      }
    } catch (e) {
      // If API fails, fallback to local data if available
      developer.log('API fetch failed, falling back to local cache. Error: $e');
      return _transactionBox.values.toList();
    }
  }

  // Get a single transaction by ID
  Future<Transaction> getTransaction(String id) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${AppConstants.transactionsEndpoint}/$id'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      return Transaction.fromJson(responseData['data']);
    } else {
      throw Exception('Failed to load transaction');
    }
  }

  // Create a new transaction
  Future<Transaction> createTransaction(Transaction transaction) async {
    final connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult.contains(ConnectivityResult.none)) {
      throw Exception(
        'You are offline. Please connect to the internet to create a transaction.',
      );
    }
    final headers = await _getAuthHeaders();
    final response = await http.post(
      Uri.parse(AppConstants.transactionsEndpoint),
      headers: headers,
      body: jsonEncode(transaction.toJson()),
    );

    if (response.statusCode == 201) {
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      return Transaction.fromJson(responseData['data']);
    } else {
      throw Exception('Failed to create transaction');
    }
  }

  // Update an existing transaction
  Future<Transaction> updateTransaction(
    String id,
    Transaction transaction,
  ) async {
    final connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult.contains(ConnectivityResult.none)) {
      throw Exception(
        'You are offline. Please connect to the internet to update a transaction.',
      );
    }
    final headers = await _getAuthHeaders();
    final response = await http.put(
      Uri.parse('${AppConstants.transactionsEndpoint}/$id'),
      headers: headers,
      body: jsonEncode(transaction.toJson()),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      return Transaction.fromJson(responseData['data']);
    } else {
      throw Exception('Failed to update transaction');
    }
  }

  // Delete a transaction
  Future<void> deleteTransaction(String id) async {
    final connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult.contains(ConnectivityResult.none)) {
      throw Exception(
        'You are offline. Please connect to the internet to delete a transaction.',
      );
    }
    final headers = await _getAuthHeaders();
    final response = await http.delete(
      Uri.parse('${AppConstants.transactionsEndpoint}/$id'),
      headers: headers,
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to delete transaction');
    }
  }

  // Get transaction statistics
  Future<Map<String, dynamic>> getTransactionStats({
    required String startDate,
    required String endDate,
  }) async {
    final headers = await _getAuthHeaders();

    final queryParams = <String, String>{
      'startDate': startDate,
      'endDate': endDate,
    };

    final uri = Uri.parse(
      AppConstants.transactionStatsEndpoint,
    ).replace(queryParameters: queryParams);

    final response = await http.get(uri, headers: headers);

    if (response.statusCode == 200) {
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      return responseData['data'];
    } else {
      throw Exception('Failed to load transaction statistics');
    }
  }

  // Get auth headers
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await _authService.getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }
}
