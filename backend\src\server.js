const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const morgan = require('morgan');
const dotenv = require('dotenv');
const errorHandler = require('./middleware/errorHandler');
const initSentry = require('./config/sentry');

// Import rate limiter
const limiter = require('./middleware/rateLimiter');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Initialize Sentry
if (process.env.NODE_ENV === 'production') {
  initSentry(app);
}

// Configure CORS based on environment
const getCorsOptions = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const allowedOrigins = isProduction
    ? [
        'https://your-flutter-app-domain.com', // Production domain
        'https://www.your-flutter-app-domain.com' // WWW domain
      ]
    : [
        'http://localhost:3000', // Strict dev port
        'http://127.0.0.1:3000'  // Localhost alternative
      ];

  return {
    origin: (origin, callback) => {
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept'
    ],
    credentials: true,
    optionsSuccessStatus: 204,
    maxAge: 86400, // 24 hour preflight cache
    preflightContinue: false
  };
};

// Security headers middleware
const securityHeaders = (req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'same-origin');
  next();
};

// Middleware
app.use(morgan('dev'));
app.use(express.json());
app.use(securityHeaders);
app.use(cors(getCorsOptions()));
app.use(limiter);

// Define routes
app.use('/api/auth', require('./routes/auth.routes'));
app.use('/api/transactions', require('./routes/transaction.routes'));
app.use('/api/budgets', require('./routes/budget.routes'));
app.use('/api/categories', require('./routes/category.routes'));
app.use('/api', require('./routes/system.routes'));

// Base route for API health check
app.get('/', (req, res) => {
  res.json({ message: 'Budget Tracker API is running' });
});

// Error handler must be after all other middleware/routes
app.use(errorHandler);

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
  })
  .catch((err) => {
    console.error('MongoDB connection error:', err);
  });

// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
