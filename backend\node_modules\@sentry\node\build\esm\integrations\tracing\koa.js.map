{"version": 3, "file": "koa.js", "sources": ["../../../../src/integrations/tracing/koa.ts"], "sourcesContent": ["import type { KoaInstrumentationConfig, KoaLayerType } from '@opentelemetry/instrumentation-koa';\nimport { KoaInstrumentation } from '@opentelemetry/instrumentation-koa';\nimport { ATTR_HTTP_ROUTE } from '@opentelemetry/semantic-conventions';\nimport type { IntegrationFn } from '@sentry/core';\nimport {\n  captureException,\n  defineIntegration,\n  getDefaultIsolationScope,\n  getIsolationScope,\n  logger,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  spanToJSON,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from '../../debug-build';\nimport { generateInstrumentOnce } from '../../otel/instrument';\nimport { addOriginToSpan } from '../../utils/addOriginToSpan';\nimport { ensureIsWrapped } from '../../utils/ensureIsWrapped';\n\ninterface KoaOptions {\n  /**\n   * Ignore layers of specified types\n   */\n  ignoreLayersType?: Array<'middleware' | 'router'>;\n}\n\nconst INTEGRATION_NAME = 'Koa';\n\nexport const instrumentKoa = generateInstrumentOnce(\n  INTEGRATION_NAME,\n  KoaInstrumentation,\n  (options: KoaOptions = {}) => {\n    return {\n      ignoreLayersType: options.ignoreLayersType as KoaLayerType[],\n      requestHook(span, info) {\n        addOriginToSpan(span, 'auto.http.otel.koa');\n\n        const attributes = spanToJSON(span).data;\n\n        // this is one of: middleware, router\n        const type = attributes['koa.type'];\n        if (type) {\n          span.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_OP, `${type}.koa`);\n        }\n\n        // Also update the name\n        const name = attributes['koa.name'];\n        if (typeof name === 'string') {\n          // Somehow, name is sometimes `''` for middleware spans\n          // See: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2220\n          span.updateName(name || '< unknown >');\n        }\n\n        if (getIsolationScope() === getDefaultIsolationScope()) {\n          DEBUG_BUILD && logger.warn('Isolation scope is default isolation scope - skipping setting transactionName');\n          return;\n        }\n        const route = attributes[ATTR_HTTP_ROUTE];\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        const method = info.context?.request?.method?.toUpperCase() || 'GET';\n        if (route) {\n          getIsolationScope().setTransactionName(`${method} ${route}`);\n        }\n      },\n    } satisfies KoaInstrumentationConfig;\n  },\n);\n\nconst _koaIntegration = ((options: KoaOptions = {}) => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      instrumentKoa(options);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for [Koa](https://koajs.com/).\n *\n * If you also want to capture errors, you need to call `setupKoaErrorHandler(app)` after you set up your Koa server.\n *\n * For more information, see the [koa documentation](https://docs.sentry.io/platforms/javascript/guides/koa/).\n *\n * @param {KoaOptions} options Configuration options for the Koa integration.\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [Sentry.koaIntegration()],\n * })\n * ```\n *\n * @example\n * ```javascript\n * // To ignore middleware spans\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *   integrations: [\n *     Sentry.koaIntegration({\n *       ignoreLayersType: ['middleware']\n *     })\n *   ],\n * })\n * ```\n */\nexport const koaIntegration = defineIntegration(_koaIntegration);\n\n/**\n * Add an Koa error handler to capture errors to Sentry.\n *\n * The error handler must be before any other middleware and after all controllers.\n *\n * @param app The Express instances\n * @param options {ExpressHandlerOptions} Configuration options for the handler\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n * const Koa = require(\"koa\");\n *\n * const app = new Koa();\n *\n * Sentry.setupKoaErrorHandler(app);\n *\n * // Add your routes, etc.\n *\n * app.listen(3000);\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const setupKoaErrorHandler = (app: { use: (arg0: (ctx: any, next: any) => Promise<void>) => void }): void => {\n  app.use(async (ctx, next) => {\n    try {\n      await next();\n    } catch (error) {\n      captureException(error);\n      throw error;\n    }\n  });\n\n  ensureIsWrapped(app.use, 'koa');\n};\n"], "names": [], "mappings": ";;;;;;;;AAyBA,MAAM,gBAAA,GAAmB,KAAK;;AAEvB,MAAM,aAAc,GAAE,sBAAsB;AACnD,EAAE,gBAAgB;AAClB,EAAE,kBAAkB;AACpB,EAAE,CAAC,OAAO,GAAe,EAAE,KAAK;AAChC,IAAI,OAAO;AACX,MAAM,gBAAgB,EAAE,OAAO,CAAC,gBAAiB;AACjD,MAAM,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC9B,QAAQ,eAAe,CAAC,IAAI,EAAE,oBAAoB,CAAC;;AAEnD,QAAQ,MAAM,aAAa,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI;;AAEhD;AACA,QAAQ,MAAM,IAAK,GAAE,UAAU,CAAC,UAAU,CAAC;AAC3C,QAAQ,IAAI,IAAI,EAAE;AAClB,UAAU,IAAI,CAAC,YAAY,CAAC,4BAA4B,EAAE,CAAC,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACA;;AAEA;AACA,QAAA,MAAA,IAAA,GAAA,UAAA,CAAA,UAAA,CAAA;AACA,QAAA,IAAA,OAAA,IAAA,KAAA,QAAA,EAAA;AACA;AACA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,IAAA,IAAA,aAAA,CAAA;AACA;;AAEA,QAAA,IAAA,iBAAA,EAAA,KAAA,wBAAA,EAAA,EAAA;AACA,UAAA,WAAA,IAAA,MAAA,CAAA,IAAA,CAAA,+EAAA,CAAA;AACA,UAAA;AACA;AACA,QAAA,MAAA,KAAA,GAAA,UAAA,CAAA,eAAA,CAAA;AACA;AACA,QAAA,MAAA,MAAA,GAAA,IAAA,CAAA,OAAA,EAAA,OAAA,EAAA,MAAA,EAAA,WAAA,EAAA,IAAA,KAAA;AACA,QAAA,IAAA,KAAA,EAAA;AACA,UAAA,iBAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACA;AACA,OAAA;AACA,KAAA;AACA,GAAA;AACA;;AAEA,MAAA,eAAA,IAAA,CAAA,OAAA,GAAA,EAAA,KAAA;AACA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,gBAAA;AACA,IAAA,SAAA,GAAA;AACA,MAAA,aAAA,CAAA,OAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,cAAA,GAAA,iBAAA,CAAA,eAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,oBAAA,GAAA,CAAA,GAAA,KAAA;AACA,EAAA,GAAA,CAAA,GAAA,CAAA,OAAA,GAAA,EAAA,IAAA,KAAA;AACA,IAAA,IAAA;AACA,MAAA,MAAA,IAAA,EAAA;AACA,KAAA,CAAA,OAAA,KAAA,EAAA;AACA,MAAA,gBAAA,CAAA,KAAA,CAAA;AACA,MAAA,MAAA,KAAA;AACA;AACA,GAAA,CAAA;;AAEA,EAAA,eAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA,CAAA;AACA;;;;"}