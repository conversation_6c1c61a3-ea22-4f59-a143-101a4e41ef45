{"version": 3, "file": "index.js", "sources": ["../../../../../src/integrations/tracing/vercelai/index.ts"], "sourcesContent": ["import type { Client, IntegrationFn } from '@sentry/core';\nimport { addVercelAiProcessors, defineIntegration } from '@sentry/core';\nimport { generateInstrumentOnce } from '../../../otel/instrument';\nimport type { modulesIntegration } from '../../modules';\nimport { INTEGRATION_NAME } from './constants';\nimport { SentryVercelAiInstrumentation } from './instrumentation';\nimport type { VercelAiOptions } from './types';\n\nexport const instrumentVercelAi = generateInstrumentOnce(INTEGRATION_NAME, () => new SentryVercelAiInstrumentation({}));\n\n/**\n * Determines if the integration should be forced based on environment and package availability.\n * Returns true if the 'ai' package is available.\n */\nfunction shouldForceIntegration(client: Client): boolean {\n  const modules = client.getIntegrationByName<ReturnType<typeof modulesIntegration>>('Modules');\n  return !!modules?.getModules?.()?.ai;\n}\n\nconst _vercelAIIntegration = ((options: VercelAiOptions = {}) => {\n  let instrumentation: undefined | SentryVercelAiInstrumentation;\n\n  return {\n    name: INTEGRATION_NAME,\n    options,\n    setupOnce() {\n      instrumentation = instrumentVercelAi();\n    },\n    afterAllSetup(client) {\n      // Auto-detect if we should force the integration when running with 'ai' package available\n      // Note that this can only be detected if the 'Modules' integration is available, and running in CJS mode\n      const shouldForce = options.force ?? shouldForceIntegration(client);\n\n      if (shouldForce) {\n        addVercelAiProcessors(client);\n      } else {\n        instrumentation?.callWhenPatched(() => addVercelAiProcessors(client));\n      }\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Adds Sentry tracing instrumentation for the [ai](https://www.npmjs.com/package/ai) library.\n * This integration is not enabled by default, you need to manually add it.\n *\n * For more information, see the [`ai` documentation](https://sdk.vercel.ai/docs/ai-sdk-core/telemetry).\n *\n * @example\n * ```javascript\n * const Sentry = require('@sentry/node');\n *\n * Sentry.init({\n *  integrations: [Sentry.vercelAIIntegration()],\n * });\n * ```\n *\n * This integration adds tracing support to all `ai` function calls.\n * You need to opt-in to collecting spans for a specific call,\n * you can do so by setting `experimental_telemetry.isEnabled` to `true` in the first argument of the function call.\n *\n * ```javascript\n * const result = await generateText({\n *   model: openai('gpt-4-turbo'),\n *   experimental_telemetry: { isEnabled: true },\n * });\n * ```\n *\n * If you want to collect inputs and outputs for a specific call, you must specifically opt-in to each\n * function call by setting `experimental_telemetry.recordInputs` and `experimental_telemetry.recordOutputs`\n * to `true`.\n *\n * ```javascript\n * const result = await generateText({\n *  model: openai('gpt-4-turbo'),\n *  experimental_telemetry: { isEnabled: true, recordInputs: true, recordOutputs: true },\n * });\n */\nexport const vercelAIIntegration = defineIntegration(_vercelAIIntegration);\n"], "names": ["generateInstrumentOnce", "INTEGRATION_NAME", "SentryVercelAiInstrumentation", "addVercelAiProcessors", "defineIntegration"], "mappings": ";;;;;;;MAQa,kBAAmB,GAAEA,iCAAsB,CAACC,0BAAgB,EAAE,MAAM,IAAIC,6CAA6B,CAAC,EAAE,CAAC;;AAEtH;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,MAAM,EAAmB;AACzD,EAAE,MAAM,UAAU,MAAM,CAAC,oBAAoB,CAAwC,SAAS,CAAC;AAC/F,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,EAAE;AACtC;;AAEA,MAAM,oBAAA,IAAwB,CAAC,OAAO,GAAoB,EAAE,KAAK;AACjE,EAAE,IAAI,eAAe;;AAErB,EAAE,OAAO;AACT,IAAI,IAAI,EAAED,0BAAgB;AAC1B,IAAI,OAAO;AACX,IAAI,SAAS,GAAG;AAChB,MAAM,eAAgB,GAAE,kBAAkB,EAAE;AAC5C,KAAK;AACL,IAAI,aAAa,CAAC,MAAM,EAAE;AAC1B;AACA;AACA,MAAM,MAAM,WAAY,GAAE,OAAO,CAAC,SAAS,sBAAsB,CAAC,MAAM,CAAC;;AAEzE,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQE,0BAAqB,CAAC,MAAM,CAAC;AACrC,aAAa;AACb,QAAQ,eAAe,EAAE,eAAe,CAAC,MAAMA,0BAAqB,CAAC,MAAM,CAAC,CAAC;AAC7E;AACA,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,mBAAoB,GAAEC,sBAAiB,CAAC,oBAAoB;;;;;"}