{"version": 3, "file": "traceData.js", "sources": ["../../../src/utils/traceData.ts"], "sourcesContent": ["import { getAsyncContextStrategy } from '../asyncContext';\nimport { getMainCarrier } from '../carrier';\nimport type { Client } from '../client';\nimport { getClient, getCurrentScope } from '../currentScopes';\nimport { isEnabled } from '../exports';\nimport type { Scope } from '../scope';\nimport { getDynamicSamplingContextFromScope, getDynamicSamplingContextFromSpan } from '../tracing';\nimport type { Span } from '../types-hoist/span';\nimport type { SerializedTraceData } from '../types-hoist/tracing';\nimport { dynamicSamplingContextToSentryBaggageHeader } from './baggage';\nimport { logger } from './logger';\nimport { getActiveSpan, spanToTraceHeader } from './spanUtils';\nimport { generateSentryTraceHeader, TRACEPARENT_REGEXP } from './tracing';\n\n/**\n * Extracts trace propagation data from the current span or from the client's scope (via transaction or propagation\n * context) and serializes it to `sentry-trace` and `baggage` values to strings. These values can be used to propagate\n * a trace via our tracing Http headers or Html `<meta>` tags.\n *\n * This function also applies some validation to the generated sentry-trace and baggage values to ensure that\n * only valid strings are returned.\n *\n * @returns an object with the tracing data values. The object keys are the name of the tracing key to be used as header\n * or meta tag name.\n */\nexport function getTraceData(options: { span?: Span; scope?: Scope; client?: Client } = {}): SerializedTraceData {\n  const client = options.client || getClient();\n  if (!isEnabled() || !client) {\n    return {};\n  }\n\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.getTraceData) {\n    return acs.getTraceData(options);\n  }\n\n  const scope = options.scope || getCurrentScope();\n  const span = options.span || getActiveSpan();\n  const sentryTrace = span ? spanToTraceHeader(span) : scopeToTraceHeader(scope);\n  const dsc = span ? getDynamicSamplingContextFromSpan(span) : getDynamicSamplingContextFromScope(client, scope);\n  const baggage = dynamicSamplingContextToSentryBaggageHeader(dsc);\n\n  const isValidSentryTraceHeader = TRACEPARENT_REGEXP.test(sentryTrace);\n  if (!isValidSentryTraceHeader) {\n    logger.warn('Invalid sentry-trace data. Cannot generate trace data');\n    return {};\n  }\n\n  return {\n    'sentry-trace': sentryTrace,\n    baggage,\n  };\n}\n\n/**\n * Get a sentry-trace header value for the given scope.\n */\nfunction scopeToTraceHeader(scope: Scope): string {\n  const { traceId, sampled, propagationSpanId } = scope.getPropagationContext();\n  return generateSentryTraceHeader(traceId, propagationSpanId, sampled);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,OAAO,GAAoD,EAAE,EAAuB;AACjH,EAAE,MAAM,SAAS,OAAO,CAAC,MAAO,IAAG,SAAS,EAAE;AAC9C,EAAE,IAAI,CAAC,SAAS,EAAG,IAAG,CAAC,MAAM,EAAE;AAC/B,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,MAAM,OAAA,GAAU,cAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAE,uBAAuB,CAAC,OAAO,CAAC;AAC9C,EAAE,IAAI,GAAG,CAAC,YAAY,EAAE;AACxB,IAAI,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC;AACpC;;AAEA,EAAE,MAAM,QAAQ,OAAO,CAAC,KAAM,IAAG,eAAe,EAAE;AAClD,EAAE,MAAM,OAAO,OAAO,CAAC,IAAK,IAAG,aAAa,EAAE;AAC9C,EAAE,MAAM,WAAA,GAAc,IAAA,GAAO,iBAAiB,CAAC,IAAI,CAAE,GAAE,kBAAkB,CAAC,KAAK,CAAC;AAChF,EAAE,MAAM,GAAA,GAAM,IAAA,GAAO,iCAAiC,CAAC,IAAI,CAAA,GAAI,kCAAkC,CAAC,MAAM,EAAE,KAAK,CAAC;AAChH,EAAE,MAAM,OAAQ,GAAE,2CAA2C,CAAC,GAAG,CAAC;;AAElE,EAAE,MAAM,2BAA2B,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC;AACvE,EAAE,IAAI,CAAC,wBAAwB,EAAE;AACjC,IAAI,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC;AACxE,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,OAAO;AACT,IAAI,cAAc,EAAE,WAAW;AAC/B,IAAI,OAAO;AACX,GAAG;AACH;;AAEA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAiB;AAClD,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAkB,EAAA,GAAI,KAAK,CAAC,qBAAqB,EAAE;AAC/E,EAAE,OAAO,yBAAyB,CAAC,OAAO,EAAE,iBAAiB,EAAE,OAAO,CAAC;AACvE;;;;"}