const express = require('express');
const { 
  createBudget, 
  getBudgets, 
  getBudget,
  updateBudget,
  deleteBudget,
  getBudgetStats
} = require('../controllers/budget.controller');
const { protect } = require('../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .get(getBudgets)
  .post(createBudget);

router.route('/stats')
  .get(getBudgetStats);
  
router.route('/:id')
  .get(getBudget)
  .put(updateBudget)
  .delete(deleteBudget);

module.exports = router;
