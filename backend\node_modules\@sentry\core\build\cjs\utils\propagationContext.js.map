{"version": 3, "file": "propagationContext.js", "sources": ["../../../src/utils/propagationContext.ts"], "sourcesContent": ["import { uuid4 } from './misc';\n\n/**\n * Generate a random, valid trace ID.\n */\nexport function generateTraceId(): string {\n  return uuid4();\n}\n\n/**\n * Generate a random, valid span ID.\n */\nexport function generateSpanId(): string {\n  return uuid4().substring(16);\n}\n"], "names": ["uuid4"], "mappings": ";;;;AAEA;AACA;AACA;AACO,SAAS,eAAe,GAAW;AAC1C,EAAE,OAAOA,UAAK,EAAE;AAChB;;AAEA;AACA;AACA;AACO,SAAS,cAAc,GAAW;AACzC,EAAE,OAAOA,UAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AAC9B;;;;;"}