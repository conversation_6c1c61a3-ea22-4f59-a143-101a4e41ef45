{"version": 3, "file": "node.js", "sources": ["../../../src/utils/node.ts"], "sourcesContent": ["/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n */\n\nimport { isBrowserBundle } from './env';\n\n/**\n * Checks whether we're in the Node.js or Browser environment\n *\n * @returns Answer to given question\n */\nexport function isNodeEnv(): boolean {\n  // explicitly check for browser bundles as those can be optimized statically\n  // by terser/rollup.\n  return (\n    !isBrowserBundle() &&\n    Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'\n  );\n}\n\n/**\n * Requires a module which is protected against bundler minification.\n *\n * @param request The module path to resolve\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction dynamicRequire(mod: any, request: string): any {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return mod.require(request);\n}\n\n/**\n * Helper for dynamically loading module that should work with linked dependencies.\n * The problem is that we _should_ be using `require(require.resolve(moduleName, { paths: [cwd()] }))`\n * However it's _not possible_ to do that with Webpack, as it has to know all the dependencies during\n * build time. `require.resolve` is also not available in any other way, so we cannot create,\n * a fake helper like we do with `dynamicRequire`.\n *\n * We always prefer to use local package, thus the value is not returned early from each `try/catch` block.\n * That is to mimic the behavior of `require.resolve` exactly.\n *\n * @param moduleName module name to require\n * @param existingModule module to use for requiring\n * @returns possibly required module\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function loadModule<T>(moduleName: string, existingModule: any = module): T | undefined {\n  let mod: T | undefined;\n\n  try {\n    mod = dynamicRequire(existingModule, moduleName);\n  } catch (e) {\n    // no-empty\n  }\n\n  if (!mod) {\n    try {\n      const { cwd } = dynamicRequire(existingModule, 'process');\n      mod = dynamicRequire(existingModule, `${cwd()}/node_modules/${moduleName}`) as T;\n    } catch (e) {\n      // no-empty\n    }\n  }\n\n  return mod;\n}\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;;;AAIA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,GAAY;AACrC;AACA;AACA,EAAE;AACF,IAAI,CAAC,eAAe,EAAG;AACvB,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,OAAA,KAAY,WAAY,GAAE,UAAU,CAAC,MAAM;AACrF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,GAAG,EAAO,OAAO,EAAe;AACxD;AACA,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAI,UAAU,EAAU,cAAc,GAAQ,MAAM,EAAiB;AAC/F,EAAE,IAAI,GAAG;;AAET,EAAE,IAAI;AACN,IAAI,MAAM,cAAc,CAAC,cAAc,EAAE,UAAU,CAAC;AACpD,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA;;AAEA,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,GAAA,EAAM,GAAE,cAAc,CAAC,cAAc,EAAE,SAAS,CAAC;AAC/D,MAAM,MAAM,cAAc,CAAC,cAAc,EAAE,CAAC,EAAA,GAAA,EAAA,CAAA,cAAA,EAAA,UAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA;AACA;;AAEA,EAAA,OAAA,GAAA;AACA;;;;"}