{"version": 3, "file": "profiling.js", "sources": ["../../src/profiling.ts"], "sourcesContent": ["import { getClient } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { Profiler, ProfilingIntegration } from './types-hoist/profiling';\nimport { logger } from './utils/logger';\n\nfunction isProfilingIntegrationWithProfiler(\n  integration: ProfilingIntegration<any> | undefined,\n): integration is ProfilingIntegration<any> {\n  return (\n    !!integration &&\n    typeof integration['_profiler'] !== 'undefined' &&\n    typeof integration['_profiler']['start'] === 'function' &&\n    typeof integration['_profiler']['stop'] === 'function'\n  );\n}\n/**\n * Starts the Sentry continuous profiler.\n * This mode is exclusive with the transaction profiler and will only work if the profilesSampleRate is set to a falsy value.\n * In continuous profiling mode, the profiler will keep reporting profile chunks to Sentry until it is stopped, which allows for continuous profiling of the application.\n */\nfunction startProfiler(): void {\n  const client = getClient();\n  if (!client) {\n    DEBUG_BUILD && logger.warn('No Sentry client available, profiling is not started');\n    return;\n  }\n\n  const integration = client.getIntegrationByName<ProfilingIntegration<any>>('ProfilingIntegration');\n\n  if (!integration) {\n    DEBUG_BUILD && logger.warn('ProfilingIntegration is not available');\n    return;\n  }\n\n  if (!isProfilingIntegrationWithProfiler(integration)) {\n    DEBUG_BUILD && logger.warn('Profiler is not available on profiling integration.');\n    return;\n  }\n\n  integration._profiler.start();\n}\n\n/**\n * Stops the Sentry continuous profiler.\n * Calls to stop will stop the profiler and flush the currently collected profile data to Sentry.\n */\nfunction stopProfiler(): void {\n  const client = getClient();\n  if (!client) {\n    DEBUG_BUILD && logger.warn('No Sentry client available, profiling is not started');\n    return;\n  }\n\n  const integration = client.getIntegrationByName<ProfilingIntegration<any>>('ProfilingIntegration');\n  if (!integration) {\n    DEBUG_BUILD && logger.warn('ProfilingIntegration is not available');\n    return;\n  }\n\n  if (!isProfilingIntegrationWithProfiler(integration)) {\n    DEBUG_BUILD && logger.warn('Profiler is not available on profiling integration.');\n    return;\n  }\n\n  integration._profiler.stop();\n}\n\nexport const profiler: Profiler = {\n  startProfiler,\n  stopProfiler,\n};\n"], "names": ["getClient", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;AAKA,SAAS,kCAAkC;AAC3C,EAAE,WAAW;AACb,EAA4C;AAC5C,EAAE;AACF,IAAI,CAAC,CAAC,WAAY;AAClB,IAAI,OAAO,WAAW,CAAC,WAAW,CAAA,KAAM,WAAY;AACpD,IAAI,OAAO,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAE,KAAI,UAAW;AAC5D,IAAI,OAAO,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,MAAM;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,GAAS;AAC/B,EAAE,MAAM,MAAA,GAASA,uBAAS,EAAE;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAIC,0BAAeC,aAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC;AACtF,IAAI;AACJ;;AAEA,EAAE,MAAM,cAAc,MAAM,CAAC,oBAAoB,CAA4B,sBAAsB,CAAC;;AAEpG,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAID,0BAAeC,aAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC;AACvE,IAAI;AACJ;;AAEA,EAAE,IAAI,CAAC,kCAAkC,CAAC,WAAW,CAAC,EAAE;AACxD,IAAID,0BAAeC,aAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC;AACrF,IAAI;AACJ;;AAEA,EAAE,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAAS,YAAY,GAAS;AAC9B,EAAE,MAAM,MAAA,GAASF,uBAAS,EAAE;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAIC,0BAAeC,aAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC;AACtF,IAAI;AACJ;;AAEA,EAAE,MAAM,cAAc,MAAM,CAAC,oBAAoB,CAA4B,sBAAsB,CAAC;AACpG,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAID,0BAAeC,aAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC;AACvE,IAAI;AACJ;;AAEA,EAAE,IAAI,CAAC,kCAAkC,CAAC,WAAW,CAAC,EAAE;AACxD,IAAID,0BAAeC,aAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC;AACrF,IAAI;AACJ;;AAEA,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE;AAC9B;;AAEO,MAAM,QAAQ,GAAa;AAClC,EAAE,aAAa;AACf,EAAE,YAAY;AACd;;;;"}