const Budget = require('../models/budget.model');
const Category = require('../models/category.model');
const Transaction = require('../models/transaction.model');

// @desc    Create a new budget
// @route   POST /api/budgets
// @access  Private
exports.createBudget = async (req, res) => {
  try {
    const { category, amount, month, year, notes } = req.body;

    // Validate category exists and belongs to the user
    const categoryExists = await Category.findOne({
      _id: category,
      user: req.user.id,
    });

    if (!categoryExists) {
      return res.status(404).json({
        success: false,
        message: 'Category not found',
      });
    }

    // Make sure category is for expenses (can't budget income)
    if (categoryExists.type !== 'expense') {
      return res.status(400).json({
        success: false,
        message: 'Budgets can only be set for expense categories',
      });
    }

    // Check if budget already exists for this category, month, and year
    const existingBudget = await Budget.findOne({
      user: req.user.id,
      category,
      month,
      year,
    });

    if (existingBudget) {
      return res.status(400).json({
        success: false,
        message: 'Budget already exists for this category in the specified month and year',
      });
    }

    // Create budget
    const budget = await Budget.create({
      user: req.user.id,
      category,
      amount,
      month,
      year,
      notes: notes || '',
    });

    // Populate category information
    const populatedBudget = await Budget.findById(budget._id).populate('category', 'name color icon');

    res.status(201).json({
      success: true,
      data: populatedBudget,
    });
  } catch (error) {
    console.error('Error in createBudget:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get all budgets for a user with filtering
// @route   GET /api/budgets
// @access  Private
exports.getBudgets = async (req, res) => {
  try {
    const { month, year, category } = req.query;

    // Build query
    const query = { user: req.user.id };

    // Add filters if provided
    if (month) query.month = parseInt(month);
    if (year) query.year = parseInt(year);
    if (category) query.category = category;

    // Execute query
    const budgets = await Budget.find(query)
      .populate('category', 'name color icon')
      .sort({ month: 1, year: 1 });

    res.status(200).json({
      success: true,
      count: budgets.length,
      data: budgets,
    });
  } catch (error) {
    console.error('Error in getBudgets:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get a single budget
// @route   GET /api/budgets/:id
// @access  Private
exports.getBudget = async (req, res) => {
  try {
    const budget = await Budget.findOne({
      _id: req.params.id,
      user: req.user.id,
    }).populate('category', 'name color icon');

    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found',
      });
    }

    res.status(200).json({
      success: true,
      data: budget,
    });
  } catch (error) {
    console.error('Error in getBudget:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update a budget
// @route   PUT /api/budgets/:id
// @access  Private
exports.updateBudget = async (req, res) => {
  try {
    // Make sure budget exists and belongs to user
    let budget = await Budget.findOne({
      _id: req.params.id,
      user: req.user.id,
    });

    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found',
      });
    }

    // If category is being updated, validate it
    if (req.body.category) {
      const category = await Category.findOne({
        _id: req.body.category,
        user: req.user.id,
      });

      if (!category) {
        return res.status(404).json({
          success: false,
          message: 'Category not found',
        });
      }

      // Make sure category is for expenses
      if (category.type !== 'expense') {
        return res.status(400).json({
          success: false,
          message: 'Budgets can only be set for expense categories',
        });
      }
    }

    // If month or year or category is being updated, check for duplicates
    if (req.body.month || req.body.year || req.body.category) {
      const month = req.body.month || budget.month;
      const year = req.body.year || budget.year;
      const category = req.body.category || budget.category;

      const existingBudget = await Budget.findOne({
        user: req.user.id,
        category,
        month,
        year,
        _id: { $ne: req.params.id },
      });

      if (existingBudget) {
        return res.status(400).json({
          success: false,
          message: 'Budget already exists for this category in the specified month and year',
        });
      }
    }

    // Update budget
    budget = await Budget.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('category', 'name color icon');

    res.status(200).json({
      success: true,
      data: budget,
    });
  } catch (error) {
    console.error('Error in updateBudget:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete a budget
// @route   DELETE /api/budgets/:id
// @access  Private
exports.deleteBudget = async (req, res) => {
  try {
    const budget = await Budget.findOne({
      _id: req.params.id,
      user: req.user.id,
    });

    if (!budget) {
      return res.status(404).json({
        success: false,
        message: 'Budget not found',
      });
    }

    await budget.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Error in deleteBudget:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get budget usage statistics
// @route   GET /api/budgets/stats
// @access  Private
exports.getBudgetStats = async (req, res) => {
  try {
    const { month, year } = req.query;

    if (!month || !year) {
      return res.status(400).json({
        success: false,
        message: 'Please provide month and year',
      });
    }

    // Get all budgets for the specified month and year
    const budgets = await Budget.find({
      user: req.user.id,
      month: parseInt(month),
      year: parseInt(year),
    }).populate('category', 'name color icon');

    // Get first and last day of the month
    const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
    const endDate = new Date(parseInt(year), parseInt(month), 0);

    // For each budget, calculate the usage
    const budgetStats = await Promise.all(
      budgets.map(async (budget) => {
        // Get all transactions for this category and date range
        const transactions = await Transaction.find({
          user: req.user.id,
          category: budget.category._id,
          type: 'expense',
          date: { $gte: startDate, $lte: endDate },
        });

        const totalSpent = transactions.reduce(
          (sum, transaction) => sum + transaction.amount,
          0
        );

        const remaining = budget.amount - totalSpent;
        const percentage = (totalSpent / budget.amount) * 100;

        return {
          budget: {
            id: budget._id,
            amount: budget.amount,
            category: budget.category,
          },
          usage: {
            spent: totalSpent,
            remaining,
            percentage: Math.min(percentage, 100), // Cap at 100%
            isOverBudget: totalSpent > budget.amount,
          },
        };
      })
    );

    // Calculate overall budget statistics
    const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);
    const totalSpent = budgetStats.reduce(
      (sum, stat) => sum + stat.usage.spent,
      0
    );
    const totalRemaining = totalBudget - totalSpent;
    const overallPercentage = (totalSpent / totalBudget) * 100;

    res.status(200).json({
      success: true,
      data: {
        budgetStats,
        overall: {
          totalBudget,
          totalSpent,
          totalRemaining,
          percentage: Math.min(overallPercentage, 100), // Cap at 100%
          isOverBudget: totalSpent > totalBudget,
        },
      },
    });
  } catch (error) {
    console.error('Error in getBudgetStats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
