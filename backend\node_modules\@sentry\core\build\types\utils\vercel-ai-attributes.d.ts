/**
 * AI SDK Telemetry Attributes
 * Based on https://ai-sdk.dev/docs/ai-sdk-core/telemetry#collected-data
 */
/**
 * Common attribute for operation name across all functions and spans
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#collected-data
 */
export declare const OPERATION_NAME_ATTRIBUTE = "operation.name";
/**
 * Common attribute for AI operation ID across all functions and spans
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#collected-data
 */
export declare const AI_OPERATION_ID_ATTRIBUTE = "ai.operationId";
/**
 * `generateText` function - `ai.generateText` span
 * `streamText` function - `ai.streamText` span
 *
 * The prompt that was used when calling the function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function
 */
export declare const AI_PROMPT_ATTRIBUTE = "ai.prompt";
/**
 * `generateObject` function - `ai.generateObject` span
 * `streamObject` function - `ai.streamObject` span
 *
 * The JSON schema version of the schema that was passed into the function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function
 */
export declare const AI_SCHEMA_ATTRIBUTE = "ai.schema";
/**
 * `generateObject` function - `ai.generateObject` span
 * `streamObject` function - `ai.streamObject` span
 *
 * The name of the schema that was passed into the function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function
 */
export declare const AI_SCHEMA_NAME_ATTRIBUTE = "ai.schema.name";
/**
 * `generateObject` function - `ai.generateObject` span
 * `streamObject` function - `ai.streamObject` span
 *
 * The description of the schema that was passed into the function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function
 */
export declare const AI_SCHEMA_DESCRIPTION_ATTRIBUTE = "ai.schema.description";
/**
 * `generateObject` function - `ai.generateObject` span
 * `streamObject` function - `ai.streamObject` span
 *
 * The object that was generated (stringified JSON)
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function
 */
export declare const AI_RESPONSE_OBJECT_ATTRIBUTE = "ai.response.object";
/**
 * `generateObject` function - `ai.generateObject` span
 * `streamObject` function - `ai.streamObject` span
 *
 * The object generation mode, e.g. `json`
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function
 */
export declare const AI_SETTINGS_MODE_ATTRIBUTE = "ai.settings.mode";
/**
 * `generateObject` function - `ai.generateObject` span
 * `streamObject` function - `ai.streamObject` span
 *
 * The output type that was used, e.g. `object` or `no-schema`
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function
 */
export declare const AI_SETTINGS_OUTPUT_ATTRIBUTE = "ai.settings.output";
/**
 * `embed` function - `ai.embed.doEmbed` span
 * `embedMany` function - `ai.embedMany` span
 *
 * The values that were passed into the function (array)
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embedmany-function
 */
export declare const AI_VALUES_ATTRIBUTE = "ai.values";
/**
 * `embed` function - `ai.embed.doEmbed` span
 * `embedMany` function - `ai.embedMany` span
 *
 * An array of JSON-stringified embeddings
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embedmany-function
 */
export declare const AI_EMBEDDINGS_ATTRIBUTE = "ai.embeddings";
/**
 * `generateText` function - `ai.generateText` span
 *
 * The text that was generated
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_RESPONSE_TEXT_ATTRIBUTE = "ai.response.text";
/**
 * `generateText` function - `ai.generateText` span
 *
 * The tool calls that were made as part of the generation (stringified JSON)
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_RESPONSE_TOOL_CALLS_ATTRIBUTE = "ai.response.toolCalls";
/**
 * `generateText` function - `ai.generateText` span
 *
 * The reason why the generation finished
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_RESPONSE_FINISH_REASON_ATTRIBUTE = "ai.response.finishReason";
/**
 * `generateText` function - `ai.generateText` span
 *
 * The maximum number of steps that were set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_SETTINGS_MAX_STEPS_ATTRIBUTE = "ai.settings.maxSteps";
/**
 * `generateText` function - `ai.generateText.doGenerate` span
 *
 * The format of the prompt
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_PROMPT_FORMAT_ATTRIBUTE = "ai.prompt.format";
/**
 * `generateText` function - `ai.generateText.doGenerate` span
 *
 * The messages that were passed into the provider
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_PROMPT_MESSAGES_ATTRIBUTE = "ai.prompt.messages";
/**
 * `generateText` function - `ai.generateText.doGenerate` span
 *
 * Array of stringified tool definitions
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_PROMPT_TOOLS_ATTRIBUTE = "ai.prompt.tools";
/**
 * `generateText` function - `ai.generateText.doGenerate` span
 *
 * The stringified tool choice setting (JSON)
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_PROMPT_TOOL_CHOICE_ATTRIBUTE = "ai.prompt.toolChoice";
/**
 * `streamText` function - `ai.streamText.doStream` span
 *
 * The time it took to receive the first chunk in milliseconds
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function
 */
export declare const AI_RESPONSE_MS_TO_FIRST_CHUNK_ATTRIBUTE = "ai.response.msToFirstChunk";
/**
 * `streamText` function - `ai.streamText.doStream` span
 *
 * The time it took to receive the finish part of the LLM stream in milliseconds
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function
 */
export declare const AI_RESPONSE_MS_TO_FINISH_ATTRIBUTE = "ai.response.msToFinish";
/**
 * `streamText` function - `ai.streamText.doStream` span
 *
 * The average completion tokens per second
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function
 */
export declare const AI_RESPONSE_AVG_COMPLETION_TOKENS_PER_SECOND_ATTRIBUTE = "ai.response.avgCompletionTokensPerSecond";
/**
 * `embed` function - `ai.embed` span
 *
 * The value that was passed into the `embed` function
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function
 */
export declare const AI_VALUE_ATTRIBUTE = "ai.value";
/**
 * `embed` function - `ai.embed` span
 *
 * A JSON-stringified embedding
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function
 */
export declare const AI_EMBEDDING_ATTRIBUTE = "ai.embedding";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The functionId that was set through `telemetry.functionId`
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const RESOURCE_NAME_ATTRIBUTE = "resource.name";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The id of the model
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_MODEL_ID_ATTRIBUTE = "ai.model.id";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The provider of the model
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_MODEL_PROVIDER_ATTRIBUTE = "ai.model.provider";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The request headers that were passed in through `headers`
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_REQUEST_HEADERS_ATTRIBUTE = "ai.request.headers";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The maximum number of retries that were set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_SETTINGS_MAX_RETRIES_ATTRIBUTE = "ai.settings.maxRetries";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The functionId that was set through `telemetry.functionId`
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_TELEMETRY_FUNCTION_ID_ATTRIBUTE = "ai.telemetry.functionId";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The metadata that was passed in through `telemetry.metadata`
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_TELEMETRY_METADATA_ATTRIBUTE = "ai.telemetry.metadata";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The number of completion tokens that were used
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_USAGE_COMPLETION_TOKENS_ATTRIBUTE = "ai.usage.completionTokens";
/**
 * Basic LLM span information
 * Multiple spans
 *
 * The number of prompt tokens that were used
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-llm-span-information
 */
export declare const AI_USAGE_PROMPT_TOKENS_ATTRIBUTE = "ai.usage.promptTokens";
/**
 * Call LLM span information
 * Individual LLM call spans
 *
 * The model that was used to generate the response
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const AI_RESPONSE_MODEL_ATTRIBUTE = "ai.response.model";
/**
 * Call LLM span information
 * Individual LLM call spans
 *
 * The id of the response
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const AI_RESPONSE_ID_ATTRIBUTE = "ai.response.id";
/**
 * Call LLM span information
 * Individual LLM call spans
 *
 * The timestamp of the response
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const AI_RESPONSE_TIMESTAMP_ATTRIBUTE = "ai.response.timestamp";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The provider that was used
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_SYSTEM_ATTRIBUTE = "gen_ai.system";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The model that was requested
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_MODEL_ATTRIBUTE = "gen_ai.request.model";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The temperature that was set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_TEMPERATURE_ATTRIBUTE = "gen_ai.request.temperature";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The maximum number of tokens that were set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_MAX_TOKENS_ATTRIBUTE = "gen_ai.request.max_tokens";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The frequency penalty that was set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_FREQUENCY_PENALTY_ATTRIBUTE = "gen_ai.request.frequency_penalty";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The presence penalty that was set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_PRESENCE_PENALTY_ATTRIBUTE = "gen_ai.request.presence_penalty";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The topK parameter value that was set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_TOP_K_ATTRIBUTE = "gen_ai.request.top_k";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The topP parameter value that was set
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_TOP_P_ATTRIBUTE = "gen_ai.request.top_p";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The stop sequences
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_REQUEST_STOP_SEQUENCES_ATTRIBUTE = "gen_ai.request.stop_sequences";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The finish reasons that were returned by the provider
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_RESPONSE_FINISH_REASONS_ATTRIBUTE = "gen_ai.response.finish_reasons";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The model that was used to generate the response
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_RESPONSE_MODEL_ATTRIBUTE = "gen_ai.response.model";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The id of the response
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_RESPONSE_ID_ATTRIBUTE = "gen_ai.response.id";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The number of prompt tokens that were used
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_USAGE_INPUT_TOKENS_ATTRIBUTE = "gen_ai.usage.input_tokens";
/**
 * Semantic Conventions for GenAI operations
 * Individual LLM call spans
 *
 * The number of completion tokens that were used
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#call-llm-span-information
 */
export declare const GEN_AI_USAGE_OUTPUT_TOKENS_ATTRIBUTE = "gen_ai.usage.output_tokens";
/**
 * Basic embedding span information
 * Embedding spans
 *
 * The number of tokens that were used
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#basic-embedding-span-information
 */
export declare const AI_USAGE_TOKENS_ATTRIBUTE = "ai.usage.tokens";
/**
 * Tool call spans
 * `ai.toolCall` span
 *
 * The name of the tool
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans
 */
export declare const AI_TOOL_CALL_NAME_ATTRIBUTE = "ai.toolCall.name";
/**
 * Tool call spans
 * `ai.toolCall` span
 *
 * The id of the tool call
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans
 */
export declare const AI_TOOL_CALL_ID_ATTRIBUTE = "ai.toolCall.id";
/**
 * Tool call spans
 * `ai.toolCall` span
 *
 * The parameters of the tool call
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans
 */
export declare const AI_TOOL_CALL_ARGS_ATTRIBUTE = "ai.toolCall.args";
/**
 * Tool call spans
 * `ai.toolCall` span
 *
 * The result of the tool call
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans
 */
export declare const AI_TOOL_CALL_RESULT_ATTRIBUTE = "ai.toolCall.result";
/**
 * Attributes collected for `ai.generateText` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_GENERATE_TEXT_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_PROMPT: "ai.prompt";
    readonly AI_RESPONSE_TEXT: "ai.response.text";
    readonly AI_RESPONSE_TOOL_CALLS: "ai.response.toolCalls";
    readonly AI_RESPONSE_FINISH_REASON: "ai.response.finishReason";
    readonly AI_SETTINGS_MAX_STEPS: "ai.settings.maxSteps";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_COMPLETION_TOKENS: "ai.usage.completionTokens";
    readonly AI_USAGE_PROMPT_TOKENS: "ai.usage.promptTokens";
};
/**
 * Attributes collected for `ai.generateText.doGenerate` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generatetext-function
 */
export declare const AI_GENERATE_TEXT_DO_GENERATE_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_PROMPT_FORMAT: "ai.prompt.format";
    readonly AI_PROMPT_MESSAGES: "ai.prompt.messages";
    readonly AI_PROMPT_TOOLS: "ai.prompt.tools";
    readonly AI_PROMPT_TOOL_CHOICE: "ai.prompt.toolChoice";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_COMPLETION_TOKENS: "ai.usage.completionTokens";
    readonly AI_USAGE_PROMPT_TOKENS: "ai.usage.promptTokens";
    readonly AI_RESPONSE_MODEL: "ai.response.model";
    readonly AI_RESPONSE_ID: "ai.response.id";
    readonly AI_RESPONSE_TIMESTAMP: "ai.response.timestamp";
    readonly GEN_AI_SYSTEM: "gen_ai.system";
    readonly GEN_AI_REQUEST_MODEL: "gen_ai.request.model";
    readonly GEN_AI_REQUEST_TEMPERATURE: "gen_ai.request.temperature";
    readonly GEN_AI_REQUEST_MAX_TOKENS: "gen_ai.request.max_tokens";
    readonly GEN_AI_REQUEST_FREQUENCY_PENALTY: "gen_ai.request.frequency_penalty";
    readonly GEN_AI_REQUEST_PRESENCE_PENALTY: "gen_ai.request.presence_penalty";
    readonly GEN_AI_REQUEST_TOP_K: "gen_ai.request.top_k";
    readonly GEN_AI_REQUEST_TOP_P: "gen_ai.request.top_p";
    readonly GEN_AI_REQUEST_STOP_SEQUENCES: "gen_ai.request.stop_sequences";
    readonly GEN_AI_RESPONSE_FINISH_REASONS: "gen_ai.response.finish_reasons";
    readonly GEN_AI_RESPONSE_MODEL: "gen_ai.response.model";
    readonly GEN_AI_RESPONSE_ID: "gen_ai.response.id";
    readonly GEN_AI_USAGE_INPUT_TOKENS: "gen_ai.usage.input_tokens";
    readonly GEN_AI_USAGE_OUTPUT_TOKENS: "gen_ai.usage.output_tokens";
};
/**
 * Attributes collected for `ai.streamText` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function
 */
export declare const AI_STREAM_TEXT_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_PROMPT: "ai.prompt";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_COMPLETION_TOKENS: "ai.usage.completionTokens";
    readonly AI_USAGE_PROMPT_TOKENS: "ai.usage.promptTokens";
};
/**
 * Attributes collected for `ai.streamText.doStream` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamtext-function
 */
export declare const AI_STREAM_TEXT_DO_STREAM_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_RESPONSE_MS_TO_FIRST_CHUNK: "ai.response.msToFirstChunk";
    readonly AI_RESPONSE_MS_TO_FINISH: "ai.response.msToFinish";
    readonly AI_RESPONSE_AVG_COMPLETION_TOKENS_PER_SECOND: "ai.response.avgCompletionTokensPerSecond";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_COMPLETION_TOKENS: "ai.usage.completionTokens";
    readonly AI_USAGE_PROMPT_TOKENS: "ai.usage.promptTokens";
    readonly AI_RESPONSE_MODEL: "ai.response.model";
    readonly AI_RESPONSE_ID: "ai.response.id";
    readonly AI_RESPONSE_TIMESTAMP: "ai.response.timestamp";
    readonly GEN_AI_SYSTEM: "gen_ai.system";
    readonly GEN_AI_REQUEST_MODEL: "gen_ai.request.model";
    readonly GEN_AI_REQUEST_TEMPERATURE: "gen_ai.request.temperature";
    readonly GEN_AI_REQUEST_MAX_TOKENS: "gen_ai.request.max_tokens";
    readonly GEN_AI_REQUEST_FREQUENCY_PENALTY: "gen_ai.request.frequency_penalty";
    readonly GEN_AI_REQUEST_PRESENCE_PENALTY: "gen_ai.request.presence_penalty";
    readonly GEN_AI_REQUEST_TOP_K: "gen_ai.request.top_k";
    readonly GEN_AI_REQUEST_TOP_P: "gen_ai.request.top_p";
    readonly GEN_AI_REQUEST_STOP_SEQUENCES: "gen_ai.request.stop_sequences";
    readonly GEN_AI_RESPONSE_FINISH_REASONS: "gen_ai.response.finish_reasons";
    readonly GEN_AI_RESPONSE_MODEL: "gen_ai.response.model";
    readonly GEN_AI_RESPONSE_ID: "gen_ai.response.id";
    readonly GEN_AI_USAGE_INPUT_TOKENS: "gen_ai.usage.input_tokens";
    readonly GEN_AI_USAGE_OUTPUT_TOKENS: "gen_ai.usage.output_tokens";
};
/**
 * Attributes collected for `ai.generateObject` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#generateobject-function
 */
export declare const AI_GENERATE_OBJECT_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_SCHEMA: "ai.schema";
    readonly AI_SCHEMA_NAME: "ai.schema.name";
    readonly AI_SCHEMA_DESCRIPTION: "ai.schema.description";
    readonly AI_RESPONSE_OBJECT: "ai.response.object";
    readonly AI_SETTINGS_MODE: "ai.settings.mode";
    readonly AI_SETTINGS_OUTPUT: "ai.settings.output";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_COMPLETION_TOKENS: "ai.usage.completionTokens";
    readonly AI_USAGE_PROMPT_TOKENS: "ai.usage.promptTokens";
};
/**
 * Attributes collected for `ai.streamObject` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#streamobject-function
 */
export declare const AI_STREAM_OBJECT_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_SCHEMA: "ai.schema";
    readonly AI_SCHEMA_NAME: "ai.schema.name";
    readonly AI_SCHEMA_DESCRIPTION: "ai.schema.description";
    readonly AI_RESPONSE_OBJECT: "ai.response.object";
    readonly AI_SETTINGS_MODE: "ai.settings.mode";
    readonly AI_SETTINGS_OUTPUT: "ai.settings.output";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_COMPLETION_TOKENS: "ai.usage.completionTokens";
    readonly AI_USAGE_PROMPT_TOKENS: "ai.usage.promptTokens";
};
/**
 * Attributes collected for `ai.embed` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function
 */
export declare const AI_EMBED_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_VALUE: "ai.value";
    readonly AI_EMBEDDING: "ai.embedding";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_TOKENS: "ai.usage.tokens";
};
/**
 * Attributes collected for `ai.embed.doEmbed` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embed-function
 */
export declare const AI_EMBED_DO_EMBED_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_VALUES: "ai.values";
    readonly AI_EMBEDDINGS: "ai.embeddings";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_TOKENS: "ai.usage.tokens";
};
/**
 * Attributes collected for `ai.embedMany` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#embedmany-function
 */
export declare const AI_EMBED_MANY_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_VALUES: "ai.values";
    readonly AI_EMBEDDINGS: "ai.embeddings";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
    readonly AI_USAGE_TOKENS: "ai.usage.tokens";
};
/**
 * Attributes collected for `ai.toolCall` span
 * @see https://ai-sdk.dev/docs/ai-sdk-core/telemetry#tool-call-spans
 */
export declare const AI_TOOL_CALL_SPAN_ATTRIBUTES: {
    readonly OPERATION_NAME: "operation.name";
    readonly AI_OPERATION_ID: "ai.operationId";
    readonly AI_TOOL_CALL_NAME: "ai.toolCall.name";
    readonly AI_TOOL_CALL_ID: "ai.toolCall.id";
    readonly AI_TOOL_CALL_ARGS: "ai.toolCall.args";
    readonly AI_TOOL_CALL_RESULT: "ai.toolCall.result";
    readonly RESOURCE_NAME: "resource.name";
    readonly AI_MODEL_ID: "ai.model.id";
    readonly AI_MODEL_PROVIDER: "ai.model.provider";
    readonly AI_REQUEST_HEADERS: "ai.request.headers";
    readonly AI_SETTINGS_MAX_RETRIES: "ai.settings.maxRetries";
    readonly AI_TELEMETRY_FUNCTION_ID: "ai.telemetry.functionId";
    readonly AI_TELEMETRY_METADATA: "ai.telemetry.metadata";
};
//# sourceMappingURL=vercel-ai-attributes.d.ts.map