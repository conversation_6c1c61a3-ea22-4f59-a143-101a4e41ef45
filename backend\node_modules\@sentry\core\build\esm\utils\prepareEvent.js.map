{"version": 3, "file": "prepareEvent.js", "sources": ["../../../src/utils/prepareEvent.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getGlobalScope } from '../currentScopes';\nimport { notifyEventProcessors } from '../eventProcessors';\nimport type { CaptureContext, ScopeContext } from '../scope';\nimport { Scope } from '../scope';\nimport type { Event, EventHint } from '../types-hoist/event';\nimport type { ClientOptions } from '../types-hoist/options';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { applyScopeDataToEvent, mergeScopeData } from './applyScopeDataToEvent';\nimport { getFilenameToDebugIdMap } from './debug-ids';\nimport { addExceptionMechanism, uuid4 } from './misc';\nimport { normalize } from './normalize';\nimport { truncate } from './string';\nimport { dateTimestampInSeconds } from './time';\n\n/**\n * This type makes sure that we get either a CaptureContext, OR an EventHint.\n * It does not allow mixing them, which could lead to unexpected outcomes, e.g. this is disallowed:\n * { user: { id: '123' }, mechanism: { handled: false } }\n */\nexport type ExclusiveEventHintOrCaptureContext =\n  | (CaptureContext & Partial<{ [key in keyof EventHint]: never }>)\n  | (EventHint & Partial<{ [key in keyof ScopeContext]: never }>);\n\n/**\n * Adds common information to events.\n *\n * The information includes release and environment from `options`,\n * breadcrumbs and context (extra, tags and user) from the scope.\n *\n * Information that is already present in the event is never overwritten. For\n * nested objects, such as the context, keys are merged.\n *\n * @param event The original event.\n * @param hint May contain additional information about the original exception.\n * @param scope A scope containing event metadata.\n * @returns A new event with more information.\n * @hidden\n */\nexport function prepareEvent(\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n  scope?: Scope,\n  client?: Client,\n  isolationScope?: Scope,\n): PromiseLike<Event | null> {\n  const { normalizeDepth = 3, normalizeMaxBreadth = 1_000 } = options;\n  const prepared: Event = {\n    ...event,\n    event_id: event.event_id || hint.event_id || uuid4(),\n    timestamp: event.timestamp || dateTimestampInSeconds(),\n  };\n  const integrations = hint.integrations || options.integrations.map(i => i.name);\n\n  applyClientOptions(prepared, options);\n  applyIntegrationsMetadata(prepared, integrations);\n\n  if (client) {\n    client.emit('applyFrameMetadata', event);\n  }\n\n  // Only put debug IDs onto frames for error events.\n  if (event.type === undefined) {\n    applyDebugIds(prepared, options.stackParser);\n  }\n\n  // If we have scope given to us, use it as the base for further modifications.\n  // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n  const finalScope = getFinalScope(scope, hint.captureContext);\n\n  if (hint.mechanism) {\n    addExceptionMechanism(prepared, hint.mechanism);\n  }\n\n  const clientEventProcessors = client ? client.getEventProcessors() : [];\n\n  // This should be the last thing called, since we want that\n  // {@link Scope.addEventProcessor} gets the finished prepared event.\n  // Merge scope data together\n  const data = getGlobalScope().getScopeData();\n\n  if (isolationScope) {\n    const isolationData = isolationScope.getScopeData();\n    mergeScopeData(data, isolationData);\n  }\n\n  if (finalScope) {\n    const finalScopeData = finalScope.getScopeData();\n    mergeScopeData(data, finalScopeData);\n  }\n\n  const attachments = [...(hint.attachments || []), ...data.attachments];\n  if (attachments.length) {\n    hint.attachments = attachments;\n  }\n\n  applyScopeDataToEvent(prepared, data);\n\n  const eventProcessors = [\n    ...clientEventProcessors,\n    // Run scope event processors _after_ all other processors\n    ...data.eventProcessors,\n  ];\n\n  const result = notifyEventProcessors(eventProcessors, prepared, hint);\n\n  return result.then(evt => {\n    if (evt) {\n      // We apply the debug_meta field only after all event processors have ran, so that if any event processors modified\n      // file names (e.g.the RewriteFrames integration) the filename -> debug ID relationship isn't destroyed.\n      // This should not cause any PII issues, since we're only moving data that is already on the event and not adding\n      // any new data\n      applyDebugMeta(evt);\n    }\n\n    if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n      return normalizeEvent(evt, normalizeDepth, normalizeMaxBreadth);\n    }\n    return evt;\n  });\n}\n\n/**\n * Enhances event using the client configuration.\n * It takes care of all \"static\" values like environment, release and `dist`,\n * as well as truncating overly long values.\n *\n * Only exported for tests.\n *\n * @param event event instance to be enhanced\n */\nexport function applyClientOptions(event: Event, options: ClientOptions): void {\n  const { environment, release, dist, maxValueLength = 250 } = options;\n\n  // empty strings do not make sense for environment, release, and dist\n  // so we handle them the same as if they were not provided\n  event.environment = event.environment || environment || DEFAULT_ENVIRONMENT;\n\n  if (!event.release && release) {\n    event.release = release;\n  }\n\n  if (!event.dist && dist) {\n    event.dist = dist;\n  }\n\n  const request = event.request;\n  if (request?.url) {\n    request.url = truncate(request.url, maxValueLength);\n  }\n}\n\n/**\n * Puts debug IDs into the stack frames of an error event.\n */\nexport function applyDebugIds(event: Event, stackParser: StackParser): void {\n  // Build a map of filename -> debug_id\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.filename) {\n        frame.debug_id = filenameDebugIdMap[frame.filename];\n      }\n    });\n  });\n}\n\n/**\n * Moves debug IDs from the stack frames of an error event into the debug_meta field.\n */\nexport function applyDebugMeta(event: Event): void {\n  // Extract debug IDs and filenames from the stack frames on the event.\n  const filenameDebugIdMap: Record<string, string> = {};\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.debug_id) {\n        if (frame.abs_path) {\n          filenameDebugIdMap[frame.abs_path] = frame.debug_id;\n        } else if (frame.filename) {\n          filenameDebugIdMap[frame.filename] = frame.debug_id;\n        }\n        delete frame.debug_id;\n      }\n    });\n  });\n\n  if (Object.keys(filenameDebugIdMap).length === 0) {\n    return;\n  }\n\n  // Fill debug_meta information\n  event.debug_meta = event.debug_meta || {};\n  event.debug_meta.images = event.debug_meta.images || [];\n  const images = event.debug_meta.images;\n  Object.entries(filenameDebugIdMap).forEach(([filename, debug_id]) => {\n    images.push({\n      type: 'sourcemap',\n      code_file: filename,\n      debug_id,\n    });\n  });\n}\n\n/**\n * This function adds all used integrations to the SDK info in the event.\n * @param event The event that will be filled with all integrations.\n */\nfunction applyIntegrationsMetadata(event: Event, integrationNames: string[]): void {\n  if (integrationNames.length > 0) {\n    event.sdk = event.sdk || {};\n    event.sdk.integrations = [...(event.sdk.integrations || []), ...integrationNames];\n  }\n}\n\n/**\n * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n * Normalized keys:\n * - `breadcrumbs.data`\n * - `user`\n * - `contexts`\n * - `extra`\n * @param event Event\n * @returns Normalized event\n */\nfunction normalizeEvent(event: Event | null, depth: number, maxBreadth: number): Event | null {\n  if (!event) {\n    return null;\n  }\n\n  const normalized: Event = {\n    ...event,\n    ...(event.breadcrumbs && {\n      breadcrumbs: event.breadcrumbs.map(b => ({\n        ...b,\n        ...(b.data && {\n          data: normalize(b.data, depth, maxBreadth),\n        }),\n      })),\n    }),\n    ...(event.user && {\n      user: normalize(event.user, depth, maxBreadth),\n    }),\n    ...(event.contexts && {\n      contexts: normalize(event.contexts, depth, maxBreadth),\n    }),\n    ...(event.extra && {\n      extra: normalize(event.extra, depth, maxBreadth),\n    }),\n  };\n\n  // event.contexts.trace stores information about a Transaction. Similarly,\n  // event.spans[] stores information about child Spans. Given that a\n  // Transaction is conceptually a Span, normalization should apply to both\n  // Transactions and Spans consistently.\n  // For now the decision is to skip normalization of Transactions and Spans,\n  // so this block overwrites the normalized event to add back the original\n  // Transaction information prior to normalization.\n  if (event.contexts?.trace && normalized.contexts) {\n    normalized.contexts.trace = event.contexts.trace;\n\n    // event.contexts.trace.data may contain circular/dangerous data so we need to normalize it\n    if (event.contexts.trace.data) {\n      normalized.contexts.trace.data = normalize(event.contexts.trace.data, depth, maxBreadth);\n    }\n  }\n\n  // event.spans[].data may contain circular/dangerous data so we need to normalize it\n  if (event.spans) {\n    normalized.spans = event.spans.map(span => {\n      return {\n        ...span,\n        ...(span.data && {\n          data: normalize(span.data, depth, maxBreadth),\n        }),\n      };\n    });\n  }\n\n  // event.contexts.flags (FeatureFlagContext) stores context for our feature\n  // flag integrations. It has a greater nesting depth than our other typed\n  // Contexts, so we re-normalize with a fixed depth of 3 here. We do not want\n  // to skip this in case of conflicting, user-provided context.\n  if (event.contexts?.flags && normalized.contexts) {\n    normalized.contexts.flags = normalize(event.contexts.flags, 3, maxBreadth);\n  }\n\n  return normalized;\n}\n\nfunction getFinalScope(scope: Scope | undefined, captureContext: CaptureContext | undefined): Scope | undefined {\n  if (!captureContext) {\n    return scope;\n  }\n\n  const finalScope = scope ? scope.clone() : new Scope();\n  finalScope.update(captureContext);\n  return finalScope;\n}\n\n/**\n * Parse either an `EventHint` directly, or convert a `CaptureContext` to an `EventHint`.\n * This is used to allow to update method signatures that used to accept a `CaptureContext` but should now accept an `EventHint`.\n */\nexport function parseEventHintOrCaptureContext(\n  hint: ExclusiveEventHintOrCaptureContext | undefined,\n): EventHint | undefined {\n  if (!hint) {\n    return undefined;\n  }\n\n  // If you pass a Scope or `() => Scope` as CaptureContext, we just return this as captureContext\n  if (hintIsScopeOrFunction(hint)) {\n    return { captureContext: hint };\n  }\n\n  if (hintIsScopeContext(hint)) {\n    return {\n      captureContext: hint,\n    };\n  }\n\n  return hint;\n}\n\nfunction hintIsScopeOrFunction(hint: CaptureContext | EventHint): hint is Scope | ((scope: Scope) => Scope) {\n  return hint instanceof Scope || typeof hint === 'function';\n}\n\ntype ScopeContextProperty = keyof ScopeContext;\nconst captureContextKeys: readonly ScopeContextProperty[] = [\n  'user',\n  'level',\n  'extra',\n  'contexts',\n  'tags',\n  'fingerprint',\n  'propagationContext',\n] as const;\n\nfunction hintIsScopeContext(hint: Partial<ScopeContext> | EventHint): hint is Partial<ScopeContext> {\n  return Object.keys(hint).some(key => captureContextKeys.includes(key as ScopeContextProperty));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY;AAC5B,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,cAAc;AAChB,EAA6B;AAC7B,EAAE,MAAM,EAAE,cAAA,GAAiB,CAAC,EAAE,mBAAA,GAAsB,IAAA,EAAQ,GAAE,OAAO;AACrE,EAAE,MAAM,QAAQ,GAAU;AAC1B,IAAI,GAAG,KAAK;AACZ,IAAI,QAAQ,EAAE,KAAK,CAAC,QAAS,IAAG,IAAI,CAAC,QAAS,IAAG,KAAK,EAAE;AACxD,IAAI,SAAS,EAAE,KAAK,CAAC,aAAa,sBAAsB,EAAE;AAC1D,GAAG;AACH,EAAE,MAAM,YAAa,GAAE,IAAI,CAAC,YAAA,IAAgB,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,IAAI,CAAC;;AAEjF,EAAE,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC;AACvC,EAAE,yBAAyB,CAAC,QAAQ,EAAE,YAAY,CAAC;;AAEnD,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,CAAC;AAC5C;;AAEA;AACA,EAAE,IAAI,KAAK,CAAC,IAAK,KAAI,SAAS,EAAE;AAChC,IAAI,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC;AAChD;;AAEA;AACA;AACA,EAAE,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC;;AAE9D,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;AACtB,IAAI,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC;AACnD;;AAEA,EAAE,MAAM,qBAAA,GAAwB,MAAA,GAAS,MAAM,CAAC,kBAAkB,EAAC,GAAI,EAAE;;AAEzE;AACA;AACA;AACA,EAAE,MAAM,OAAO,cAAc,EAAE,CAAC,YAAY,EAAE;;AAE9C,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,MAAM,aAAc,GAAE,cAAc,CAAC,YAAY,EAAE;AACvD,IAAI,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC;AACvC;;AAEA,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,MAAM,cAAe,GAAE,UAAU,CAAC,YAAY,EAAE;AACpD,IAAI,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC;AACxC;;AAEA,EAAE,MAAM,WAAY,GAAE,CAAC,IAAI,IAAI,CAAC,WAAA,IAAe,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;AACxE,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,WAAY,GAAE,WAAW;AAClC;;AAEA,EAAE,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC;;AAEvC,EAAE,MAAM,kBAAkB;AAC1B,IAAI,GAAG,qBAAqB;AAC5B;AACA,IAAI,GAAG,IAAI,CAAC,eAAe;AAC3B,GAAG;;AAEH,EAAE,MAAM,MAAO,GAAE,qBAAqB,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC;;AAEvE,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO;AAC5B,IAAI,IAAI,GAAG,EAAE;AACb;AACA;AACA;AACA;AACA,MAAM,cAAc,CAAC,GAAG,CAAC;AACzB;;AAEA,IAAI,IAAI,OAAO,cAAe,KAAI,YAAY,cAAA,GAAiB,CAAC,EAAE;AAClE,MAAM,OAAO,cAAc,CAAC,GAAG,EAAE,cAAc,EAAE,mBAAmB,CAAC;AACrE;AACA,IAAI,OAAO,GAAG;AACd,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,KAAK,EAAS,OAAO,EAAuB;AAC/E,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,cAAe,GAAE,GAAI,EAAA,GAAI,OAAO;;AAEtE;AACA;AACA,EAAE,KAAK,CAAC,WAAA,GAAc,KAAK,CAAC,WAAY,IAAG,WAAY,IAAG,mBAAmB;;AAE7E,EAAE,IAAI,CAAC,KAAK,CAAC,OAAQ,IAAG,OAAO,EAAE;AACjC,IAAI,KAAK,CAAC,OAAQ,GAAE,OAAO;AAC3B;;AAEA,EAAE,IAAI,CAAC,KAAK,CAAC,IAAK,IAAG,IAAI,EAAE;AAC3B,IAAI,KAAK,CAAC,IAAK,GAAE,IAAI;AACrB;;AAEA,EAAE,MAAM,OAAA,GAAU,KAAK,CAAC,OAAO;AAC/B,EAAE,IAAI,OAAO,EAAE,GAAG,EAAE;AACpB,IAAI,OAAO,CAAC,GAAA,GAAM,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC;AACvD;AACA;;AAEA;AACA;AACA;AACO,SAAS,aAAa,CAAC,KAAK,EAAS,WAAW,EAAqB;AAC5E;AACA,EAAE,MAAM,kBAAmB,GAAE,uBAAuB,CAAC,WAAW,CAAC;;AAEjE,EAAE,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,SAAA,IAAa;AAChD,IAAI,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,KAAA,IAAS;AACnD,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC1B,QAAQ,KAAK,CAAC,QAAS,GAAE,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC3D;AACA,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAAS,cAAc,CAAC,KAAK,EAAe;AACnD;AACA,EAAE,MAAM,kBAAkB,GAA2B,EAAE;AACvD,EAAE,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,SAAA,IAAa;AAChD,IAAI,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,KAAA,IAAS;AACnD,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC1B,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC5B,UAAU,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAE,GAAE,KAAK,CAAC,QAAQ;AAC7D,eAAe,IAAI,KAAK,CAAC,QAAQ,EAAE;AACnC,UAAU,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAE,GAAE,KAAK,CAAC,QAAQ;AAC7D;AACA,QAAQ,OAAO,KAAK,CAAC,QAAQ;AAC7B;AACA,KAAK,CAAC;AACN,GAAG,CAAC;;AAEJ,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAA,KAAW,CAAC,EAAE;AACpD,IAAI;AACJ;;AAEA;AACA,EAAE,KAAK,CAAC,UAAW,GAAE,KAAK,CAAC,UAAA,IAAc,EAAE;AAC3C,EAAE,KAAK,CAAC,UAAU,CAAC,MAAO,GAAE,KAAK,CAAC,UAAU,CAAC,MAAO,IAAG,EAAE;AACzD,EAAE,MAAM,MAAO,GAAE,KAAK,CAAC,UAAU,CAAC,MAAM;AACxC,EAAE,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK;AACvE,IAAI,MAAM,CAAC,IAAI,CAAC;AAChB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,SAAS,EAAE,QAAQ;AACzB,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAAS,yBAAyB,CAAC,KAAK,EAAS,gBAAgB,EAAkB;AACnF,EAAE,IAAI,gBAAgB,CAAC,MAAO,GAAE,CAAC,EAAE;AACnC,IAAI,KAAK,CAAC,GAAI,GAAE,KAAK,CAAC,GAAA,IAAO,EAAE;AAC/B,IAAI,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,EAAE,GAAG,gBAAgB,CAAC;AACrF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,KAAK,EAAgB,KAAK,EAAU,UAAU,EAAwB;AAC9F,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI;AACf;;AAEA,EAAE,MAAM,UAAU,GAAU;AAC5B,IAAI,GAAG,KAAK;AACZ,IAAI,IAAI,KAAK,CAAC,eAAe;AAC7B,MAAM,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAE,KAAI;AAC/C,QAAQ,GAAG,CAAC;AACZ,QAAQ,IAAI,CAAC,CAAC,QAAQ;AACtB,UAAU,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;AACpD,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,QAAQ;AACtB,MAAM,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;AACpD,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,YAAY;AAC1B,MAAM,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC;AAC5D,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,SAAS;AACvB,MAAM,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC;AACtD,KAAK,CAAC;AACN,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAA,IAAS,UAAU,CAAC,QAAQ,EAAE;AACpD,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAA,GAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK;;AAEpD;AACA,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;AACnC,MAAM,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAA,GAAO,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;AAC9F;AACA;;AAEA;AACA,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE;AACnB,IAAI,UAAU,CAAC,KAAA,GAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,IAAQ;AAC/C,MAAM,OAAO;AACb,QAAQ,GAAG,IAAI;AACf,QAAQ,IAAI,IAAI,CAAC,QAAQ;AACzB,UAAU,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;AACvD,SAAS,CAAC;AACV,OAAO;AACP,KAAK,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAA,IAAS,UAAU,CAAC,QAAQ,EAAE;AACpD,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAA,GAAQ,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC;AAC9E;;AAEA,EAAE,OAAO,UAAU;AACnB;;AAEA,SAAS,aAAa,CAAC,KAAK,EAAqB,cAAc,EAAiD;AAChH,EAAE,IAAI,CAAC,cAAc,EAAE;AACvB,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,MAAM,UAAA,GAAa,KAAA,GAAQ,KAAK,CAAC,KAAK,EAAG,GAAE,IAAI,KAAK,EAAE;AACxD,EAAE,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC;AACnC,EAAE,OAAO,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACO,SAAS,8BAA8B;AAC9C,EAAE,IAAI;AACN,EAAyB;AACzB,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,SAAS;AACpB;;AAEA;AACA,EAAE,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;AACnC,IAAI,OAAO,EAAE,cAAc,EAAE,MAAM;AACnC;;AAEA,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;AAChC,IAAI,OAAO;AACX,MAAM,cAAc,EAAE,IAAI;AAC1B,KAAK;AACL;;AAEA,EAAE,OAAO,IAAI;AACb;;AAEA,SAAS,qBAAqB,CAAC,IAAI,EAAyE;AAC5G,EAAE,OAAO,gBAAgB,KAAA,IAAS,OAAO,IAAA,KAAS,UAAU;AAC5D;;AAGA,MAAM,kBAAkB,GAAoC;AAC5D,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,aAAa;AACf,EAAE,oBAAoB;AACtB,CAAE;;AAEF,SAAS,kBAAkB,CAAC,IAAI,EAAoE;AACpG,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAA,IAAO,kBAAkB,CAAC,QAAQ,CAAC,GAAA,EAA4B,CAAC;AAChG;;;;"}