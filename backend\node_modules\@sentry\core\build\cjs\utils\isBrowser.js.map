{"version": 3, "file": "isBrowser.js", "sources": ["../../../src/utils/isBrowser.ts"], "sourcesContent": ["import { isNodeEnv } from './node';\nimport { GLOBAL_OBJ } from './worldwide';\n\n/**\n * Returns true if we are in the browser.\n */\nexport function isBrowser(): boolean {\n  // eslint-disable-next-line no-restricted-globals\n  return typeof window !== 'undefined' && (!isNodeEnv() || isElectronNodeRenderer());\n}\n\ntype ElectronProcess = { type?: string };\n\n// Electron renderers with nodeIntegration enabled are detected as Node.js so we specifically test for them\nfunction isElectronNodeRenderer(): boolean {\n  const process = (GLOBAL_OBJ as typeof GLOBAL_OBJ & { process?: ElectronProcess }).process;\n  return process?.type === 'renderer';\n}\n"], "names": ["isNodeEnv", "GLOBAL_OBJ"], "mappings": ";;;;;AAGA;AACA;AACA;AACO,SAAS,SAAS,GAAY;AACrC;AACA,EAAE,OAAO,OAAO,MAAO,KAAI,gBAAgB,CAACA,cAAS,EAAG,IAAG,sBAAsB,EAAE,CAAC;AACpF;;AAIA;AACA,SAAS,sBAAsB,GAAY;AAC3C,EAAE,MAAM,OAAQ,GAAE,CAACC,oBAAW,GAAsD,OAAO;AAC3F,EAAE,OAAO,OAAO,EAAE,IAAA,KAAS,UAAU;AACrC;;;;"}