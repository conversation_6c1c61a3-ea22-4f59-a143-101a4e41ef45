{"version": 3, "file": "syncpromise.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/syncpromise.ts"], "names": [], "mappings": "AAcA,wBAAgB,mBAAmB,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACzD,wBAAgB,mBAAmB,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAclF;;;;;GAKG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,GAAG,KAAK,EAAE,MAAM,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAI3E;AAED,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI,CAAC;AAElH;;;GAGG;AACH,qBAAa,WAAW,CAAC,CAAC,CAAE,YAAW,WAAW,CAAC,CAAC,CAAC;IACnD,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,SAAS,CAA6D;IAC9E,OAAO,CAAC,MAAM,CAAM;gBAED,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAOxC,kBAAkB;IACX,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,EACxC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,EACrE,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,GACtE,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAiCnC,kBAAkB;IACX,KAAK,CAAC,OAAO,GAAG,KAAK,EAC1B,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,GACpE,WAAW,CAAC,CAAC,GAAG,OAAO,CAAC;IAI3B,kBAAkB;IACX,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC;IA+B9E,0CAA0C;IAC1C,OAAO,CAAC,gBAAgB;IAyBxB,4CAA4C;IAC5C,OAAO,CAAC,YAAY;CA+BrB"}