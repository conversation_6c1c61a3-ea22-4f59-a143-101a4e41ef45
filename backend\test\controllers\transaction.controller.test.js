const request = require('supertest');
const mongoose = require('mongoose');
const { setupTestDB, teardownTestDB, clearTestDB, generateTestToken } = require('../test-helpers');
const app = require('../../src/app');
const Transaction = require('../../src/models/transaction.model');

// Mock the cache service
jest.mock('../../src/services/cache.service', () => ({
  get: jest.fn().mockResolvedValue(null),
  set: jest.fn().mockResolvedValue(true),
  del: jest.fn().mockResolvedValue(true)
}));

describe('Transaction Controller', () => {
  let testToken;
  let testUserId;

  beforeAll(async () => {
    await setupTestDB();
    testUserId = new mongoose.Types.ObjectId();
    testToken = generateTestToken(testUserId);
  });

  afterAll(async () => {
    await teardownTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('GET /transactions', () => {
    it('should return empty array when no transactions', async () => {
      const res = await request(app)
        .get('/api/transactions')
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body).toHaveProperty('success', true);
      expect(res.body.data).toEqual([]);
    });

    it('should return user transactions', async () => {
      // Create test transactions
      const testTransaction = await Transaction.create({
        user: testUserId,
        amount: 100,
        type: 'expense',
        category: 'food',
        description: 'Test transaction'
      });

      const res = await request(app)
        .get('/api/transactions')
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.data).toHaveLength(1);
      expect(res.body.data[0]._id).toBe(testTransaction._id.toString());
    });
  });

  describe('POST /transactions', () => {
    it('should create a new transaction', async () => {
      const newTransaction = {
        amount: 150,
        type: 'income',
        category: 'salary',
        description: 'Monthly salary',
        date: new Date().toISOString()
      };

      const res = await request(app)
        .post('/api/transactions')
        .set('Authorization', `Bearer ${testToken}`)
        .send(newTransaction);
      
      expect(res.statusCode).toBe(201);
      expect(res.body).toHaveProperty('success', true);
      expect(res.body.data).toMatchObject({
        amount: newTransaction.amount,
        type: newTransaction.type,
        category: newTransaction.category,
        description: newTransaction.description
      });
    });

    it('should return 400 for invalid transaction data', async () => {
      const invalidTransaction = {
        amount: 'not-a-number',
        type: 'invalid-type',
        category: ''
      };

      const res = await request(app)
        .post('/api/transactions')
        .set('Authorization', `Bearer ${testToken}`)
        .send(invalidTransaction);
      
      expect(res.statusCode).toBe(400);
      expect(res.body).toHaveProperty('success', false);
      expect(res.body.errors).toBeDefined();
    });
  });

  describe('GET /transactions/:id', () => {
    it('should return a single transaction', async () => {
      const testTransaction = await Transaction.create({
        user: testUserId,
        amount: 200,
        type: 'expense',
        category: 'shopping',
        description: 'Test transaction'
      });

      const res = await request(app)
        .get(`/api/transactions/${testTransaction._id}`)
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.data._id).toBe(testTransaction._id.toString());
    });

    it('should return 404 for non-existent transaction', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      const res = await request(app)
        .get(`/api/transactions/${nonExistentId}`)
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(res.statusCode).toBe(404);
    });
  });

  describe('PUT /transactions/:id', () => {
    it('should update an existing transaction', async () => {
      const testTransaction = await Transaction.create({
        user: testUserId,
        amount: 100,
        type: 'expense',
        category: 'food',
        description: 'Original description'
      });

      const updates = {
        amount: 150,
        description: 'Updated description'
      };

      const res = await request(app)
        .put(`/api/transactions/${testTransaction._id}`)
        .set('Authorization', `Bearer ${testToken}`)
        .send(updates);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.data.amount).toBe(updates.amount);
      expect(res.body.data.description).toBe(updates.description);
    });
  });

  describe('DELETE /transactions/:id', () => {
    it('should delete a transaction', async () => {
      const testTransaction = await Transaction.create({
        user: testUserId,
        amount: 100,
        type: 'expense',
        category: 'food'
      });

      const res = await request(app)
        .delete(`/api/transactions/${testTransaction._id}`)
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body).toHaveProperty('success', true);

      // Verify the transaction was deleted
      const deletedTransaction = await Transaction.findById(testTransaction._id);
      expect(deletedTransaction).toBeNull();
    });
  });

  describe('GET /transactions/stats', () => {
    it('should return transaction statistics', async () => {
      // Create test transactions
      await Transaction.create([
        { user: testUserId, amount: 100, type: 'income', category: 'salary' },
        { user: testUserId, amount: 50, type: 'expense', category: 'food' },
        { user: testUserId, amount: 30, type: 'expense', category: 'transport' },
        { user: new mongoose.Types.ObjectId(), amount: 200, type: 'income', category: 'other' } // Different user
      ]);

      const res = await request(app)
        .get('/api/transactions/stats')
        .set('Authorization', `Bearer ${testToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body).toHaveProperty('data');
      expect(res.body.data).toHaveProperty('totalIncome', 100);
      expect(res.body.data).toHaveProperty('totalExpense', 80);
      expect(res.body.data).toHaveProperty('balance', 20);
    });
  });
});
