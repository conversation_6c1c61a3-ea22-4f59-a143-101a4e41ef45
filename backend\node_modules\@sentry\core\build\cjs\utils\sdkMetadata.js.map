{"version": 3, "file": "sdkMetadata.js", "sources": ["../../../src/utils/sdkMetadata.ts"], "sourcesContent": ["import type { Options } from '../types-hoist/options';\nimport { SDK_VERSION } from '../utils/version';\n\n/**\n * A builder for the SDK metadata in the options for the SDK initialization.\n *\n * Note: This function is identical to `buildMetadata` in Remix and NextJS and SvelteKit.\n * We don't extract it for bundle size reasons.\n * @see https://github.com/getsentry/sentry-javascript/pull/7404\n * @see https://github.com/getsentry/sentry-javascript/pull/4196\n *\n * If you make changes to this function consider updating the others as well.\n *\n * @param options SDK options object that gets mutated\n * @param names list of package names\n */\nexport function applySdkMetadata(options: Options, name: string, names = [name], source = 'npm'): void {\n  const metadata = options._metadata || {};\n\n  if (!metadata.sdk) {\n    metadata.sdk = {\n      name: `sentry.javascript.${name}`,\n      packages: names.map(name => ({\n        name: `${source}:@sentry/${name}`,\n        version: SDK_VERSION,\n      })),\n      version: SDK_VERSION,\n    };\n  }\n\n  options._metadata = metadata;\n}\n"], "names": ["SDK_VERSION"], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,OAAO,EAAW,IAAI,EAAU,KAAA,GAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,KAAK,EAAQ;AACvG,EAAE,MAAM,WAAW,OAAO,CAAC,SAAU,IAAG,EAAE;;AAE1C,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;AACrB,IAAI,QAAQ,CAAC,GAAA,GAAM;AACnB,MAAM,IAAI,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;AACA,MAAA,QAAA,EAAA,KAAA,CAAA,GAAA,CAAA,IAAA,KAAA;AACA,QAAA,IAAA,EAAA,CAAA,EAAA,MAAA,CAAA,SAAA,EAAA,IAAA,CAAA,CAAA;AACA,QAAA,OAAA,EAAAA,mBAAA;AACA,OAAA,CAAA,CAAA;AACA,MAAA,OAAA,EAAAA,mBAAA;AACA,KAAA;AACA;;AAEA,EAAA,OAAA,CAAA,SAAA,GAAA,QAAA;AACA;;;;"}