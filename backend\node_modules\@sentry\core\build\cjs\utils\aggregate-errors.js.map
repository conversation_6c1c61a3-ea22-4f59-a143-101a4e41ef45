{"version": 3, "file": "aggregate-errors.js", "sources": ["../../../src/utils/aggregate-errors.ts"], "sourcesContent": ["import type { ExtendedError } from '../types-hoist/error';\nimport type { Event, EventHint } from '../types-hoist/event';\nimport type { Exception } from '../types-hoist/exception';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { isInstanceOf } from './is';\n\n/**\n * Creates exceptions inside `event.exception.values` for errors that are nested on properties based on the `key` parameter.\n */\nexport function applyAggregateErrorsToEvent(\n  exceptionFromErrorImplementation: (stackParser: StackParser, ex: Error) => Exception,\n  parser: StackParser,\n  key: string,\n  limit: number,\n  event: Event,\n  hint?: EventHint,\n): void {\n  if (!event.exception?.values || !hint || !isInstanceOf(hint.originalException, Error)) {\n    return;\n  }\n\n  // Generally speaking the last item in `event.exception.values` is the exception originating from the original Error\n  const originalException: Exception | undefined =\n    event.exception.values.length > 0 ? event.exception.values[event.exception.values.length - 1] : undefined;\n\n  // We only create exception grouping if there is an exception in the event.\n  if (originalException) {\n    event.exception.values = aggregateExceptionsFromError(\n      exceptionFromErrorImplementation,\n      parser,\n      limit,\n      hint.originalException as ExtendedError,\n      key,\n      event.exception.values,\n      originalException,\n      0,\n    );\n  }\n}\n\nfunction aggregateExceptionsFromError(\n  exceptionFromErrorImplementation: (stackParser: StackParser, ex: Error) => Exception,\n  parser: StackParser,\n  limit: number,\n  error: ExtendedError,\n  key: string,\n  prevExceptions: Exception[],\n  exception: Exception,\n  exceptionId: number,\n): Exception[] {\n  if (prevExceptions.length >= limit + 1) {\n    return prevExceptions;\n  }\n\n  let newExceptions = [...prevExceptions];\n\n  // Recursively call this function in order to walk down a chain of errors\n  if (isInstanceOf(error[key], Error)) {\n    applyExceptionGroupFieldsForParentException(exception, exceptionId);\n    const newException = exceptionFromErrorImplementation(parser, error[key]);\n    const newExceptionId = newExceptions.length;\n    applyExceptionGroupFieldsForChildException(newException, key, newExceptionId, exceptionId);\n    newExceptions = aggregateExceptionsFromError(\n      exceptionFromErrorImplementation,\n      parser,\n      limit,\n      error[key],\n      key,\n      [newException, ...newExceptions],\n      newException,\n      newExceptionId,\n    );\n  }\n\n  // This will create exception grouping for AggregateErrors\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError\n  if (Array.isArray(error.errors)) {\n    error.errors.forEach((childError, i) => {\n      if (isInstanceOf(childError, Error)) {\n        applyExceptionGroupFieldsForParentException(exception, exceptionId);\n        const newException = exceptionFromErrorImplementation(parser, childError);\n        const newExceptionId = newExceptions.length;\n        applyExceptionGroupFieldsForChildException(newException, `errors[${i}]`, newExceptionId, exceptionId);\n        newExceptions = aggregateExceptionsFromError(\n          exceptionFromErrorImplementation,\n          parser,\n          limit,\n          childError,\n          key,\n          [newException, ...newExceptions],\n          newException,\n          newExceptionId,\n        );\n      }\n    });\n  }\n\n  return newExceptions;\n}\n\nfunction applyExceptionGroupFieldsForParentException(exception: Exception, exceptionId: number): void {\n  // Don't know if this default makes sense. The protocol requires us to set these values so we pick *some* default.\n  exception.mechanism = exception.mechanism || { type: 'generic', handled: true };\n\n  exception.mechanism = {\n    ...exception.mechanism,\n    ...(exception.type === 'AggregateError' && { is_exception_group: true }),\n    exception_id: exceptionId,\n  };\n}\n\nfunction applyExceptionGroupFieldsForChildException(\n  exception: Exception,\n  source: string,\n  exceptionId: number,\n  parentId: number | undefined,\n): void {\n  // Don't know if this default makes sense. The protocol requires us to set these values so we pick *some* default.\n  exception.mechanism = exception.mechanism || { type: 'generic', handled: true };\n\n  exception.mechanism = {\n    ...exception.mechanism,\n    type: 'chained',\n    source,\n    exception_id: exceptionId,\n    parent_id: parentId,\n  };\n}\n"], "names": ["isInstanceOf"], "mappings": ";;;;AAMA;AACA;AACA;AACO,SAAS,2BAA2B;AAC3C,EAAE,gCAAgC;AAClC,EAAE,MAAM;AACR,EAAE,GAAG;AACL,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAQ;AACR,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,QAAQ,CAACA,eAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAE;AACzF,IAAI;AACJ;;AAEA;AACA,EAAE,MAAM,iBAAiB;AACzB,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAA,GAAS,CAAA,GAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAA,GAAS,CAAC,CAAE,GAAE,SAAS;;AAE7G;AACA,EAAE,IAAI,iBAAiB,EAAE;AACzB,IAAI,KAAK,CAAC,SAAS,CAAC,MAAA,GAAS,4BAA4B;AACzD,MAAM,gCAAgC;AACtC,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,IAAI,CAAC,iBAAkB;AAC7B,MAAM,GAAG;AACT,MAAM,KAAK,CAAC,SAAS,CAAC,MAAM;AAC5B,MAAM,iBAAiB;AACvB,MAAM,CAAC;AACP,KAAK;AACL;AACA;;AAEA,SAAS,4BAA4B;AACrC,EAAE,gCAAgC;AAClC,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,GAAG;AACL,EAAE,cAAc;AAChB,EAAE,SAAS;AACX,EAAE,WAAW;AACb,EAAe;AACf,EAAE,IAAI,cAAc,CAAC,UAAU,KAAA,GAAQ,CAAC,EAAE;AAC1C,IAAI,OAAO,cAAc;AACzB;;AAEA,EAAE,IAAI,aAAc,GAAE,CAAC,GAAG,cAAc,CAAC;;AAEzC;AACA,EAAE,IAAIA,eAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE;AACvC,IAAI,2CAA2C,CAAC,SAAS,EAAE,WAAW,CAAC;AACvE,IAAI,MAAM,YAAa,GAAE,gCAAgC,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7E,IAAI,MAAM,cAAA,GAAiB,aAAa,CAAC,MAAM;AAC/C,IAAI,0CAA0C,CAAC,YAAY,EAAE,GAAG,EAAE,cAAc,EAAE,WAAW,CAAC;AAC9F,IAAI,aAAA,GAAgB,4BAA4B;AAChD,MAAM,gCAAgC;AACtC,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,KAAK,CAAC,GAAG,CAAC;AAChB,MAAM,GAAG;AACT,MAAM,CAAC,YAAY,EAAE,GAAG,aAAa,CAAC;AACtC,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,KAAK;AACL;;AAEA;AACA;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AACnC,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC,KAAK;AAC5C,MAAM,IAAIA,eAAY,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE;AAC3C,QAAQ,2CAA2C,CAAC,SAAS,EAAE,WAAW,CAAC;AAC3E,QAAQ,MAAM,eAAe,gCAAgC,CAAC,MAAM,EAAE,UAAU,CAAC;AACjF,QAAQ,MAAM,cAAA,GAAiB,aAAa,CAAC,MAAM;AACnD,QAAQ,0CAA0C,CAAC,YAAY,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,WAAW,CAAC;AAC7G,QAAQ,aAAA,GAAgB,4BAA4B;AACpD,UAAU,gCAAgC;AAC1C,UAAU,MAAM;AAChB,UAAU,KAAK;AACf,UAAU,UAAU;AACpB,UAAU,GAAG;AACb,UAAU,CAAC,YAAY,EAAE,GAAG,aAAa,CAAC;AAC1C,UAAU,YAAY;AACtB,UAAU,cAAc;AACxB,SAAS;AACT;AACA,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO,aAAa;AACtB;;AAEA,SAAS,2CAA2C,CAAC,SAAS,EAAa,WAAW,EAAgB;AACtG;AACA,EAAE,SAAS,CAAC,SAAA,GAAY,SAAS,CAAC,SAAU,IAAG,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;;AAEjF,EAAE,SAAS,CAAC,SAAA,GAAY;AACxB,IAAI,GAAG,SAAS,CAAC,SAAS;AAC1B,IAAI,IAAI,SAAS,CAAC,IAAK,KAAI,gBAAiB,IAAG,EAAE,kBAAkB,EAAE,IAAA,EAAM,CAAC;AAC5E,IAAI,YAAY,EAAE,WAAW;AAC7B,GAAG;AACH;;AAEA,SAAS,0CAA0C;AACnD,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAQ;AACR;AACA,EAAE,SAAS,CAAC,SAAA,GAAY,SAAS,CAAC,SAAU,IAAG,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;;AAEjF,EAAE,SAAS,CAAC,SAAA,GAAY;AACxB,IAAI,GAAG,SAAS,CAAC,SAAS;AAC1B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,MAAM;AACV,IAAI,YAAY,EAAE,WAAW;AAC7B,IAAI,SAAS,EAAE,QAAQ;AACvB,GAAG;AACH;;;;"}